[Unit]
Description=ZeroBase Client Server
After=network.target

[Service]
Type=simple
User=east
WorkingDirectory=/var/www/apps/client
Environment=NODE_ENV=production
Environment=HOST=0.0.0.0
Environment=PORT=3000
Environment=BACKEND_ENDPOINT="https://www.zerobase.dpdns.org/api"
Environment=FRONTEND_ENDPOINT="https://www.zerobase.dpdns.org"
Environment=API_BASE="https://www.zerobase.dpdns.org/api"
ExecStart=/usr/bin/node .output/server/index.mjs
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
