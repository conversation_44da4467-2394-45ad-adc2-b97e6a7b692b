课程语句导入模板说明

支持的文件格式：
1. Excel格式 (.xlsx, .xls)
2. CSV格式 (.csv)
3. 文本格式 (.txt)

## Excel格式 (.xlsx, .xls)
- 第一行为表头：中文、英文、音标（可留空）
- 从第二行开始，每行一条语句
- 示例：
  | 中文   | 英文      | 音标（可留空） |
  |--------|-----------|---------------|
  | 你好   | Hello     |               |
  | 谢谢   | Thank you |               |
  | 再见   | Goodbye   |               |

## CSV格式 (.csv)
- 第一行为表头：中文,英文,音标（可留空）
- 从第二行开始，每行一条语句，用逗号分隔
- 示例：
  ```
  中文,英文,音标（可留空）
  你好,Hello,
  谢谢,Thank you,
  再见,Goodbye,
  ```

## 文本格式 (.txt)
- 每行一条语句
- 格式：中文[分隔符]英文[分隔符]音标
- 支持的分隔符：制表符、逗号、中文顿号(、)、分号
- 可以使用#开头的行作为注释
- 示例：
  ```
  # 这是注释行
  你好,Hello,
  谢谢,Thank you,
  再见,Goodbye,
  ```

## 注意事项
1. 中文和英文列为必填项
2. 音标列可选，如果留空系统会自动生成
3. 导入时会覆盖课程的所有现有语句
4. 确保文件格式正确，否则导入可能失败 