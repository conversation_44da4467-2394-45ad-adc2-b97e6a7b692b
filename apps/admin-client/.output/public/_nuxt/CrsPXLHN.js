var j=t=>{throw TypeError(t)};var U=(t,i,r)=>i.has(t)||j("Cannot "+r);var g=(t,i,r)=>(U(t,i,"read from private field"),r?r.call(t):i.get(t)),k=(t,i,r)=>i.has(t)?j("Cannot add the same private member more than once"):i instanceof WeakSet?i.add(t):i.set(t,r);import{ae as M,r as z,af as v,y as I,ag as D,U as N,A as x,ad as B,a7 as L}from"./qi-a-hqW.js";function E(t){return typeof t=="string"?`'${t}'`:new P().serialize(t)}const P=function(){var i;class t{constructor(){k(this,i,new Map)}compare(e,n){const o=typeof e,a=typeof n;return o==="string"&&a==="string"?e.localeCompare(n):o==="number"&&a==="number"?e-n:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(n,!0))}serialize(e,n){if(e===null)return"null";switch(typeof e){case"string":return n?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const n=Object.prototype.toString.call(e);if(n!=="[object Object]")return this.serializeBuiltInType(n.length<10?`unknown:${n}`:n.slice(8,-1),e);const o=e.constructor,a=o===Object||o===void 0?"":o.name;if(a!==""&&globalThis[a]===o)return this.serializeBuiltInType(a,e);if(typeof e.toJSON=="function"){const s=e.toJSON();return a+(s!==null&&typeof s=="object"?this.$object(s):`(${this.serialize(s)})`)}return this.serializeObjectEntries(a,Object.entries(e))}serializeBuiltInType(e,n){const o=this["$"+e];if(o)return o.call(this,n);if(typeof(n==null?void 0:n.entries)=="function")return this.serializeObjectEntries(e,n.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,n){const o=Array.from(n).sort((s,c)=>this.compare(s[0],c[0]));let a=`${e}{`;for(let s=0;s<o.length;s++){const[c,l]=o[s];a+=`${this.serialize(c,!0)}:${this.serialize(l)}`,s<o.length-1&&(a+=",")}return a+"}"}$object(e){let n=g(this,i).get(e);return n===void 0&&(g(this,i).set(e,`#${g(this,i).size}`),n=this.serializeObject(e),g(this,i).set(e,n)),n}$function(e){const n=Function.prototype.toString.call(e);return n.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${n.replace(/\s*\n\s*/g,"")}`}$Array(e){let n="[";for(let o=0;o<e.length;o++)n+=this.serialize(e[o]),o<e.length-1&&(n+=",");return n+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((n,o)=>this.compare(n,o)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}i=new WeakMap;for(const r of["Error","RegExp","URL"])t.prototype["$"+r]=function(e){return`${r}(${e})`};for(const r of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])t.prototype["$"+r]=function(e){return`${r}[${e.join(",")}]`};for(const r of["BigInt64Array","BigUint64Array"])t.prototype["$"+r]=function(e){return`${r}[${e.join("n,")}${e.length>0?"n":""}]`};return t}();function F(t,i){return t===i||E(t)===E(i)}function _(t,i){if(typeof t!="string")throw new TypeError("argument str must be a string");const r={},e=i||{},n=e.decode||H;let o=0;for(;o<t.length;){const a=t.indexOf("=",o);if(a===-1)break;let s=t.indexOf(";",o);if(s===-1)s=t.length;else if(s<a){o=t.lastIndexOf(";",a-1)+1;continue}const c=t.slice(o,a).trim();if(e!=null&&e.filter&&!(e!=null&&e.filter(c))){o=s+1;continue}if(r[c]===void 0){let l=t.slice(a+1,s).trim();l.codePointAt(0)===34&&(l=l.slice(1,-1)),r[c]=J(l,n)}o=s+1}return r}function H(t){return t.includes("%")?decodeURIComponent(t):t}function J(t,i){try{return i(t)}catch{return t}}const w=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function O(t,i,r){const e=r||{},n=e.encode||encodeURIComponent;if(typeof n!="function")throw new TypeError("option encode is invalid");if(!w.test(t))throw new TypeError("argument name is invalid");const o=n(i);if(o&&!w.test(o))throw new TypeError("argument val is invalid");let a=t+"="+o;if(e.maxAge!==void 0&&e.maxAge!==null){const s=e.maxAge-0;if(Number.isNaN(s)||!Number.isFinite(s))throw new TypeError("option maxAge is invalid");a+="; Max-Age="+Math.floor(s)}if(e.domain){if(!w.test(e.domain))throw new TypeError("option domain is invalid");a+="; Domain="+e.domain}if(e.path){if(!w.test(e.path))throw new TypeError("option path is invalid");a+="; Path="+e.path}if(e.expires){if(!V(e.expires)||Number.isNaN(e.expires.valueOf()))throw new TypeError("option expires is invalid");a+="; Expires="+e.expires.toUTCString()}if(e.httpOnly&&(a+="; HttpOnly"),e.secure&&(a+="; Secure"),e.priority)switch(typeof e.priority=="string"?e.priority.toLowerCase():e.priority){case"low":{a+="; Priority=Low";break}case"medium":{a+="; Priority=Medium";break}case"high":{a+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(e.sameSite)switch(typeof e.sameSite=="string"?e.sameSite.toLowerCase():e.sameSite){case!0:{a+="; SameSite=Strict";break}case"lax":{a+="; SameSite=Lax";break}case"strict":{a+="; SameSite=Strict";break}case"none":{a+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return e.partitioned&&(a+="; Partitioned"),a}function V(t){return Object.prototype.toString.call(t)==="[object Date]"||t instanceof Date}function f(t){if(typeof t!="object")return t;var i,r,e=Object.prototype.toString.call(t);if(e==="[object Object]"){if(t.constructor!==Object&&typeof t.constructor=="function"){r=new t.constructor;for(i in t)t.hasOwnProperty(i)&&r[i]!==t[i]&&(r[i]=f(t[i]))}else{r={};for(i in t)i==="__proto__"?Object.defineProperty(r,i,{value:f(t[i]),configurable:!0,enumerable:!0,writable:!0}):r[i]=f(t[i])}return r}if(e==="[object Array]"){for(i=t.length,r=Array(i);i--;)r[i]=f(t[i]);return r}return e==="[object Set]"?(r=new Set,t.forEach(function(n){r.add(f(n))}),r):e==="[object Map]"?(r=new Map,t.forEach(function(n,o){r.set(f(o),f(n))}),r):e==="[object Date]"?new Date(+t):e==="[object RegExp]"?(r=new RegExp(t.source,t.flags),r.lastIndex=t.lastIndex,r):e==="[object DataView]"?new t.constructor(f(t.buffer)):e==="[object ArrayBuffer]"?t.slice(0):e.slice(-6)==="Array]"?new t.constructor(t):t}const q={path:"/",watch:!0,decode:t=>M(decodeURIComponent(t)),encode:t=>encodeURIComponent(typeof t=="string"?t:JSON.stringify(t))},b=window.cookieStore;function X(t,i){var l;const r={...q,...i};r.filter??(r.filter=u=>u===t);const e=C(r)||{};let n;r.maxAge!==void 0?n=r.maxAge*1e3:r.expires&&(n=r.expires.getTime()-Date.now());const o=n!==void 0&&n<=0,a=o||e[t]===void 0||e[t]===null,s=f(o?void 0:e[t]??((l=r.default)==null?void 0:l.call(r))),c=n&&!o?K(s,n,r.watch&&r.watch!=="shallow"):z(s);{let u=null;try{!b&&typeof BroadcastChannel<"u"&&(u=new BroadcastChannel(`nuxt:cookies:${t}`))}catch{}const d=(p=!1)=>{!p&&(r.readonly||F(c.value,e[t]))||(G(t,c.value,r),e[t]=f(c.value),u==null||u.postMessage({value:r.encode(c.value)}))},$=p=>{var y;const h=p.refresh?(y=C(r))==null?void 0:y[t]:r.decode(p.value);m=!0,c.value=h,e[t]=f(h),x(()=>{m=!1})};let m=!1;const A=!!D();if(A&&v(()=>{m=!0,d(),u==null||u.close()}),b){const p=h=>{const y=h.changed.find(S=>S.name===t),R=h.deleted.find(S=>S.name===t);y&&$({value:y.value}),R&&$({value:null})};b.addEventListener("change",p),A&&v(()=>b.removeEventListener("change",p))}else u&&(u.onmessage=({data:p})=>$(p));r.watch&&I(c,()=>{m||d()},{deep:r.watch!=="shallow"}),a&&d(a)}return c}function C(t={}){return _(document.cookie,t)}function Y(t,i,r={}){return i==null?O(t,i,{...r,maxAge:-1}):O(t,i,r)}function G(t,i,r={}){document.cookie=Y(t,i,r)}const T=2147483647;function K(t,i,r){let e,n,o=0;const a=r?z(t):{value:t};return D()&&v(()=>{n==null||n(),clearTimeout(e)}),N((s,c)=>{r&&(n=I(a,c));function l(){o=0,clearTimeout(e);const u=i-o,d=u<T?u:T;e=setTimeout(()=>{if(o+=d,o<i)return l();a.value=void 0,c()},d)}return{get(){return s(),a.value},set(u){l(),a.value=u,c()}}})}const W=B((t,i)=>{if(X("token").value)return L("/dashboard",{replace:!0})});export{W as default};
