import{p as R,B as N,f as U,r as f,C as B,z as F,g as q,c,o as d,b as z,a as o,D as A,t as m,h as C,F as P,j,i as W,n as X,l as G}from"./qi-a-hqW.js";import{a as J}from"./CxzfvvNm.js";import{A as K}from"./oUsj6fiR.js";import{s as Q}from"./x_rD_Ya3.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const Y={class:"flex"},Z={class:"flex-1 p-6"},ee={class:"flex justify-between items-center mb-6"},te={class:"flex space-x-3"},oe={key:0,class:"text-center py-10"},ne={key:1,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"},se={key:2},ae={class:"bg-white shadow-md rounded p-6 mb-6"},re={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ie={class:"mt-1 text-lg"},le={class:"mt-1"},ce={class:"mt-1 flex flex-wrap gap-2"},de={key:0,class:"text-gray-500"},ge={class:"mt-1"},ue={class:"bg-white shadow-md rounded p-6 mb-6"},pe={class:"flex items-center space-x-4"},me={class:"bg-white shadow-md rounded p-6"},he=["innerHTML"],ye={key:0,class:"text-gray-500 mt-2"},$e={__name:"id",setup(fe){const v=R(),$=N(),g=U(),x=$.params.id,b=f(!0),u=f(""),h=f(!1),S=f("1"),w=f(!1),E=()=>{if(!window.speechSynthesis)return console.error("浏览器不支持语音合成功能"),!1;try{const t=navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1,e=navigator.userAgent.indexOf("Edge")>-1||navigator.userAgent.indexOf("Edg")>-1,r=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);console.log("浏览器环境:",{isChrome:t,isEdge:e,isSafari:r});const p=window.speechSynthesis.getVoices();if(p.length===0)if((t||e)&&"onvoiceschanged"in window.speechSynthesis)console.log("使用onvoiceschanged事件加载语音"),window.speechSynthesis.onvoiceschanged=()=>{const l=window.speechSynthesis.getVoices();console.log("语音列表已加载，可用语音数量:",l.length),w.value=!0};else{console.log("使用轮询方式加载语音");let l=0;const a=10,i=()=>{l++;const y=window.speechSynthesis.getVoices();if(y.length>0){console.log(`语音列表已加载，可用语音数量: ${y.length}`),w.value=!0;return}l<a?(console.log(`尝试加载语音列表 (${l}/${a})`),setTimeout(i,200)):(console.warn("无法加载语音列表，将使用默认语音"),w.value=!0)};i()}else console.log("语音列表已立即加载，可用语音数量:",p.length),w.value=!0;const s=new SpeechSynthesisUtterance("");return s.volume=0,s.onend=()=>{console.log("语音合成引擎初始化完成")},s.onerror=l=>{console.error("语音合成引擎初始化失败:",l)},t&&window.speechSynthesis.cancel(),window.speechSynthesis.speak(s),!0}catch(t){return console.error("初始化语音合成引擎失败:",t),!1}},n=B({id:"",title:"",type:"",body:"",categories:[],annotations:null,createdAt:"",updatedAt:""}),_=F(()=>{if(!n.body)return"";if(n.annotations&&Array.isArray(n.annotations)&&n.annotations.length>0){console.log("处理注解，共有注解数量:",n.annotations.length);const t=document.createElement("div");t.innerHTML=n.body;const e=t.textContent||t.innerText;console.log("提取的纯文本内容长度:",e.length);let r=n.body;[...n.annotations].sort((a,i)=>i.word.length-a.word.length).forEach(a=>{try{const{word:i,pronunciation:y}=a;console.log(`处理单词: "${i}", 音标: "${y}"`);const V=i.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),D=`<span class="pronunciation-word" data-pronunciation="${y.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}">${V}</span>`,H=new RegExp(`\\b${i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"g");r=r.replace(H,D)}catch(i){console.error(`处理单词 ${a.word} 时出错:`,i)}});const s=document.createElement("div");return s.innerHTML=r,s.querySelectorAll("span[data-pronunciation]").forEach(a=>{a.classList.contains("pronunciation-word")||(a.textContent,a.getAttribute("data-pronunciation"),a.classList.add("pronunciation-word"))}),console.log("处理完成，返回新的HTML内容"),s.innerHTML}return n.body}),T=t=>{if(t.target.classList.contains("pronunciation-word")||t.target.classList.contains("has-pronunciation")){const e=t.target.getAttribute("data-pronunciation"),r=t.target.textContent;e&&L(r,e)}},L=(t,e)=>{if(!window.speechSynthesis){g.warning("您的浏览器不支持语音合成功能");return}const r=navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1,p=navigator.userAgent.indexOf("Edge")>-1||navigator.userAgent.indexOf("Edg")>-1;console.log("发音请求 - 浏览器环境:",{isChrome:r,isEdge:p,word:t,pronunciation:e}),window.speechSynthesis.cancel();const s=new SpeechSynthesisUtterance(t);s.lang="en-US",s.rate=parseFloat(S.value),s.pitch=1,s.volume=1,s.onerror=i=>{console.error("语音合成错误:",i),g.error(`发音失败: ${i.error||"未知错误"}`)};let l=!1,a=null;s.onend=()=>{console.log("语音播放结束"),l=!0,a&&(clearInterval(a),a=null)},r?(console.log("使用Chrome浏览器兼容模式"),(window.speechSynthesis.speaking||window.speechSynthesis.pending)&&window.speechSynthesis.cancel(),setTimeout(()=>{s.onstart=()=>{console.log("语音播放开始"),a=Q(()=>{if(l)a&&(clearInterval(a),a=null);else{if(!window.speechSynthesis.speaking)return;console.log("保持语音活跃"),window.speechSynthesis.pause(),window.speechSynthesis.resume()}},5e3)},window.speechSynthesis.speak(s),g.info(`正在朗读: ${t} [${e}]`,{timeout:2e3})},100)):(s.onstart=()=>{console.log("语音播放开始")},window.speechSynthesis.speak(s),g.info(`正在朗读: ${t} [${e}]`,{timeout:2e3}))},I=()=>{h.value=!h.value,g.info(`自动发音已${h.value?"开启":"关闭"}`)},k=async()=>{b.value=!0,u.value="";try{console.log(`尝试获取内容详情，ID: ${x}`);const t=localStorage.getItem("adminToken");if(console.log("当前Token:",t?`${t.substring(0,15)}...`:"未找到"),!t){u.value="未找到认证令牌，请重新登录",g.error(u.value),v.push("/admin/login");return}const e=await J.get(`/api/admin/content/${x}`);console.log("API返回内容:",e),n.id=e.id,n.title=e.title||"",n.type=e.type||"",n.body=e.body||"",n.categories=Array.isArray(e.categories)?[...e.categories]:[],n.annotations=e.annotations||[],n.createdAt=e.createdAt,n.updatedAt=e.updatedAt}catch(t){console.error("获取内容详情失败，完整错误:",t),u.value=t.message||"获取内容详情失败",g.error(`获取内容失败: ${u.value}`),t.message&&(t.message.includes("认证失败")||t.message.includes("未提供认证令牌")||t.message.includes("无效的认证令牌"))&&(console.log("检测到认证错误，准备重定向到登录页"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),v.push("/admin/login"))}finally{b.value=!1}},M=t=>({ARTICLE:"文章",LESSON:"课程",EXERCISE:"练习",NOTE:"笔记"})[t]||t,O=t=>t?new Date(t).toLocaleString():"未知";return q(()=>{k(),E()}),(t,e)=>(d(),c("div",Y,[z(K),o("div",Z,[o("div",ee,[e[3]||(e[3]=o("h1",{class:"text-2xl font-bold"},"内容预览",-1)),o("div",te,[o("button",{onClick:e[0]||(e[0]=r=>A(v).push(`/admin/content/edit/${A(x)}`)),class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"}," 编辑 "),o("button",{onClick:e[1]||(e[1]=r=>A(v).push("/admin/content")),class:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"}," 返回列表 ")])]),b.value?(d(),c("div",oe,e[4]||(e[4]=[o("p",null,"加载中...",-1)]))):u.value?(d(),c("div",ne,[e[5]||(e[5]=o("p",{class:"font-bold"},"加载失败:",-1)),o("p",null,m(u.value),1),o("button",{onClick:k,class:"mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"}," 重试 ")])):(d(),c("div",se,[o("div",ae,[o("div",re,[o("div",null,[e[6]||(e[6]=o("h3",{class:"text-sm font-medium text-gray-500"},"标题",-1)),o("p",ie,m(n.title||"无标题"),1)]),o("div",null,[e[7]||(e[7]=o("h3",{class:"text-sm font-medium text-gray-500"},"类型",-1)),o("p",le,m(M(n.type)),1)]),o("div",null,[e[8]||(e[8]=o("h3",{class:"text-sm font-medium text-gray-500"},"栏目",-1)),o("div",ce,[(d(!0),c(P,null,j(n.categories,(r,p)=>(d(),c("span",{key:p,class:"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"},m(r),1))),128)),!n.categories||n.categories.length===0?(d(),c("span",de," 无栏目 ")):C("",!0)])]),o("div",null,[e[9]||(e[9]=o("h3",{class:"text-sm font-medium text-gray-500"},"创建时间",-1)),o("p",ge,m(O(n.createdAt)),1)])])]),o("div",ue,[e[11]||(e[11]=o("h2",{class:"text-lg font-medium text-gray-700 mb-4"},"发音控制",-1)),o("div",pe,[o("button",{onClick:I,class:X(["px-4 py-2 rounded",h.value?"bg-green-500 hover:bg-green-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"])},m(h.value?"自动发音已开启":"自动发音已关闭"),3),W(o("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>S.value=r),class:"border rounded px-3 py-2"},e[10]||(e[10]=[o("option",{value:"0.8"},"慢速 (0.8x)",-1),o("option",{value:"1"},"正常 (1.0x)",-1),o("option",{value:"1.2"},"快速 (1.2x)",-1)]),512),[[G,S.value]])])]),o("div",me,[e[12]||(e[12]=o("h2",{class:"text-lg font-medium text-gray-700 mb-4"},"内容正文",-1)),o("div",{class:"prose max-w-none quill-content",innerHTML:_.value,onClick:T},null,8,he),!n.body||n.body.trim()===""?(d(),c("pre",ye,`            无内容
          `)):C("",!0)])]))])]))}};export{$e as default};
