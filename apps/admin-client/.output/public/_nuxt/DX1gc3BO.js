import{A as K}from"./oUsj6fiR.js";import{e as q,r as c,z as G,g as J,c as n,o as r,b as N,a as s,I as T,h as p,t as i,d as m,i as L,v as O,J as Q,F as I,j as P,n as H,w as W,T as X}from"./qi-a-hqW.js";import{_ as Y,a as Z,b as ee}from"./DgOJsROy.js";import{a as j}from"./CxzfvvNm.js";import{_ as se}from"./DlAUqK2U.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./D0FjqKqG.js";import"./x_rD_Ya3.js";const te={class:"min-h-screen bg-gray-100"},oe={class:"ml-64"},le={class:"flex-1 p-8"},re={class:"w-full max-w-6xl mx-auto"},ne={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"},ae={class:"mb-6 text-right"},ue={class:"bg-gray-50 p-4 rounded-lg shadow mb-6 flex flex-wrap gap-4 items-center"},ie={key:1,class:"bg-white p-4 rounded-lg shadow mb-6"},de={key:0,class:"text-center py-4"},ce={key:1,class:"text-center py-4"},ve={key:2,class:"border rounded-lg overflow-hidden"},be={class:"bg-gray-50 p-3 font-bold border-b"},fe={class:"p-4"},pe={class:"mb-2"},me={class:"mb-2"},ye={key:3},he={class:"mb-3"},ge={class:"list-disc pl-5 mb-4"},xe=["onClick"],_e={key:2,class:"w-full flex justify-center py-12"},ke={class:"flex gap-8"},we={class:"w-64 min-h-[600px] bg-white border-r border-gray-200 rounded-lg shadow-sm p-4 flex-shrink-0"},Ce={key:0,class:"text-gray-400 text-center py-8"},Ae=["onClick"],$e={class:"text-gray-500"},Te={key:0,class:"pl-2 border-l-2 border-gray-100 ml-2"},Ie=["onClick"],Pe={class:"flex space-x-2"},je=["onClick"],Be=["onClick"],Me={key:1,class:"text-sm text-gray-400 italic pl-2 py-1"},Se={class:"flex-1 min-h-[600px] bg-white rounded-lg shadow-lg p-8"},Ve={key:1,class:"text-gray-400 text-center py-32"},Ne=q({__name:"courses",setup(Re){const a=c([]),k=c(null),x=c(!1),w=c(!1),C=c(null),f=c(""),y=c(!1),b=c(""),v=c([]),_=c(!1),d=c([]),A=c(!1),R=G(()=>{const t={};return d.value.forEach(e=>{const l=e.courseId;t[l]||(t[l]={courseId:e.courseId,courseTitle:e.courseTitle,coursePackId:e.coursePackId,coursePackTitle:e.coursePackTitle,count:0}),t[l].count++}),Object.values(t)}),$=async()=>{y.value=!0,f.value="";try{console.log("开始获取课程包数据...");const t=await j.get("/api/admin/course-packages");a.value=t&&t.packages?t.packages:Array.isArray(t)?t:[],console.log("获取到课程包数据:",a.value.length),a.value.length>0&&v.value.length===0&&(v.value=[a.value[0].id])}catch(t){console.error("获取课程包出错:",t),f.value="获取课程包失败，请刷新页面重试"}finally{y.value=!1}},B=async()=>{if(b.value.trim()){_.value=!0,A.value=!0,d.value=[];try{console.log("搜索课程内容:",b.value);const t=await j.get(`/api/admin/search?query=${encodeURIComponent(b.value)}`);t&&Array.isArray(t)&&(d.value=t.map(e=>{const l=a.value&&a.value.length>0?a.value.find(g=>g.courses&&Array.isArray(g.courses)&&g.courses.some(D=>D.id===e.courseId)):null,o=(l==null?void 0:l.title)||"未知课程包",u=l!=null&&l.courses&&Array.isArray(l.courses)?l.courses.find(g=>g.id===e.courseId):null,h=(u==null?void 0:u.title)||"未知课程";return{...e,coursePackTitle:o,courseTitle:h,coursePackId:l==null?void 0:l.id}}))}catch(t){console.error("搜索内容失败:",t),f.value="搜索内容失败，请重试"}finally{_.value=!1}}},E=()=>{A.value=!1,d.value=[],b.value=""},M=t=>{let e=null;if(a.value&&Array.isArray(a.value)){for(const l of a.value)if(l&&l.courses&&Array.isArray(l.courses)){const o=l.courses.find(u=>u&&u.id===t);if(o){v.value.includes(l.id)||v.value.push(l.id),e=o;break}}}e&&S(e)},S=t=>{console.log("📚 选择课程:",t.title),k.value=t},F=t=>{C.value=t,w.value=!0},U=async t=>{if(confirm("确定删除该课程？此操作不可恢复。")){y.value=!0,f.value="";try{console.log("删除课程:",t.id),await j.delete(`/api/admin/courses/${t.id}`),await $()}catch(e){console.error("删除课程失败:",e),f.value="删除课程失败，请重试"}finally{y.value=!1}}},V=()=>$(),z=t=>{v.value.includes(t)?v.value=v.value.filter(e=>e!==t):v.value.push(t)};return J($),(t,e)=>{const l=K;return r(),n("div",te,[N(l),s("div",oe,[s("div",le,[s("div",re,[e[15]||(e[15]=s("h1",{class:"text-2xl font-bold mb-8 text-center"},"课程管理",-1)),f.value?(r(),n("div",ne,[s("p",null,i(f.value),1)])):p("",!0),s("div",ae,[s("button",{onClick:e[0]||(e[0]=o=>x.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg shadow transition"},e[6]||(e[6]=[s("span",{class:"mr-2"},"+",-1),m(" 创建新课程 ",-1)]))]),s("div",ue,[L(s("input",{"onUpdate:modelValue":e[1]||(e[1]=o=>b.value=o),type:"text",placeholder:"搜索课程、语句...",class:"flex-1 min-w-[200px] px-4 py-2 border rounded-lg shadow-sm focus:ring focus:border-blue-300",onKeyup:Q(B,["enter"])},null,544),[[O,b.value]]),s("button",{onClick:B,class:"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg shadow transition"}," 搜索 ")]),A.value?(r(),n("div",ie,[s("div",{class:"flex justify-between items-center mb-4"},[e[8]||(e[8]=s("h3",{class:"text-lg font-bold"},"搜索结果",-1)),s("button",{onClick:E,class:"text-gray-500 hover:text-gray-700"},e[7]||(e[7]=[s("span",{class:"mr-1"},"×",-1),m(" 关闭 ",-1)]))]),_.value?(r(),n("div",de,e[9]||(e[9]=[s("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"},null,-1),s("p",{class:"mt-2"},"搜索中...",-1)]))):d.value.length===0?(r(),n("div",ce,[s("p",null,'未找到与"'+i(b.value)+'"相关的内容',1)])):d.value.length===1?(r(),n("div",ve,[s("div",be,i(d.value[0].coursePackTitle)+" - "+i(d.value[0].courseTitle),1),s("div",fe,[s("div",pe,[e[10]||(e[10]=s("span",{class:"font-bold"},"中文：",-1)),m(i(d.value[0].chinese),1)]),s("div",me,[e[11]||(e[11]=s("span",{class:"font-bold"},"英文：",-1)),m(i(d.value[0].english),1)]),s("div",null,[e[12]||(e[12]=s("span",{class:"font-bold"},"音标：",-1)),m(i(d.value[0].soundmark),1)]),s("button",{onClick:e[2]||(e[2]=o=>M(d.value[0].courseId)),class:"mt-3 bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-3 rounded text-sm"}," 查看课程 ")])])):(r(),n("div",ye,[s("p",he,'对"'+i(b.value)+'"的搜索结果有 '+i(d.value.length)+" 个，分别在：",1),s("ul",ge,[(r(!0),n(I,null,P(R.value,(o,u)=>(r(),n("li",{key:u,class:"mb-1"},[m(i(o.coursePackTitle)+" - "+i(o.courseTitle)+" ("+i(o.count)+"个结果) ",1),s("button",{onClick:h=>M(o.courseId),class:"ml-2 bg-blue-100 hover:bg-blue-200 text-blue-700 py-0.5 px-2 rounded text-xs"}," 查看课程 ",8,xe)]))),128))])]))])):p("",!0),y.value&&!_.value?(r(),n("div",_e,e[13]||(e[13]=[s("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):p("",!0),s("div",ke,[s("aside",we,[!a.value||a.value.length===0?(r(),n("div",Ce," 暂无课程包数据 ")):(r(!0),n(I,{key:1},P(a.value,o=>(r(),n("div",{key:o.id,class:"mb-6"},[s("div",{onClick:u=>z(o.id),class:"font-bold text-gray-700 mb-2 flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"},[s("span",null,i(o.title||"未命名课程包"),1),s("span",$e,[(r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:H(["h-4 w-4 transition-transform",v.value.includes(o.id)?"transform rotate-180":""]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},e[14]||(e[14]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))])],8,Ae),N(X,{name:"slide"},{default:W(()=>[v.value.includes(o.id)?(r(),n("ul",Te,[o.courses&&Array.isArray(o.courses)&&o.courses.length>0?(r(!0),n(I,{key:0},P(o.courses,u=>(r(),n("li",{key:u.id,class:"pl-2 flex items-center justify-between hover:bg-blue-50 rounded transition mb-1 py-1"},[s("span",{onClick:h=>S(u),class:"cursor-pointer hover:text-blue-600 flex-1"},i(u.title||"未命名课程"),9,Ie),s("div",Pe,[s("button",{class:"text-xs text-blue-500 hover:underline",onClick:h=>F(u)},"编辑信息",8,je),s("button",{class:"text-xs text-red-500 hover:underline",onClick:h=>U(u)},"删除",8,Be)])]))),128)):(r(),n("li",Me," 无课程 "))])):p("",!0)]),_:2},1024)]))),128)),s("button",{class:"mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg shadow",onClick:e[3]||(e[3]=o=>x.value=!0)},"创建新课程")]),s("main",Se,[k.value?(r(),T(Y,{key:0,course:k.value,coursePacks:a.value},null,8,["course","coursePacks"])):(r(),n("div",Ve,"请选择左侧课程查看详情"))])])])])]),x.value?(r(),T(Z,{key:0,onClose:e[4]||(e[4]=o=>x.value=!1),onCreated:V,coursePacks:a.value},null,8,["coursePacks"])):p("",!0),w.value&&C.value?(r(),T(ee,{key:1,onClose:e[5]||(e[5]=o=>w.value=!1),onUpdated:V,course:C.value},null,8,["course"])):p("",!0)])}}}),Le=se(Ne,[["__scopeId","data-v-817f246d"]]);export{Le as default};
