const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./qi-a-hqW.js","./entry.DXiz2RTu.css"])))=>i.map(i=>d[i]);
var Se=Object.defineProperty;var Ce=(G,c,o)=>c in G?Se(G,c,{enumerable:!0,configurable:!0,writable:!0,value:o}):G[c]=o;var H=(G,c,o)=>Ce(G,typeof c!="symbol"?c+"":c,o);import{e as X,f as ye,r as S,g as ne,c as p,o as v,a as e,i as z,v as Z,t as _,K as de,z as O,h as T,D as N,F as Q,j as W,y as V,b as ce,n as le,_ as Fe,d as xe,C as pe,k as he,L as Ue,m as ie,M as Me,I as re,s as Ee,l as Ge}from"./qi-a-hqW.js";import{a as j}from"./CxzfvvNm.js";import{_ as me}from"./DlAUqK2U.js";import{L as Ae}from"./D0FjqKqG.js";import{s as Ie}from"./x_rD_Ya3.js";const Te={class:"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"},Le={class:"bg-white p-6 rounded-lg shadow-lg w-96"},Pe={class:"mb-4"},Re={class:"mb-4"},je={key:0,class:"mb-2"},Be=["src"],De={key:1,class:"mb-2"},ze=["src"],Ve={key:2,class:"w-full h-40 bg-gray-200 flex items-center justify-center rounded mb-2"},Oe={class:"mb-4"},qe={class:"flex justify-end space-x-2"},He=["disabled"],Ke=X({__name:"CoverModal",props:{course:{}},emits:["close","updated"],setup(G,{emit:c}){const o=ye(),l=G,n=c,d=S(""),m=S(null),g=S(!1),h=S("");ne(()=>{l.course&&l.course.title&&(h.value=l.course.title)});const b=C=>{const f=C.target.files[0];if(!f)return;m.value=f;const U=new FileReader;U.onload=$=>{d.value=$.target.result},U.readAsDataURL(f)},F=async C=>{const f=new FormData;f.append("file",C);const U=localStorage.getItem("adminToken")||sessionStorage.getItem("adminToken"),$=de().public.apiBaseUrl||"http://localhost:5000",L=U?U.startsWith("Bearer ")?U:`Bearer ${U}`:"";try{const E=await fetch(`${$}/api/admin/upload`,{method:"POST",headers:L?{Authorization:L}:{},body:f});if(!E.ok){const i=await E.json();throw new Error(i.message||"文件上传失败")}return await E.json()}catch(E){throw console.error("文件上传错误:",E),o.error(E instanceof Error?E.message:"文件上传失败"),E}},k=async()=>{g.value=!0;try{const C={};if(h.value!==l.course.title&&(C.title=h.value),m.value){console.log("上传新封面图片...");try{const f=await F(m.value);console.log("文件上传成功，获取到URL:",f.url),C.cover=f.url,l.course.cover=f.url;const U=`course_cover_${l.course.id}`;localStorage.setItem(U,f.url)}catch{o.error("封面图片上传失败，请重试"),g.value=!1;return}}if(Object.keys(C).length>0){console.log("更新课程信息:",C);const f=localStorage.getItem("adminToken")||sessionStorage.getItem("adminToken"),U=de().public.apiBaseUrl||"http://localhost:5000",$=f?f.startsWith("Bearer ")?f:`Bearer ${f}`:"",L={"Content-Type":"application/json"};$&&(L.Authorization=$);const E=await fetch(`${U}/api/admin/courses/${l.course.id}`,{method:"PUT",headers:L,body:JSON.stringify(C)});if(!E.ok){const i=await E.json();throw new Error(i.message||"更新课程信息失败")}console.log("课程信息更新成功"),o.success("课程信息更新成功"),n("updated")}n("close")}catch(C){console.error("保存课程信息失败:",C),o.error(C instanceof Error?C.message:"保存课程信息失败")}finally{g.value=!1}};return(C,f)=>(v(),p("div",Te,[e("div",Le,[f[5]||(f[5]=e("h2",{class:"text-lg font-bold mb-4"},"编辑课程信息",-1)),e("div",Pe,[f[2]||(f[2]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"课程标题",-1)),z(e("input",{"onUpdate:modelValue":f[0]||(f[0]=U=>h.value=U),type:"text",placeholder:"输入课程标题",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[Z,h.value]])]),e("div",Re,[d.value?(v(),p("div",je,[e("img",{src:d.value,class:"w-full h-40 object-cover rounded"},null,8,Be)])):C.course.cover?(v(),p("div",De,[e("img",{src:C.course.cover,class:"w-full h-40 object-cover rounded"},null,8,ze)])):(v(),p("div",Ve,f[3]||(f[3]=[e("span",{class:"text-gray-500"},"无封面",-1)])))]),e("div",Oe,[f[4]||(f[4]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"选择图片",-1)),e("input",{type:"file",accept:"image/*",onChange:b,class:"w-full"},null,32)]),e("div",qe,[e("button",{onClick:f[1]||(f[1]=U=>C.$emit("close")),class:"btn"},"取消"),e("button",{onClick:k,class:"btn btn-primary",disabled:g.value},_(g.value?"上传中...":"保存"),9,He)])])]))}}),Ne={class:"audio-reuse-info"},Qe={key:0,class:"text-purple-600 text-sm"},We={class:"flex items-center mb-1"},Ye={key:0,class:"mt-2 space-y-1 text-xs"},Je={class:"w-10"},Ze=["src"],Xe={key:1,class:"text-gray-400"},et={class:"mt-1 text-xs text-gray-500"},tt={__name:"AudioReuseInfo",props:{statement:{type:Object,required:!0},reuseInfo:{type:Object,default:()=>({reused:!1,audioFiles:{},source:null})}},setup(G){const c=G,o=S(!1),l=O(()=>c.reuseInfo.reused),n=O(()=>c.reuseInfo.source),d=O(()=>c.reuseInfo.audioFiles||{}),m=k=>({enUsMale:"美男",enUsFemale:"美女",enGbMale:"英男",enGbFemale:"英女"})[k]||k,g=k=>{if(!k)return"";if(k.startsWith("http"))return k;const C=`http://localhost:5000${k}`;return console.log("🎵 音频URL:",C),C},h=k=>{console.error("🎵 音频加载错误:",k.target.src,k)},b=k=>{console.log("🎵 开始加载音频:",k.target.src)},F=k=>{console.log("🎵 音频可以播放:",k.target.src)};return(k,C)=>(v(),p("div",Ne,[N(l)?(v(),p("div",Qe,[e("div",We,[C[1]||(C[1]=e("span",{class:"mr-1"},"🔄",-1)),e("span",null,"复用音频 (来源: "+_(N(n).courseTitle)+")",1)]),o.value?(v(),p("div",Ye,[(v(!0),p(Q,null,W(N(d),(f,U)=>(v(),p("div",{key:U,class:"flex items-center"},[e("span",Je,_(m(U))+":",1),f?(v(),p("audio",{key:0,src:g(f),controls:"",preload:"metadata",class:"h-8 w-32",style:{"min-height":"32px","min-width":"128px"},onError:h,onLoadstart:b,onCanplay:F},null,40,Ze)):(v(),p("span",Xe,"无音频"))]))),128)),e("div",et,[e("span",null,"来源语句ID: "+_(N(n).id),1)])])):T("",!0),e("button",{onClick:C[0]||(C[0]=f=>o.value=!o.value),class:"text-xs text-blue-500 hover:underline mt-1"},_(o.value?"隐藏详情":"查看详情"),1)])):T("",!0)]))}},$e=me(tt,[["__scopeId","data-v-a74abc8f"]]),st={class:"simple-audio-controls"},lt={class:"flex items-center space-x-2 mb-1"},at={key:0,class:"space-y-1 mb-2 p-2 bg-gray-50 rounded"},nt={class:"text-xs text-gray-600 w-12"},ot=["src","onLoadedmetadata"],it={key:1,class:"text-xs text-gray-400 flex-1"},rt={class:"flex space-x-1"},ut=["disabled"],dt=X({__name:"SimpleAudioControls",props:{statement:{},courseId:{}},emits:["audio-generated"],setup(G,{emit:c}){const o=G,l=c,n=S(!1);S(!1);const d=S(!1),m=S(o.statement.audioStatus||"pending"),g=S({enUsMale:o.statement.audioEnUsMale||null,enUsFemale:o.statement.audioEnUsFemale||null,enGbMale:o.statement.audioEnGbMale||null,enGbFemale:o.statement.audioEnGbFemale||null}),h=$=>{switch($){case"completed":return"已生成";case"generating":return"生成中";case"failed":return"生成失败";default:return"未生成"}},b=$=>{switch($){case"completed":return"text-green-600";case"generating":return"text-blue-600";case"failed":return"text-red-600";default:return"text-gray-600"}},F=$=>({enUsMale:"美男",enUsFemale:"美女",enGbMale:"英男",enGbFemale:"英女"})[$]||$,k=($,L)=>{const E=L.target,i=E.duration,s=i>0?"✅":"❌";console.log(`🎵 ${F($)} ${s} 时长: ${i}秒 (readyState: ${E.readyState})`),(i===0||!isFinite(i))&&(console.log(`⚠️  ${F($)} 时长异常，检查文件...`),fetch(E.src,{method:"HEAD"}).then(t=>{const a=t.headers.get("content-length");console.log(`📁 ${F($)} 文件: ${t.status} | ${a}字节 | ${t.headers.get("content-type")}`)}).catch(t=>{console.error(`❌ ${F($)} 文件访问失败:`,t.message)}))},C=async()=>{if(!(n.value||!o.statement.english)){n.value=!0,m.value="generating";try{const $=await j.post("/api/admin/audio/statement/generate",{statementId:o.statement.id,voiceTypes:["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"],regenerate:!0});$.statement&&(m.value=$.statement.audioStatus,$.statement.audioFiles&&(g.value={enUsMale:$.statement.audioFiles.enUsMale||null,enUsFemale:$.statement.audioFiles.enUsFemale||null,enGbMale:$.statement.audioFiles.enGbMale||null,enGbFemale:$.statement.audioFiles.enGbFemale||null}),l("audio-generated",$.statement),m.value==="completed"&&(d.value=!0))}catch($){console.error("生成音频失败:",$),m.value="failed",alert("生成音频失败，请重试")}finally{n.value=!1}}},f=async()=>{if(confirm("确定要删除这个语句的所有音频吗？"))try{await j.delete(`/api/admin/audio/statement/${o.statement.id}`),m.value="pending",g.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},d.value=!1,l("updated",{...o.statement,audioFiles:null,audioStatus:"pending",audioEnUsMale:null,audioEnUsFemale:null,audioEnGbMale:null,audioEnGbFemale:null,audioGeneratedAt:null}),alert("音频删除成功")}catch($){console.error("删除音频失败:",$),alert("删除音频失败，请重试")}},U=()=>{o.statement&&(m.value=o.statement.audioStatus||"pending",o.statement.audioStatus==="completed"&&(g.value={enUsMale:o.statement.audioEnUsMale||null,enUsFemale:o.statement.audioEnUsFemale||null,enGbMale:o.statement.audioEnGbMale||null,enGbFemale:o.statement.audioEnGbFemale||null},console.log("🎵 音频文件状态:"),Object.entries(g.value).forEach(([$,L])=>{console.log(`  ${F($)}: ${L?"✅ 有文件":"❌ 无文件"} ${L||""}`)}),d.value=!0))};return V(()=>o.statement,$=>{$&&U()},{immediate:!0,deep:!0}),V(()=>o.statement.english,$=>{$||(m.value="pending",g.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},d.value=!1)}),ne(()=>{console.log("🔧 SimpleAudioControls 组件挂载"),U()}),($,L)=>(v(),p("div",st,[e("div",lt,[e("span",{class:le(["text-xs",b(m.value)])},_(h(m.value)),3),m.value==="completed"?(v(),p("button",{key:0,onClick:L[0]||(L[0]=E=>d.value=!d.value),class:"btn btn-xs btn-outline"},_(d.value?"隐藏":"试听"),1)):T("",!0)]),ce($e,{statement:$.statement},null,8,["statement"]),d.value&&m.value==="completed"?(v(),p("div",at,[(v(!0),p(Q,null,W(g.value,(E,i)=>(v(),p("div",{key:i,class:"flex items-center space-x-2"},[e("span",nt,_(F(i)),1),E?(v(),p("audio",{key:0,src:E,controls:"",preload:"metadata",class:"h-6 text-xs flex-1",onLoadedmetadata:s=>k(i,s)}," 您的浏览器不支持音频播放 ",40,ot)):(v(),p("span",it,"未生成"))]))),128))])):T("",!0),e("div",rt,[e("button",{onClick:C,disabled:n.value||!$.statement.english,class:"btn btn-xs btn-primary"},_(n.value?"生成中...":"生成音频"),9,ut),m.value==="completed"?(v(),p("button",{key:0,onClick:f,class:"btn btn-xs btn-error"}," 删除 ")):T("",!0)])]))}}),ct=me(dt,[["__scopeId","data-v-9bf431db"]]),mt={class:"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"},gt={class:"bg-white rounded-lg shadow-lg w-4/5 max-w-4xl max-h-[90vh] flex flex-col"},vt={class:"flex-shrink-0 p-6 pb-4 border-b border-gray-200"},pt={class:"flex justify-between items-center mb-4"},ht={class:"flex space-x-2"},ft=["disabled"],bt={class:"relative"},yt={key:0,class:"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10"},xt={class:"flex-1 overflow-auto px-6 py-4"},$t={key:0,class:"text-center py-4 bg-gray-50 rounded"},wt={key:1,class:"overflow-x-auto"},_t={class:"min-w-full divide-y divide-gray-200"},kt={class:"bg-white divide-y divide-gray-200"},St={class:"px-4 py-2 whitespace-nowrap text-sm text-gray-500"},Ct={class:"px-4 py-2"},Ft=["onUpdate:modelValue"],Ut={class:"px-4 py-2"},Mt=["onUpdate:modelValue","onBlur"],Et={class:"px-4 py-2"},Gt=["onUpdate:modelValue"],At={class:"px-4 py-2"},It={class:"px-4 py-2 whitespace-nowrap"},Tt=["onClick","disabled"],Lt=["onClick","disabled"],Pt=["onClick"],Rt={class:"flex-shrink-0 p-6 pt-4 border-t border-gray-200"},jt={class:"flex justify-end space-x-2"},Bt=["disabled"],Dt=X({__name:"StatementModal",props:{course:{},statements:{}},emits:["close","updated"],setup(G,{emit:c}){const o=G,l=c,n=S([]),d=S(!1),m=S(!1),g=S(!1);ne(async()=>{var t;console.log("📝 语句编辑模态框开始初始化，语句数量:",(t=o.statements)==null?void 0:t.length);const{useGlobalLoading:i}=await Fe(async()=>{const{useGlobalLoading:a}=await import("./qi-a-hqW.js").then(r=>r.ai);return{useGlobalLoading:a}},__vite__mapDeps([0,1]),import.meta.url),s=i();s.showLoading({title:"语句编辑初始化中",message:"正在加载语句数据，请稍候",key:"statement-modal-init"});try{n.value=[],o.statements&&o.statements.length>0?(await new Promise(a=>setTimeout(a,600)),n.value=o.statements.map(a=>({id:a.id,chinese:a.chinese||"",english:a.english||"",soundmark:a.soundmark||"",audioStatus:a.audioStatus||"pending",audioEnUsMale:a.audioEnUsMale||null,audioEnUsFemale:a.audioEnUsFemale||null,audioEnGbMale:a.audioEnGbMale||null,audioEnGbFemale:a.audioEnGbFemale||null})),console.log("✅ 已加载",n.value.length,"个语句")):(h(),console.log("✅ 添加了空行"))}finally{s.hideLoading("statement-modal-init")}});const h=()=>{n.value.push({chinese:"",english:"",soundmark:"",audioStatus:"pending",audioEnUsMale:null,audioEnUsFemale:null,audioEnGbMale:null,audioEnGbFemale:null}),console.log("添加新行，当前语句数量:",n.value.length)},b=i=>{n.value.splice(i,1),n.value.length===0&&h()},F=i=>{if(i>0){const s=n.value[i];n.value[i]=n.value[i-1],n.value[i-1]=s}},k=i=>{if(i<n.value.length-1){const s=n.value[i];n.value[i]=n.value[i+1],n.value[i+1]=s}},C=async i=>{const s=n.value[i];if(s.english&&!s.soundmark)try{console.log("获取音标:",s.english);const t=await j.post(`/api/admin/courses/${o.course.id}/phonetic`,{text:s.english}).catch(a=>(console.error("获取音标请求失败:",a),null));t&&t.phonetic&&(s.soundmark=t.phonetic)}catch(t){console.error("获取音标失败:",t)}},f=async()=>{const i=n.value.filter(s=>s.chinese&&s.english);if(i.length===0){alert("请至少添加一条有效语句");return}d.value=!0;try{console.log("保存语句到课程:",o.course.id),await j.put(`/api/admin/courses/${o.course.id}/statements`,{statements:i}),l("updated"),l("close")}catch(s){console.error("保存语句失败:",s),alert("保存语句失败，请重试")}finally{d.value=!1}},U=async()=>{if(!g.value&&confirm("确定要为当前课程的所有语句生成音频吗？这可能需要一些时间。")){g.value=!0;try{const i=await j.post("/api/admin/audio/course/generate",{courseId:o.course.id,voiceTypes:["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"],regenerate:!1});console.log("批量音频生成结果:",i),alert(`批量音频生成完成！成功: ${i.successCount}，失败: ${i.failCount}`)}catch(i){console.error("批量生成音频失败:",i),alert("批量生成音频失败，请重试")}finally{g.value=!1}}},$=i=>{if(console.log("音频生成完成:",i),i&&i.id){const s=n.value.findIndex(t=>t.id===i.id);s!==-1&&(n.value[s].audioStatus=i.audioStatus||"completed",i.audioFiles&&(n.value[s].audioEnUsMale=i.audioFiles.enUsMale,n.value[s].audioEnUsFemale=i.audioFiles.enUsFemale,n.value[s].audioEnGbMale=i.audioFiles.enGbMale,n.value[s].audioEnGbFemale=i.audioFiles.enGbFemale),console.log("已更新语句音频状态和文件"))}},L=i=>{if(console.log("🗑️ 音频删除事件:",i),i&&i.statementId){const s=n.value.findIndex(t=>t.id===i.statementId);s!==-1&&(n.value[s].audioStatus="pending",n.value[s].audioEnUsMale=null,n.value[s].audioEnUsFemale=null,n.value[s].audioEnGbMale=null,n.value[s].audioEnGbFemale=null,n.value[s].audioGeneratedAt=null,console.log("✅ 已清除语句音频状态和文件"))}if(i.deletedFiles>0||i.affectedStatements>0)if(console.log("🔄 更新所有受影响的语句状态"),i.affectedStatementIds&&i.affectedStatementIds.length>0)i.affectedStatementIds.forEach(s=>{const t=n.value.find(a=>a.id===s);t&&(t.audioStatus="pending",t.audioEnUsMale=null,t.audioEnUsFemale=null,t.audioEnGbMale=null,t.audioEnGbFemale=null,t.audioGeneratedAt=null,console.log(`✅ 已更新语句 ${t.id} 的状态为pending`))});else{const s=i.english;s&&n.value.forEach(t=>{t.english===s&&(t.audioStatus="pending",t.audioEnUsMale=null,t.audioEnUsFemale=null,t.audioEnGbMale=null,t.audioEnGbFemale=null,t.audioGeneratedAt=null,console.log(`✅ 已更新语句 ${t.id} 的状态为pending`))})}},E=()=>{m.value=!1,alert(`导入文件格式说明：

1. Excel格式(.xlsx)：
   - 第一行为表头：中文、英文、音标（可留空）
   - 从第二行开始，每行一条语句

2. CSV格式(.csv)：
   - 第一行为表头：中文,英文,音标（可留空）
   - 从第二行开始，每行一条语句，用逗号分隔

3. 文本格式(.txt)：
   - 每行一条语句
   - 格式：中文[分隔符]英文[分隔符]音标
   - 支持的分隔符：制表符、逗号、中文顿号(、)、分号
   - 例如：你好,Hello,həˈloʊ

注意：中文和英文为必填项，音标可选`)};return(i,s)=>(v(),p("div",mt,[e("div",gt,[e("div",vt,[e("div",pt,[s[6]||(s[6]=e("h2",{class:"text-lg font-bold"},"编辑课程语句",-1)),e("div",ht,[e("button",{onClick:h,class:"btn btn-sm btn-secondary"},"添加语句"),e("button",{onClick:U,class:"btn btn-sm btn-info",disabled:g.value},_(g.value?"生成中...":"批量生成音频"),9,ft),e("div",bt,[e("button",{onClick:s[0]||(s[0]=t=>m.value=!m.value),class:"btn btn-sm btn-outline"},s[2]||(s[2]=[xe(" 下载模板 ",-1),e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)])),m.value?(v(),p("div",yt,[e("div",{class:"py-1"},[s[3]||(s[3]=e("a",{href:"/excel/course-import-template.xlsx",download:"",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},"Excel模板 (.xlsx)",-1)),s[4]||(s[4]=e("a",{href:"/excel/course-import-template.csv",download:"",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},"CSV模板 (.csv)",-1)),s[5]||(s[5]=e("a",{href:"/excel/course-import-template.txt",download:"",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},"文本模板 (.txt)",-1)),e("div",{onClick:E,class:"block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100 cursor-pointer"},"查看格式说明")])])):T("",!0)])])])]),e("div",xt,[n.value.length===0?(v(),p("div",$t,s[7]||(s[7]=[e("p",null,"暂无语句数据，请添加语句或通过批量导入",-1)]))):T("",!0),n.value.length>0?(v(),p("div",wt,[e("table",_t,[s[8]||(s[8]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16"},"序号"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"中文"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"英文"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"音标"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"音频"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24"},"操作")])],-1)),e("tbody",kt,[(v(!0),p(Q,null,W(n.value,(t,a)=>(v(),p("tr",{key:a},[e("td",St,_(a+1),1),e("td",Ct,[z(e("input",{"onUpdate:modelValue":r=>t.chinese=r,class:"w-full border rounded px-2 py-1",placeholder:"中文"},null,8,Ft),[[Z,t.chinese]])]),e("td",Ut,[z(e("input",{"onUpdate:modelValue":r=>t.english=r,class:"w-full border rounded px-2 py-1",placeholder:"英文",onBlur:r=>C(a)},null,40,Mt),[[Z,t.english]])]),e("td",Et,[z(e("input",{"onUpdate:modelValue":r=>t.soundmark=r,class:"w-full border rounded px-2 py-1",placeholder:"音标(可选)"},null,8,Gt),[[Z,t.soundmark]])]),e("td",At,[ce(ct,{statement:t,courseId:i.course.id,onAudioGenerated:$,onUpdated:$,onAudioDeleted:L},null,8,["statement","courseId"])]),e("td",It,[e("button",{onClick:r=>F(a),disabled:a===0,class:"btn btn-xs btn-outline mr-1"},"↑",8,Tt),e("button",{onClick:r=>k(a),disabled:a===n.value.length-1,class:"btn btn-xs btn-outline mr-1"},"↓",8,Lt),e("button",{onClick:r=>b(a),class:"btn btn-xs btn-error"},"删除",8,Pt)])]))),128))])])])):T("",!0)]),e("div",Rt,[e("div",jt,[e("button",{onClick:s[1]||(s[1]=t=>i.$emit("close")),class:"btn"},"取消"),e("button",{onClick:f,class:"btn btn-primary",disabled:d.value},_(d.value?"保存中...":"保存语句"),9,Bt)])])])]))}}),K=pe(new Map),ae=S(!1),ue=S(!1),ee=pe(new Map),te=pe(new Set);class zt{constructor(){H(this,"CACHE_DURATION",300*1e3);H(this,"BATCH_SIZE",100);H(this,"BATCH_DELAY",200)}async initialize(){if(!(ae.value||ue.value)){ue.value=!0,console.log("🚀 初始化音频复用管理器");try{const c=await j.get("/api/admin/audio/reuse-relations");if(c.relations)for(const o of c.relations){const l=this.getCacheKey(o.english);ee.set(l,o.sourceStatementId),K.set(l,{reused:!0,audioFiles:o.audioFiles,sourceStatement:o.sourceStatement,lastChecked:Date.now(),verified:!0})}ae.value=!0,console.log(`✅ 复用管理器初始化完成，加载了 ${ee.size} 个复用关系`)}catch(c){console.error("❌ 复用管理器初始化失败:",c)}finally{ue.value=!1}}}async getReuseState(c,o){const l=this.getCacheKey(c),n=K.get(l);if(n&&this.isCacheValid(n))return console.log(`📋 从缓存获取复用状态: ${c}`),n;if(te.has(l))return console.log(`⏳ 复用检查进行中: ${c}`),this.waitForCheck(l);te.add(l);try{console.log(`🔍 检查复用状态: ${c}`);const d=await this.checkReuseState(c,o);return K.set(l,{...d,lastChecked:Date.now(),verified:!0}),d}finally{te.delete(l)}}async batchGetReuseStates(c){const o=new Map,l=[];for(const n of c){const d=this.getCacheKey(n.english),m=K.get(d);m&&this.isCacheValid(m)?o.set(d,m):l.push(n)}if(l.length>0){console.log(`🔍 批量检查 ${l.length} 个复用状态`);const n=await this.batchCheckReuseStates(l);for(const[d,m]of n)K.set(d,{...m,lastChecked:Date.now(),verified:!0}),o.set(d,m)}return o}async updateReuseRelations(c,o){const l=this.getCacheKey(c);o?(ee.set(l,o),console.log(`📝 更新复用关系: ${c} -> ${o}`)):(ee.delete(l),K.delete(l),console.log(`🗑️ 删除复用关系: ${c}`)),this.notifyReuseChange(l)}clearCache(){K.clear(),ee.clear(),te.clear(),ae.value=!1,console.log("🧹 清除复用缓存")}getStats(){return{totalRelations:ee.size,cachedStates:K.size,pendingChecks:te.size,isInitialized:ae.value}}getCacheKey(c){return c.trim().toLowerCase()}isCacheValid(c){return c.verified&&Date.now()-c.lastChecked<this.CACHE_DURATION}async checkReuseState(c,o){try{const l=await j.post("/api/admin/audio/check-reuse",{english:c.trim(),excludeStatementId:o});return{reused:l.reused||!1,audioFiles:l.audioFiles||{},sourceStatement:l.source,lastChecked:Date.now(),verified:!0}}catch(l){return console.error("检查复用状态失败:",l),{reused:!1,audioFiles:{},lastChecked:Date.now(),verified:!1}}}async batchCheckReuseStates(c){try{const o=await j.post("/api/admin/audio/batch-check-reuse",{requests:c.map(n=>({english:n.english.trim(),excludeStatementId:n.statementId}))}),l=new Map;if(o.results)for(const[n,d]of Object.entries(o.results)){const m=this.getCacheKey(n);l.set(m,{reused:d.reused||!1,audioFiles:d.audioFiles||{},sourceStatement:d.sourceStatement,lastChecked:Date.now(),verified:!0})}return l}catch(o){return console.error("批量检查复用状态失败:",o),new Map}}async waitForCheck(c){for(;te.has(c);)await new Promise(o=>setTimeout(o,50));return K.get(c)||{reused:!1,audioFiles:{},lastChecked:Date.now(),verified:!1}}notifyReuseChange(c){console.log(`📢 通知复用状态变化: ${c}`)}}const se=new zt;function Vt(){return{isInitialized:O(()=>ae.value),isInitializing:O(()=>ue.value),initialize:()=>se.initialize(),getReuseState:(G,c)=>se.getReuseState(G,c),batchGetReuseStates:G=>se.batchGetReuseStates(G),updateReuseRelations:(G,c)=>se.updateReuseRelations(G,c),clearCache:()=>se.clearCache(),getStats:()=>se.getStats()}}class Ot{constructor(){H(this,"pendingRequests",new Map);H(this,"batchQueue",[]);H(this,"batchTimeout",null);H(this,"BATCH_SIZE",50);H(this,"BATCH_DELAY",100)}async checkAudioReuse(c,o){const l=c.trim();if(console.log(`🔍 batchAudioReuseService.checkAudioReuse: "${l}"`),this.pendingRequests.has(l))return console.log(`♻️ 复用现有请求: "${l}"`),this.pendingRequests.get(l);const n=new Promise((d,m)=>{this.batchQueue.push({english:l,excludeStatementId:o,resolve:d,reject:m}),console.log(`📝 添加到批量队列: "${l}", 队列长度: ${this.batchQueue.length}`),this.batchTimeout&&(clearTimeout(this.batchTimeout),console.log("⏰ 清除旧的批量处理定时器")),console.log(`⏰ 设置批量处理定时器: ${this.BATCH_DELAY}ms`),this.batchTimeout=setTimeout(()=>{console.log("🚀 批量处理定时器触发"),this.processBatch()},this.BATCH_DELAY)});return this.pendingRequests.set(l,n),console.log(`💾 保存请求Promise: "${l}", 总请求数: ${this.pendingRequests.size}`),n}async processBatch(){if(this.batchQueue.length===0)return;const c=this.batchQueue.splice(0,this.BATCH_SIZE);console.log(`🔍 批量处理音频复用检查，共 ${c.length} 个请求`);try{const o=await j.post("/api/admin/audio/batch-check-reuse",{requests:c.map(n=>({english:n.english,excludeStatementId:n.excludeStatementId}))}),l=new Map;if(o&&o.results)for(const[n,d]of Object.entries(o.results))l.set(n,d);for(const n of c){const d=n.english.trim(),m=l.get(d);n.resolve&&(m?n.resolve(m):n.resolve({reused:!1,audioFiles:{}})),this.pendingRequests.delete(d)}console.log(`✅ 批量检查完成，找到 ${Array.from(l.values()).filter(n=>n.reused).length} 个可复用音频`)}catch(o){console.error("批量音频复用检查失败:",o);for(const l of c)l.reject&&l.reject(o),this.pendingRequests.delete(l.english.trim())}}clearPendingRequests(){this.pendingRequests.clear(),this.batchQueue=[],this.batchTimeout&&(clearTimeout(this.batchTimeout),this.batchTimeout=null)}getPendingCount(){return this.pendingRequests.size+this.batchQueue.length}}const qt=new Ot,Ht={class:"audio-controls"},Kt={class:"flex items-center space-x-2 mb-2"},Nt={key:0,class:"space-y-1 mb-2"},Qt={class:"text-xs text-gray-600 w-16"},Wt=["src"],Yt={key:1,class:"text-xs text-gray-400"},Jt={class:"flex space-x-1"},Zt=["disabled"],Xt=["disabled"],es=120*1e3,be=50,ts=X({__name:"AudioControls",props:{statement:{},courseId:{}},emits:["audio-generated","updated","audio-deleted"],setup(G,{emit:c}){const o=new Map,l=G,n=c,d=he(),m=S(!1),g=S(!1),h=S(null),b=S("pending"),F=S({enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null}),k=O(()=>{if(U.value)return"text-green-600";switch(b.value){case"generating":return"text-blue-600";case"failed":return"text-red-600";case"reusable":return"text-purple-600";default:return"text-gray-600"}}),C=O(()=>{var u;if(U.value){if(b.value==="completed")return f.value?"已生成 (复用)":"已生成";if(h.value!==null)return"已生成 (复用)"}switch(b.value){case"generating":return"生成中";case"failed":return"生成失败";case"reusable":return`可复用 (来源: ${((u=h.value)==null?void 0:u.courseTitle)||"其他课程"})`;default:return"未生成"}}),f=O(()=>h.value!==null),U=O(()=>b.value==="completed"||h.value!==null),$=()=>{if(m.value)return"生成中...";if(f.value&&b.value==="pending")return"生成新音频";if(b.value==="completed")return"重新生成";switch(b.value){case"reusable":return"重新生成";default:return"生成音频"}},L=u=>({enUsMale:"美男",enUsFemale:"美女",enGbMale:"英男",enGbFemale:"英女"})[u]||u,E=async()=>{var w;if(!((w=l.statement)!=null&&w.id)||!h.value){console.error("❌ 应用复用失败：缺少必要信息");return}if(!confirm(`确定要应用复用音频吗？

来源：${h.value.courseTitle}
英文：${h.value.english}

应用后，这个音频将被持久化到数据库中，
学习平台就能正确读取和播放了。`))return;m.value=!0;const y=`apply-reuse-${l.statement.id}`;d.showLoading({title:"应用复用音频",message:"正在将复用音频持久化到数据库，请稍候",key:y});try{console.log(`🔄 应用复用音频到数据库: ${l.statement.id}`);const M=await j.post("/api/admin/audio/statement/generate",{statementId:l.statement.id,voiceTypes:["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"],regenerate:!1});if(M.reused&&M.statement){const R=M.statement;b.value=R.audioStatus,F.value={enUsMale:R.audioEnUsMale||null,enUsFemale:R.audioEnUsFemale||null,enGbMale:R.audioEnGbMale||null,enGbFemale:R.audioEnGbFemale||null},h.value=M.source,g.value=!1,console.log(`✅ 复用音频已持久化到数据库: ${l.statement.id}`),n("audio-generated",R),alert("复用音频已成功应用到数据库！")}else throw new Error("应用复用音频失败")}catch(M){console.error("应用复用音频失败:",M),alert("应用复用音频失败，请重试")}finally{d.hideLoading(y),m.value=!1}},i=async()=>{if(m.value||!l.statement.english)return;let u=!0;if(f.value&&h.value){if(!confirm(`当前音频是从"${h.value.courseTitle}"复用的。

重新生成后，新音频将自动应用到所有相同英文内容的语句，
确保音频的一致性。

点击"确定"：重新生成并替换所有相同内容的音频
点击"取消"：保持使用复用的音频`))return}else try{const y=await j.post("/api/admin/audio/check-usage",{statementId:l.statement.id});if(y.otherUsageCount>0&&!confirm(`检测到有 ${y.otherUsageCount} 个其他语句使用相同的音频。

重新生成后，新音频将自动应用到所有相同英文内容的语句，
确保音频的一致性。

点击"确定"：重新生成并替换所有相同内容的音频
点击"取消"：取消重新生成`))return}catch{}m.value=!0,b.value="generating",d.showLoading({title:"音频生成中",message:f.value?"正在生成独立音频，请稍候":"正在生成语句音频，请稍候",key:`audio-${l.statement.id}`});try{const y={statementId:l.statement.id,voiceTypes:["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"],regenerate:!0,replaceAllSame:u},w=await j.post("/api/admin/audio/statement/generate",y);w.statement&&(b.value=w.statement.audioStatus,F.value=w.statement.audioFiles,h.value=null,n("audio-generated",w.statement),U.value&&(g.value=!1,w.replacedCount>0&&setTimeout(()=>{alert(`音频生成成功！
✅ 已同时更新 ${w.replacedCount} 个相同内容的语句`)},500)))}catch{b.value="failed",alert("生成音频失败，请重试")}finally{d.hideLoading(`audio-${l.statement.id}`),m.value=!1}},s=async()=>{let u;if(f.value&&h.value)u=`⚠️ 删除源音频确认

该语句使用的是从"${h.value.courseTitle}"复用的音频。

删除后：
• 源音频文件将被删除
• 所有使用该音频的语句都会变成"未生成"状态
• 可以重新生成新的音频文件

确定要删除源音频吗？`;else try{const y=await j.post("/api/admin/audio/check-usage",{statementId:l.statement.id});y.otherUsageCount>0?u=`⚠️ 删除源音频确认

当前音频正被 ${y.otherUsageCount+1} 个语句使用。

删除后：
• 源音频文件将被删除
• 所有 ${y.otherUsageCount+1} 个语句都会变成"未生成"状态
• 可以重新生成新的音频文件

确定要删除源音频吗？`:u=`确定要删除这个语句的音频吗？

删除后可以重新生成新的音频文件。`}catch{u=`确定要删除这个语句的音频吗？

删除后可以重新生成新的音频文件。`}if(confirm(u)){d.showLoading({title:"删除中",message:"正在删除音频文件，请稍候",key:`delete-${l.statement.id}`});try{const y=await j.delete(`/api/admin/audio/statement/${l.statement.id}`),w=y.data||y;b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},g.value=!1,n("updated",{...l.statement,audioFiles:null,audioStatus:"pending",audioEnUsMale:null,audioEnUsFemale:null,audioEnGbMale:null,audioEnGbFemale:null,audioGeneratedAt:null});const M=`✅ 源音频删除完成！

📊 处理结果：
• ${w.affectedStatements||0} 个语句状态已更新为"未生成"
• ${w.sourceAudioDeleted?"源音频文件已删除":"源音频文件已不存在"}

💡 说明：
• 所有使用该音频的语句现在都是"未生成"状态
• 重新生成音频后，新音频将成为源音频供其他语句复用
• 您可以点击"生成音频"来创建新的音频文件`;b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},g.value=!1,n("updated",{...l.statement,audioFiles:null,audioStatus:"pending",audioEnUsMale:null,audioEnUsFemale:null,audioEnGbMale:null,audioEnGbFemale:null,audioGeneratedAt:null}),alert(M);const R={statementId:l.statement.id,english:l.statement.english,deletedFiles:w.deletedFiles||0,affectedStatements:w.affectedStatements||0,sourceAudioDeleted:w.sourceAudioDeleted||!1,affectedStatementIds:w.affectedStatementIds||[]};n("audio-deleted",R),h.value=null,setTimeout(()=>{confirm(`是否立即重新生成音频？

这样可以为所有相同内容的语句生成统一的音频。`)&&i()},500)}catch{alert("删除音频失败，请重试")}finally{d.hideLoading(`delete-${l.statement.id}`)}}},t=S(!1),a=S(!1);let r=null;const x=async()=>{if(!l.statement.english||!l.statement.english.trim()){console.log(`❌ 跳过复用检查 - 英文内容为空: ${l.statement.id}`);return}if(t.value){console.log(`❌ 跳过复用检查 - 正在检查中: ${l.statement.id}`);return}const u=l.statement.english.trim().toLowerCase(),y=o.get(u);if(y&&Date.now()-y.timestamp<es){console.log(`📋 从缓存获取复用状态: ${l.statement.id} - "${l.statement.english}"`),P(y.result);return}console.log(`🔍 开始检查复用: ${l.statement.id} - "${l.statement.english}"`),t.value=!0;try{console.log(`📞 调用批量复用服务: ${l.statement.english}`);const w=await qt.checkAudioReuse(l.statement.english,l.statement.id);console.log("📞 批量复用服务响应:",w),console.log(`🔍 响应详情 - reused: ${w==null?void 0:w.reused}, audioFiles:`,w==null?void 0:w.audioFiles),o.set(u,{result:w,timestamp:Date.now()}),P(w)}catch(w){console.error("检查音频复用失败:",w),a.value||(h.value=null)}finally{t.value=!1}},A=u=>{if(!u)return!1;const y=u.audioEnUsMale||u.audioEnUsFemale||u.audioEnGbMale||u.audioEnGbFemale;return console.log(`🔍 检查数据库音频: ${u.id}, 有音频: ${!!y}`),!!y},P=u=>{var y,w;if(u&&u.reused){const M=u.audioFiles||{};Object.values(M).some(J=>J)?(console.log(`✅ 找到可复用音频: ${l.statement.id}`),console.log(`   来源: ${(y=u.sourceStatement)==null?void 0:y.courseTitle}`),console.log(`   英文: "${(w=u.sourceStatement)==null?void 0:w.english}"`),F.value={enUsMale:M.enUsMale||null,enUsFemale:M.enUsFemale||null,enGbMale:M.enGbMale||null,enGbFemale:M.enGbFemale||null},h.value=u.sourceStatement,b.value="pending",g.value=!1):(console.log(`❌ 复用音频无效（无音频文件）: ${l.statement.id}`),h.value=null)}else console.log(`❌ 未找到可复用音频: ${l.statement.id}`),h.value=null},I=async()=>{var y,w,M,R,J;if(r&&clearTimeout(r),a.value){console.log(`⚠️ 组件 ${(y=l.statement)==null?void 0:y.id} 正在初始化中，跳过重复初始化`);return}a.value=!0,console.log(`🚀 开始初始化组件: ${(w=l.statement)==null?void 0:w.id}`),h.value=null,g.value=!1;const u=`audio-init-${((M=l.statement)==null?void 0:M.id)||"new"}`;d.showLoading({title:"检查音频状态",message:"正在检查语句的音频状态，请稍候",showProgress:!0,key:u});try{if(d.updateProgress(10),await new Promise(q=>setTimeout(q,100)),d.updateProgress(30),!l.statement){b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},d.updateProgress(100);return}if(d.updateProgress(50),l.statement.audioStatus==="completed"&&A(l.statement))console.log(`💾 从数据库加载音频: ${l.statement.id}`),b.value="completed",F.value={enUsMale:l.statement.audioEnUsMale||null,enUsFemale:l.statement.audioEnUsFemale||null,enGbMale:l.statement.audioEnGbMale||null,enGbFemale:l.statement.audioEnGbFemale||null},h.value=null,g.value=!1,d.updateProgress(100);else if(l.statement.audioStatus==="generating")console.log(`⏳ 音频生成中: ${l.statement.id}`),b.value="generating",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},h.value=null,g.value=!1,d.updateProgress(100);else{if(console.log(`🔍 数据库无音频，检查复用: ${l.statement.id}`),d.updateProgress(70),b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},h.value=null,g.value=!1,l.statement.english&&l.statement.english.trim())try{d.updateProgress(90),await x()}catch(q){console.error(`❌ 复用检查失败: ${l.statement.id}`,q)}d.updateProgress(100)}}catch(q){console.error(`❌ 音频状态初始化失败: ${(R=l.statement)==null?void 0:R.id}`,q),b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},d.updateProgress(100)}finally{try{d.hideLoading(u)}catch(q){console.error(`❌ 隐藏加载动画失败: ${u}`,q),d.forceCleanQueue()}console.log(`✅ 组件初始化完成: ${(J=l.statement)==null?void 0:J.id}`),a.value=!1}};V(()=>l.statement,(u,y)=>{u&&u!==y&&(console.log(`🔄 语句变化触发重新初始化: ${u==null?void 0:u.id}`),r=setTimeout(()=>{I()},100))},{deep:!0}),V(()=>{var u;return(u=l.statement)==null?void 0:u.audioStatus},(u,y)=>{u!==y&&u!==b.value&&(b.value=u||"pending",u==="pending"&&(F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},h.value=null,g.value=!1))}),V(()=>{var u,y,w,M;return[(u=l.statement)==null?void 0:u.audioEnUsMale,(y=l.statement)==null?void 0:y.audioEnUsFemale,(w=l.statement)==null?void 0:w.audioEnGbMale,(M=l.statement)==null?void 0:M.audioEnGbFemale]},(u,y)=>{const w=u.every(R=>!R),M=y&&y.some(R=>R);w&&M&&(h.value=null,g.value=!1,b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null})},{deep:!0}),V(()=>{var u,y,w,M;return[(u=l.statement)==null?void 0:u.audioEnUsMale,(y=l.statement)==null?void 0:y.audioEnUsFemale,(w=l.statement)==null?void 0:w.audioEnGbMale,(M=l.statement)==null?void 0:M.audioEnGbFemale]},(u,y)=>{var M,R,J,q;if(u.some((ge,ve)=>ge!==(y==null?void 0:y[ve]))){F.value={enUsMale:((M=l.statement)==null?void 0:M.audioEnUsMale)||null,enUsFemale:((R=l.statement)==null?void 0:R.audioEnUsFemale)||null,enGbMale:((J=l.statement)==null?void 0:J.audioEnGbMale)||null,enGbFemale:((q=l.statement)==null?void 0:q.audioEnGbFemale)||null};const ge=Object.values(F.value).some(ve=>ve);g.value=ge}},{deep:!0}),V(()=>{var u;return(u=l.statement)==null?void 0:u.english},(u,y)=>{u?u!==y&&y!==void 0&&x():(b.value="pending",F.value={enUsMale:null,enUsFemale:null,enGbMale:null,enGbFemale:null},g.value=!1)});const B=u=>u?u.startsWith("http")?u:`http://localhost:5000${u}`:"",D=u=>{},Y={currentOpenId:null,closeAll(){window.dispatchEvent(new CustomEvent("closeAllAudioPlayers"))},openPlayer(u){this.currentOpenId!==u&&(this.closeAll(),this.currentOpenId=u)}};let oe=0;const we=u=>{oe++,console.log(`🎵 音频播放器创建: ${oe}/${be}`),oe>be&&console.warn(`⚠️ 音频播放器数量过多: ${oe}`)},_e=u=>{},ke=()=>{g.value?(g.value=!1,console.log(`🔽 隐藏音频播放器: ${l.statement.id}`)):(Y.openPlayer(l.statement.id),g.value=!0,console.log(`🔼 展开音频播放器: ${l.statement.id}`))},fe=()=>{g.value&&(g.value=!1,console.log(`🔽 响应全局关闭: ${l.statement.id}`))};return ne(()=>{window.addEventListener("closeAllAudioPlayers",fe),I()}),Ue(()=>{r&&clearTimeout(r),window.removeEventListener("closeAllAudioPlayers",fe)}),(u,y)=>(v(),p("div",Ht,[e("div",Kt,[e("span",{class:le(["text-xs",k.value])},_(C.value),3),b.value==="completed"?(v(),p("button",{key:0,onClick:ke,class:"btn btn-xs btn-outline"},_(g.value?"隐藏":"查看详情"),1)):T("",!0)]),ce($e,{statement:u.statement,reuseInfo:{reused:h.value!==null,audioFiles:h.value?F.value:{},source:h.value}},null,8,["statement","reuseInfo"]),g.value&&U.value?(v(),p("div",Nt,[(v(!0),p(Q,null,W(F.value,(w,M)=>(v(),p("div",{key:M,class:"flex items-center space-x-2"},[e("span",Qt,_(L(M)),1),w?(v(),p("audio",{key:0,src:B(w),controls:"",preload:"none",class:"h-8 text-xs",style:{"min-height":"32px","min-width":"120px"},onError:D,onLoadstart:we,onCanplay:_e},null,40,Wt)):(v(),p("span",Yt,"未生成"))]))),128))])):T("",!0),e("div",Jt,[f.value&&b.value==="pending"?(v(),p("button",{key:0,onClick:E,disabled:m.value,class:le(["btn btn-xs btn-success",{loading:m.value}])},_(m.value?"应用中...":"应用复用"),11,Zt)):T("",!0),e("button",{onClick:i,disabled:m.value||!u.statement.english,class:le(["btn btn-xs btn-primary",{loading:m.value}])},_($()),11,Xt),b.value==="completed"||f.value&&b.value==="pending"?(v(),p("button",{key:1,onClick:s,class:"btn btn-xs btn-error"}," 删除 ")):T("",!0)])]))}}),ss=me(ts,[["__scopeId","data-v-3a3dfcc4"]]),ls={class:"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"},as={class:"bg-white p-6 rounded-lg shadow-lg w-4/5 max-w-5xl max-h-[90vh] overflow-auto relative"},ns={class:"bg-blue-50 p-4 rounded-lg mb-6"},os={class:"text-lg font-semibold text-blue-800 mb-2"},is={class:"text-blue-600"},rs={class:"mb-6 p-4 bg-green-50 rounded-lg"},us={class:"text-lg font-semibold mb-3 text-green-800"},ds={class:"grid grid-cols-3 gap-6 text-sm"},cs={class:"text-center"},ms={class:"text-2xl font-bold text-blue-600"},gs={class:"text-center"},vs={class:"text-2xl font-bold text-green-600"},ps={class:"text-center"},hs={class:"text-2xl font-bold text-orange-600"},fs={key:0,class:"mt-3 text-sm text-green-700"},bs={key:1,class:"mt-2 text-sm text-blue-700"},ys={class:"bg-gray-50 p-4 rounded-lg mb-6"},xs={class:"flex flex-wrap gap-4 items-center"},$s={class:"flex items-center space-x-2"},ws={class:"flex space-x-2"},_s={class:"flex items-center"},ks={class:"flex items-center"},Ss={class:"flex items-center"},Cs={class:"flex items-center"},Fs=["disabled"],Us=["disabled"],Ms=["disabled"],Es={key:0,class:"mt-4"},Gs={class:"flex justify-between text-sm text-gray-600 mb-1"},As={class:"w-full bg-gray-200 rounded-full h-2"},Is={class:"space-y-4"},Ts={key:0,class:"text-center py-8"},Ls={key:1,class:"text-center py-8 text-gray-500"},Ps={key:2,class:"space-y-3"},Rs={class:"flex items-start justify-between"},js={class:"flex-1"},Bs={class:"flex items-center mb-2"},Ds={class:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2"},zs={class:"flex-1"},Vs={class:"font-medium text-gray-900 mb-1"},Os={class:"text-gray-600"},qs={key:0,class:"text-gray-500 text-sm"},Hs={class:"ml-4"},Ks={class:"flex justify-end mt-6 pt-4 border-t"},Ns=X({__name:"AudioManagementModal",props:{course:{},statements:{}},emits:["close","updated"],setup(G,{emit:c}){const o=G,l=c,n=he(),d=Vt(),m=S(!1),g=S(!1),h=S(["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"]),b=S({current:0,total:0}),F=S(0),k=S({totalStatements:0,completedStatements:0,pendingStatements:0}),C=O(()=>{if(!o.statements||o.statements.length===0)return{totalStatements:0,completedStatements:0,pendingStatements:0};const t=o.statements.length,a=o.statements.filter(x=>x.audioStatus==="completed").length,r=o.statements.filter(x=>x.audioStatus==="pending").length;return{totalStatements:t,completedStatements:a,pendingStatements:r}}),f=async()=>{try{console.log("正在获取课程音频统计:",o.course.id);const t=new Promise((x,A)=>{setTimeout(()=>A(new Error("获取课程音频统计超时")),5e3)}),a=j.get(`/api/admin/audio/course/${o.course.id}/stats`),r=await Promise.race([a,t]);if(console.log("课程音频统计响应:",r),r&&r.stats){const x=r.stats;k.value={totalStatements:x.totalStatements,completedStatements:x.statementsWithAudio,pendingStatements:x.pendingStatements},console.log("课程音频统计更新成功:",k.value)}else console.log("使用基础统计作为备用"),k.value=C.value}catch(t){console.error("获取课程音频统计失败:",t),console.log("使用基础统计作为备用"),k.value=C.value}};V(C,t=>{k.value.totalStatements===0&&(k.value={totalStatements:t.totalStatements,completedStatements:t.completedStatements,pendingStatements:t.pendingStatements})},{immediate:!0});const U=async()=>{var r;if(!((r=o.course)!=null&&r.id)||!confirm(`确定要批量应用复用音频吗？

这将把所有可复用的音频持久化到数据库中，
确保学习平台能正确读取音频文件。

此操作不会生成新音频，只是应用现有的复用关系。`))return;g.value=!0;const a=`batch-apply-reuse-${o.course.id}`;n.showLoading({title:"批量应用复用音频",message:"正在将复用音频持久化到数据库，请稍候",showProgress:!0,key:a});try{console.log(`🚀 开始批量应用复用音频: 课程 ${o.course.id}`);const x=await j.post("/api/admin/audio/batch-apply-reuse",{courseId:o.course.id});console.log(`✅ 批量应用完成: ${x.appliedCount}/${x.totalStatements}`),await f(),l("updated"),alert(`批量应用复用音频完成！

已应用: ${x.appliedCount} 个
总语句: ${x.totalStatements} 个`)}catch(x){console.error("批量应用复用音频失败:",x);let A="批量应用复用音频失败";x.response&&x.response.data?(A+=`

错误详情: ${x.response.data.message||x.response.data.error}`,x.response.data.details&&console.error("详细错误堆栈:",x.response.data.details)):x.message&&(A+=`

错误信息: ${x.message}`),alert(A+`

请检查控制台获取更多信息，然后重试`)}finally{n.hideLoading(a),g.value=!1}},$=async()=>{if(g.value||h.value.length===0)return;g.value=!0,b.value={current:0,total:o.statements.length},n.showLoading({title:"音频生成中",message:`正在批量生成音频，共 ${o.statements.length} 个语句，请稍候`,showProgress:!0,key:"batch-audio"});let t=null;(()=>{let r=0;const x=o.statements.length,A=Math.max(6e4,x*2e3),P=1e3,I=x*.9/(A/P);t=Ie(()=>{if(r<x*.9){r+=I,b.value.current=Math.floor(r);const B=Math.round(b.value.current/b.value.total*100);n.updateProgress(B)}},P)})();try{const r=await j.post("/api/admin/audio/course/generate",{courseId:o.course.id,voiceTypes:h.value,regenerate:!1});t&&(clearInterval(t),t=null),b.value.current=b.value.total,n.updateProgress(100);const x=r.data||r,A=x.successCount||0,P=x.failCount||0,I=x.reuseCount||0,B=I>0?`批量音频生成完成！
✅ 成功: ${A}
❌ 失败: ${P}
🔄 复用: ${I}`:`批量音频生成完成！
✅ 成功: ${A}
❌ 失败: ${P}`;alert(B),l("updated"),await f()}catch(r){console.error("批量生成音频失败:",r),alert("批量生成音频失败，请重试")}finally{t&&clearInterval(t),n.hideLoading("batch-audio"),g.value=!1,b.value={current:0,total:0}}},L=async()=>{if(g.value||k.value.completedStatements===0)return;const t=`确定要删除课程 "${o.course.title}" 的所有音频吗？

⚠️ 注意：
• 只会删除不被其他课程使用的音频文件
• 被其他课程复用的音频文件会被保留
• 此操作不可撤销`;if(confirm(t)){g.value=!0,n.showLoading({title:"批量删除音频中",message:"正在安全删除课程音频，请稍候",key:"batch-delete-audio",priority:10});try{const a=await j.delete(`/api/admin/audio/course/${o.course.id}`),r=a.data||a,x=`批量删除完成！
🗑️ 删除文件: ${r.deletedFiles||0}
🔒 保留文件: ${r.keptFiles||0}
📝 处理语句: ${r.totalStatements||0}`;alert(x),l("updated"),await f()}catch{alert("批量删除音频失败，请重试")}finally{n.hideLoading("batch-delete-audio"),g.value=!1}}},E=async t=>{const a=o.statements.findIndex(r=>r.id===t.id);a!==-1&&(o.statements[a]={...o.statements[a],...t,audioStatus:t.audioStatus,audioEnUsMale:t.audioEnUsMale||null,audioEnUsFemale:t.audioEnUsFemale||null,audioEnGbMale:t.audioEnGbMale||null,audioEnGbFemale:t.audioEnGbFemale||null,audioGeneratedAt:t.audioGeneratedAt||null}),l("updated"),await f()},i=async t=>{l("updated"),await new Promise(a=>setTimeout(a,200)),F.value++},s=()=>{n.hideLoading("batch-audio"),g.value=!1,b.value={current:0,total:0},l("close")};return ne(async()=>{n.showLoading({title:"音频管理初始化中",message:"正在加载音频数据和检查音频状态，请稍候",showProgress:!0,key:"audio-modal-init"});try{console.log("AudioManagementModal 开始初始化");const t=setTimeout(()=>{console.warn("音频管理初始化超时，强制完成"),n.hideLoading("audio-modal-init"),m.value=!1},1e4);console.log("🚀 初始化复用管理器"),d.isInitialized.value||await d.initialize(),await f(),await new Promise(a=>setTimeout(a,200)),clearTimeout(t),console.log("AudioManagementModal 初始化完成")}catch(t){console.error("AudioManagementModal 初始化失败:",t)}finally{n.hideLoading("audio-modal-init"),m.value=!1}}),(t,a)=>(v(),p("div",ls,[e("div",as,[ce(Ae,{show:N(n).isLoading.value,title:N(n).loadingTitle.value,message:N(n).loadingMessage.value,"show-progress":N(n).showProgress.value,progress:N(n).progress.value,class:"absolute inset-0 z-10"},null,8,["show","title","message","show-progress","progress"]),e("div",{class:"flex justify-between items-center mb-6"},[a[7]||(a[7]=e("h2",{class:"text-2xl font-bold text-gray-800"},"🎵 音频管理",-1)),e("button",{onClick:s,class:"text-gray-500 hover:text-gray-700"},a[6]||(a[6]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",ns,[e("h3",os,_(t.course.title),1),e("p",is,"共 "+_(t.statements.length)+" 个语句",1)]),e("div",rs,[e("h3",us,"🎵 "+_(t.course.title)+" - 音频统计",1),e("div",ds,[e("div",cs,[e("div",ms,_(k.value.totalStatements),1),a[8]||(a[8]=e("div",{class:"text-gray-600"},"总语句数",-1))]),e("div",gs,[e("div",vs,_(k.value.completedStatements),1),a[9]||(a[9]=e("div",{class:"text-gray-600"},"已有音频",-1))]),e("div",ps,[e("div",hs,_(k.value.pendingStatements),1),a[10]||(a[10]=e("div",{class:"text-gray-600"},"待生成",-1))])]),k.value.completedStatements>0?(v(),p("div",fs," ✅ 本课程已完成 "+_(k.value.completedStatements)+" 个语句的音频生成 ",1)):T("",!0),k.value.pendingStatements>0?(v(),p("div",bs," ⏳ 还有 "+_(k.value.pendingStatements)+" 个语句待生成音频 ",1)):T("",!0)]),e("div",ys,[a[17]||(a[17]=e("h4",{class:"text-lg font-semibold mb-4"},"批量操作",-1)),e("div",xs,[e("div",$s,[a[15]||(a[15]=e("label",{class:"text-sm font-medium"},"语音类型:",-1)),e("div",ws,[e("label",_s,[z(e("input",{type:"checkbox","onUpdate:modelValue":a[0]||(a[0]=r=>h.value=r),value:"en-US-Male",class:"mr-1"},null,512),[[ie,h.value]]),a[11]||(a[11]=e("span",{class:"text-sm"},"美式男声",-1))]),e("label",ks,[z(e("input",{type:"checkbox","onUpdate:modelValue":a[1]||(a[1]=r=>h.value=r),value:"en-US-Female",class:"mr-1"},null,512),[[ie,h.value]]),a[12]||(a[12]=e("span",{class:"text-sm"},"美式女声",-1))]),e("label",Ss,[z(e("input",{type:"checkbox","onUpdate:modelValue":a[2]||(a[2]=r=>h.value=r),value:"en-GB-Male",class:"mr-1"},null,512),[[ie,h.value]]),a[13]||(a[13]=e("span",{class:"text-sm"},"英式男声",-1))]),e("label",Cs,[z(e("input",{type:"checkbox","onUpdate:modelValue":a[3]||(a[3]=r=>h.value=r),value:"en-GB-Female",class:"mr-1"},null,512),[[ie,h.value]]),a[14]||(a[14]=e("span",{class:"text-sm"},"英式女声",-1))])])]),e("button",{onClick:U,disabled:g.value,class:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50 mr-2",title:"将所有可复用的音频持久化到数据库，确保学习平台能正确读取"},_(g.value?"应用中...":"批量应用复用音频到数据库"),9,Fs),e("button",{onClick:$,disabled:g.value||h.value.length===0,class:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50 mr-2"},_(g.value?"批量生成中...":"批量生成音频"),9,Us),e("button",{onClick:L,disabled:g.value||k.value.completedStatements===0,class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"}," 批量删除音频 ",8,Ms)]),g.value?(v(),p("div",Es,[e("div",Gs,[a[16]||(a[16]=e("span",null,"生成进度",-1)),e("span",null,_(b.value.current)+" / "+_(b.value.total),1)]),e("div",As,[e("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:Me({width:b.value.total>0?b.value.current/b.value.total*100+"%":"0%"})},null,4)])])):T("",!0)]),e("div",Is,[a[20]||(a[20]=e("h4",{class:"text-lg font-semibold"},"语句音频状态",-1)),m.value?(v(),p("div",Ts,a[18]||(a[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600"},"加载中...",-1)]))):t.statements.length===0?(v(),p("div",Ls,[a[19]||(a[19]=e("p",null,"该课程暂无语句",-1)),e("button",{onClick:a[4]||(a[4]=r=>t.$emit("close")),class:"mt-2 text-blue-500 hover:underline"}," 去添加语句 ")])):(v(),p("div",Ps,[(v(!0),p(Q,null,W(t.statements,(r,x)=>(v(),p("div",{key:r.id||x,class:"border rounded-lg p-4 bg-white shadow-sm"},[e("div",Rs,[e("div",js,[e("div",Bs,[e("span",Ds,_(x+1),1),e("div",zs,[e("div",Vs,_(r.chinese),1),e("div",Os,_(r.english),1),r.soundmark?(v(),p("div",qs,_(r.soundmark),1)):T("",!0)])])]),e("div",Hs,[(v(),re(ss,{key:`${r.id}-${r.audioStatus}-${r.audioEnUsMale}-${r.audioEnUsFemale}-${r.audioEnGbMale}-${r.audioEnGbFemale}-${F.value}`,statement:r,courseId:t.course.id,onAudioGenerated:E,onUpdated:E,onAudioDeleted:i},null,8,["statement","courseId"]))])])]))),128))]))]),e("div",Ks,[e("button",{onClick:a[5]||(a[5]=r=>t.$emit("close")),class:"bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded"}," 关闭 ")])])]))}}),Qs=me(Ns,[["__scopeId","data-v-271c84ae"]]),Ws={key:0,class:"p-4"},Ys={class:"flex justify-between items-center mb-4"},Js={class:"text-xl font-bold"},Zs={class:"flex space-x-2"},Xs={class:"mb-4"},el={class:"flex items-center mb-2"},tl={key:0,class:"mb-2"},sl=["src"],ll={key:1,class:"w-32 h-20 bg-gray-200 flex items-center justify-center rounded mb-2"},al={class:"mb-4"},nl={class:"text-lg font-bold mb-2"},ol={key:0,class:"text-center py-4"},il={key:1,class:"text-center py-4 bg-gray-50 rounded"},rl={key:2,class:"border rounded divide-y divide-gray-100 bg-white"},ul={class:"flex items-center mb-1"},dl={class:"w-10 text-gray-400 text-center font-mono"},cl={class:"flex-1"},ml={class:"flex items-center mb-1"},gl={class:"flex-1"},vl={class:"flex items-center"},pl={class:"flex-1"},Ql=X({__name:"CourseDetail",props:{course:{},coursePacks:{}},setup(G){const c=G,o=S([]),l=S(!1),n=S(!1),d=S(!1),m=he();V(d,s=>{s&&m.hideLoading("audio-management")}),V(n,s=>{s&&m.hideLoading("statement-edit")});const g=S(!1),h=S(null),b=S(!1),F=O(()=>{if(c.course.cover)return c.course.cover;const s=`course_cover_${c.course.id}`;return localStorage.getItem(s)||""}),k=s=>{var t,a;if((t=s.course_packs)!=null&&t.title)return s.course_packs.title;if(s.coursePackId&&((a=c.coursePacks)!=null&&a.length)){const r=c.coursePacks.find(x=>x.id===s.coursePackId);if(r!=null&&r.title)return r.title}return""},C=async()=>{var s;if((s=c.course)!=null&&s.id){g.value=!0,m.showLoading({title:"加载课程数据",message:"正在获取课程语句数据，请稍候",key:"fetch-statements"});try{const t=await j.get(`/api/admin/courses/${c.course.id}/statements`);o.value=t||[]}catch{o.value=[]}finally{m.hideLoading("fetch-statements"),g.value=!1}}},f=async()=>{await C()},U=()=>{m.showLoading({title:"课程语句编辑",message:"正在检查语句音频状态，请稍候",key:"statement-modal-loading"}),setTimeout(()=>{m.hideLoading("statement-modal-loading"),n.value=!0},1e3)},$=()=>{try{m.showLoading({title:"程序在加载中",message:"正在准备音频管理界面，请稍候",key:"audio-management"}),setTimeout(()=>{d.value=!0},300)}catch{d.value=!0}},L=()=>{b.value=!0,f()},E=()=>{h.value.click()},i=async s=>{var x;const t=s.target.files[0];if(!t)return;const a=(x=t.name.split(".").pop())==null?void 0:x.toLowerCase();if(!["xlsx","xls","csv","txt"].includes(a||"")){alert("请上传支持的文件格式: Excel(.xlsx/.xls)、CSV(.csv)或文本文件(.txt)"),s.target.value="";return}if(t.size===0){alert("文件内容为空，请选择有效的文件"),s.target.value="";return}if(a==="txt")try{const P=(await t.text()).split(/\r?\n/).filter(D=>D.trim());if(P.length===0){alert("文件内容为空，请确保文件包含有效数据"),s.target.value="";return}P.slice(0,Math.min(3,P.length)).filter(D=>/[,、;；\t]/.test(D)).length===0&&alert("文件格式可能不正确，请确保每行包含中文和英文，并使用逗号、制表符或分号分隔")}catch{}const r=new FormData;r.append("file",t);try{const A=localStorage.getItem("adminToken")||sessionStorage.getItem("adminToken"),P=A?A.startsWith("Bearer ")?A:`Bearer ${A}`:"",I={};P&&(I.Authorization=P);const B=await fetch(`/api/admin/courses/${c.course.id}/import`,{method:"POST",headers:I,body:r});let D;try{D=await B.json()}catch{D={message:"无法解析服务器响应"}}if(!B.ok)throw new Error(D.message||D.error||"语句批量导入失败");alert(`导入成功! 共导入${D.count}条语句。`),C()}catch(A){alert(`语句批量导入失败：${A.message||"请检查文件格式"}`)}finally{s.target.value=""}};return V(()=>{var s;return(s=c.course)==null?void 0:s.id},C,{immediate:!0}),(s,t)=>s.course?(v(),p("div",Ws,[e("div",Ys,[e("h2",Js,_(s.course.title),1),e("div",Zs,[e("button",{onClick:t[0]||(t[0]=a=>l.value=!0),class:"bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"},"编辑封面"),e("button",{onClick:U,class:"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"},"课程语句编辑"),e("button",{onClick:$,class:"bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"},"🎵 音频管理"),e("button",{onClick:E,class:"bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm"},"语句批量导入")])]),e("div",Xs,[e("div",el,[t[5]||(t[5]=e("span",{class:"mr-2 font-bold"},"所属课程包:",-1)),e("span",null,_(k(s.course)),1)]),F.value?(v(),p("div",tl,[e("img",{src:F.value,class:"w-32 h-20 object-cover rounded"},null,8,sl)])):(v(),p("div",ll,t[6]||(t[6]=[e("span",{class:"text-gray-500"},"无封面",-1)])))]),e("div",al,[e("h3",nl,"语句列表 ("+_(o.value.length)+")",1),g.value?(v(),p("div",ol,"加载中...")):o.value.length===0?(v(),p("div",il,[t[7]||(t[7]=e("p",null,"暂无语句数据",-1)),e("button",{onClick:t[1]||(t[1]=a=>n.value=!0),class:"btn btn-sm btn-primary mt-2"},"添加语句")])):(v(),p("div",rl,[(v(!0),p(Q,null,W(o.value,(a,r)=>(v(),p("div",{key:a.id||r,class:"p-4"},[e("div",ul,[e("div",dl,_(r+1),1),t[8]||(t[8]=e("div",{class:"w-16 font-bold"},"中文",-1)),e("div",cl,_(a.chinese),1)]),e("div",ml,[t[9]||(t[9]=e("div",{class:"w-10"},null,-1)),t[10]||(t[10]=e("div",{class:"w-16 font-bold"},"英文",-1)),e("div",gl,_(a.english),1)]),e("div",vl,[t[11]||(t[11]=e("div",{class:"w-10"},null,-1)),t[12]||(t[12]=e("div",{class:"w-16 font-bold"},"音标",-1)),e("div",pl,_(a.soundmark),1)])]))),128))]))]),l.value?(v(),re(Ke,{key:0,course:s.course,onClose:t[2]||(t[2]=a=>l.value=!1),onUpdated:L},null,8,["course"])):T("",!0),n.value?(v(),re(Dt,{key:1,course:s.course,statements:o.value,onClose:t[3]||(t[3]=a=>n.value=!1),onUpdated:f},null,8,["course","statements"])):T("",!0),d.value?(v(),re(Qs,{key:2,course:s.course,statements:o.value,onClose:t[4]||(t[4]=a=>d.value=!1),onUpdated:f},null,8,["course","statements"])):T("",!0),e("input",{ref_key:"fileInput",ref:h,type:"file",accept:".xlsx,.xls,.csv,.txt",class:"hidden",onChange:i},null,544)])):T("",!0)}}),hl={class:"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"},fl={class:"bg-white p-6 rounded-lg shadow-lg w-96 max-h-[90vh] overflow-auto"},bl={class:"mb-4"},yl=["value"],xl={class:"mb-4"},$l={class:"mb-4"},wl={key:0,class:"mt-2"},_l=["src"],kl={class:"mb-4"},Sl={class:"flex space-x-2"},Cl={key:0,class:"mb-4"},Fl={class:"border rounded p-3 mb-2"},Ul={class:"mb-2"},Ml={class:"mb-2"},El={key:0,class:"border rounded p-2"},Gl={class:"text-sm font-medium mb-2"},Al={class:"max-h-40 overflow-y-auto"},Il={class:"text-sm"},Tl=["onClick"],Ll={key:1,class:"mb-4"},Pl={class:"text-sm text-gray-500"},Rl={class:"mt-2"},jl={class:"flex items-center"},Bl={key:0,class:"mt-1 bg-gray-50 p-2 rounded"},Dl={class:"flex justify-end space-x-2"},zl=["disabled"],Wl=X({__name:"CreateCourseModal",props:{coursePacks:{}},emits:["close","created"],setup(G,{emit:c}){const o=c,l=ye(),n=S({coursePackId:"",title:"",cover:"",statements:[]}),d=S({chinese:"",english:""}),m=S("single"),g=S(""),h=S(null),b=S(!1),F=S(!1),k=i=>{const s=i.target.files[0];if(!s)return;const t=new FileReader;t.onload=a=>{g.value=a.target.result},t.readAsDataURL(s)},C=()=>{const i=d.value.chinese.trim(),s=d.value.english.trim();i&&s?(n.value.statements.push({chinese:i,english:s,soundmark:""}),d.value.chinese="",d.value.english=""):l.warning("请输入完整的中文和英文内容")},f=i=>{n.value.statements.splice(i,1)},U=i=>{var a;const s=i.target.files[0];if(!s)return;const t=(a=s.name.split(".").pop())==null?void 0:a.toLowerCase();if(!["xlsx","xls","csv","txt"].includes(t)){l.error("不支持的文件格式，请上传.xlsx、.xls、.csv或.txt文件"),i.target.value="";return}console.log("选择的Excel文件:",s.name,"type:",s.type,"size:",s.size),h.value=s},$=async()=>{if(!g.value)return null;const i=document.querySelector('input[type="file"][accept="image/*"]');if(!i.files[0])return null;const s=new FormData;s.append("file",i.files[0]);try{console.log("开始上传封面图片...");const t=localStorage.getItem("adminToken")||sessionStorage.getItem("adminToken"),a=de().public.apiBaseUrl||"http://localhost:5000",r=t?t.startsWith("Bearer ")?t:`Bearer ${t}`:"",x=await fetch(`${a}/api/admin/upload`,{method:"POST",headers:r?{Authorization:r}:{},body:s});if(!x.ok){const P=await x.json();throw new Error(P.message||"文件上传失败")}const A=await x.json();return console.log("封面上传成功:",A),A.url}catch(t){return console.error("上传封面失败:",t),l.error(t instanceof Error?t.message:"上传封面失败"),null}},L=()=>{alert(`导入文件格式说明：
  
1. Excel格式(.xlsx)：
   - 第一行为表头：中文、英文、音标（可留空）
   - 从第二行开始，每行一条语句

2. CSV格式(.csv)：
   - 第一行为表头：中文,英文,音标（可留空）
   - 从第二行开始，每行一条语句，用逗号分隔

3. 文本格式(.txt)：
   - 每行一条语句
   - 格式：中文[分隔符]英文[分隔符]音标
   - 支持的分隔符：制表符、逗号、中文顿号(、)、分号
   - 例如：你好,Hello,həˈloʊ
   
注意：中文和英文为必填项，音标可选`)},E=async()=>{b.value=!0;try{const i={coursePackId:n.value.coursePackId,title:n.value.title};let s=null;g.value&&(s=await $(),s&&(i.cover=s));const t=localStorage.getItem("adminToken")||sessionStorage.getItem("adminToken"),a=de().public.apiBaseUrl||"http://localhost:5000",r=t?t.startsWith("Bearer ")?t:`Bearer ${t}`:"",x={"Content-Type":"application/json"};r&&(x.Authorization=r),console.log("创建课程:",i);const A=await fetch(`${a}/api/admin/courses`,{method:"POST",headers:x,body:JSON.stringify(i)});if(!A.ok){const I=await A.json();throw new Error(I.message||"创建课程失败")}const P=await A.json();if(console.log("创建的课程:",P),s&&P.id){const I=`course_cover_${P.id}`;localStorage.setItem(I,s)}if(P.id){if(m.value==="single"&&n.value.statements.length>0){console.log("添加单句模式语句...");try{const I=await fetch(`${a}/api/admin/courses/${P.id}/statements`,{method:"PUT",headers:x,body:JSON.stringify({statements:n.value.statements})});if(I.ok)console.log("语句保存成功");else{const B=await I.json();console.warn("语句保存失败:",B),l.warning(`课程已创建，但语句保存失败: ${B.message||"未知错误"}`)}}catch(I){console.error("语句保存失败:",I),l.warning(`课程已创建，但语句保存失败: ${I.message||"网络错误"}`)}}if(m.value==="excel"&&h.value){console.log("添加Excel导入语句...");try{const I=new FormData;I.append("file",h.value);const B={};r&&(B.Authorization=r);const D=await fetch(`${a}/api/admin/courses/${P.id}/import`,{method:"POST",headers:B,body:I}),Y=await D.json();D.ok?console.log("Excel导入成功:",Y):(console.warn("Excel导入失败:",Y),l.warning(`课程已创建，但语句导入失败: ${Y.message||Y.error||"未知错误"}`))}catch(I){console.error("Excel导入失败:",I),l.warning(`课程已创建，但语句导入失败: ${I.message||"网络错误"}`)}}}l.success("课程创建成功"),o("created"),o("close")}catch(i){console.error("创建课程失败:",i),l.error(i instanceof Error?i.message:"创建课程失败")}finally{b.value=!1}};return(i,s)=>(v(),p("div",hl,[e("div",fl,[s[19]||(s[19]=e("h2",{class:"text-lg font-bold mb-4"},"创建新课程",-1)),e("form",{onSubmit:Ee(E,["prevent"])},[e("div",bl,[s[9]||(s[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"所属课程包",-1)),z(e("select",{"onUpdate:modelValue":s[0]||(s[0]=t=>n.value.coursePackId=t),required:"",class:"w-full border rounded px-3 py-2"},[s[8]||(s[8]=e("option",{value:"",disabled:""},"请选择课程包",-1)),(v(!0),p(Q,null,W(i.coursePacks,t=>(v(),p("option",{key:t.id,value:t.id},_(t.title),9,yl))),128))],512),[[Ge,n.value.coursePackId]])]),e("div",xl,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"课程标题",-1)),z(e("input",{"onUpdate:modelValue":s[1]||(s[1]=t=>n.value.title=t),required:"",class:"w-full border rounded px-3 py-2",placeholder:"输入课程标题"},null,512),[[Z,n.value.title]])]),e("div",$l,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"课程封面",-1)),e("input",{type:"file",accept:"image/*",onChange:k,class:"w-full"},null,32),g.value?(v(),p("div",wl,[e("img",{src:g.value,class:"w-full h-32 object-cover rounded"},null,8,_l)])):T("",!0)]),e("div",kl,[s[12]||(s[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"语句导入方式",-1)),e("div",Sl,[e("button",{type:"button",class:le([m.value==="single"?"bg-blue-500 text-white":"bg-gray-200","px-3 py-1 rounded"]),onClick:s[2]||(s[2]=t=>m.value="single")}," 逐句创建 ",2),e("button",{type:"button",class:le([m.value==="excel"?"bg-blue-500 text-white":"bg-gray-200","px-3 py-1 rounded"]),onClick:s[3]||(s[3]=t=>m.value="excel")}," 语句批量导入 ",2)])]),m.value==="single"?(v(),p("div",Cl,[e("div",Fl,[e("div",Ul,[s[13]||(s[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"中文",-1)),z(e("input",{"onUpdate:modelValue":s[4]||(s[4]=t=>d.value.chinese=t),class:"w-full border rounded px-3 py-2",placeholder:"输入中文"},null,512),[[Z,d.value.chinese]])]),e("div",Ml,[s[14]||(s[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"英文",-1)),z(e("input",{"onUpdate:modelValue":s[5]||(s[5]=t=>d.value.english=t),class:"w-full border rounded px-3 py-2",placeholder:"输入英文"},null,512),[[Z,d.value.english]])]),e("button",{type:"button",onClick:C,class:"btn btn-sm btn-secondary w-full"}," 添加语句 ")]),n.value.statements.length>0?(v(),p("div",El,[e("h3",Gl,"已添加语句 ("+_(n.value.statements.length)+")",1),e("ul",Al,[(v(!0),p(Q,null,W(n.value.statements,(t,a)=>(v(),p("li",{key:a,class:"flex justify-between items-center py-1 border-b last:border-b-0"},[e("span",Il,_(t.chinese)+" - "+_(t.english),1),e("button",{type:"button",onClick:r=>f(a),class:"text-red-500 text-xs"},"删除",8,Tl)]))),128))])])):T("",!0)])):T("",!0),m.value==="excel"?(v(),p("div",Ll,[e("input",{type:"file",onChange:U,accept:".xlsx,.xls,.csv,.txt",class:"w-full mb-2"},null,32),e("div",Pl,[s[17]||(s[17]=e("p",null,"请上传符合格式的文件，支持以下格式：",-1)),s[18]||(s[18]=e("ul",{class:"list-disc pl-5 mt-1"},[e("li",null,"Excel文件 (.xlsx, .xls)"),e("li",null,"CSV文件 (.csv)"),e("li",null,"文本文件 (.txt)")],-1)),e("div",Rl,[e("div",jl,[e("button",{type:"button",onClick:s[6]||(s[6]=t=>F.value=!F.value),class:"text-blue-500 flex items-center"},s[15]||(s[15]=[xe(" 下载导入模板 ",-1),e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)]))]),F.value?(v(),p("div",Bl,s[16]||(s[16]=[e("a",{href:"/excel/course-import-template.xlsx",download:"",class:"block text-blue-500 hover:underline mb-1"},"Excel模板 (.xlsx)",-1),e("a",{href:"/excel/course-import-template.csv",download:"",class:"block text-blue-500 hover:underline mb-1"},"CSV模板 (.csv)",-1),e("a",{href:"/excel/course-import-template.txt",download:"",class:"block text-blue-500 hover:underline"},"文本模板 (.txt)",-1)]))):T("",!0),e("button",{type:"button",onClick:L,class:"text-blue-500 mt-1"},"查看格式说明")])])])):T("",!0),e("div",Dl,[e("button",{type:"button",onClick:s[7]||(s[7]=t=>i.$emit("close")),class:"btn"},"取消"),e("button",{type:"submit",class:"btn btn-primary",disabled:b.value},_(b.value?"创建中...":"创建课程"),9,zl)])],32)])]))}});export{Ql as _,Wl as a,Ke as b};
