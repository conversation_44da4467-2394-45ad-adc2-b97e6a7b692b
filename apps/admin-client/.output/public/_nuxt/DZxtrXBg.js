import{e as V,f as R,q as L,z as N,r as m,g as O,c as l,o as i,b as q,a as t,h as C,d as $,i as H,m as K,F as G,j as J,t as p,K as Q}from"./qi-a-hqW.js";import{A as X}from"./oUsj6fiR.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const Y={class:"flex min-h-screen bg-gray-100"},Z={class:"flex-1 pl-64"},ee={class:"p-8"},te={class:"w-full max-w-6xl mx-auto"},oe={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"},se={key:1},ae={class:"bg-white p-6 rounded-lg shadow mb-6"},re={class:"flex flex-wrap gap-4"},ne=["disabled"],le={key:0,class:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},ie={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ce={class:"relative"},de={class:"ml-4"},ue={class:"inline-flex items-center cursor-pointer"},me={class:"bg-white rounded-lg shadow overflow-hidden"},fe={key:0,class:"p-8 text-center"},ge={key:1,class:"p-8 text-center text-gray-500"},pe={key:2,class:"overflow-x-auto"},he={class:"min-w-full divide-y divide-gray-200"},ve={class:"bg-white divide-y divide-gray-200"},xe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},we={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},be={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ke={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ye={class:"flex space-x-2"},_e=["onClick"],Be=["onClick"],Ie=["onClick"],Se={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ce={class:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full"},$e={class:"mb-6 font-semibold"},Ae={class:"flex justify-end space-x-3"},Pe={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Te={class:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full"},je={class:"mb-6 font-semibold"},ze={class:"flex justify-end space-x-3"},Ve=V({__name:"database",setup(De){const r=R(),A=Q(),{adminUser:B}=L(),d=N(()=>B.value?B.value.role==="superadmin":!1),u=m(!1),b=m(!1),k=m([]),y=m(null),c=m(""),h=m(!1),v=m(!1),x=m(!1);function f(){if(typeof window<"u"){if(console.log("当前主机名:",window.location.hostname),window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1")return console.log("使用本地API地址: http://localhost:5000"),"http://localhost:5000";const o=window.location.protocol,e=window.location.hostname,s=`${o}//${e}`;return console.log("使用生产环境API地址:",s),s}return A.public.apiBaseUrl||"http://localhost:5000"}async function w(){if(d.value){b.value=!0;try{const o=localStorage.getItem("adminToken"),e=o?o.startsWith("Bearer ")?o:`Bearer ${o}`:"";console.log("使用认证令牌:",e.substring(0,20)+"...");const s=f();console.log("获取备份列表使用的API地址:",s);const a=await fetch(`${s}/api/admin/database/backups`,{headers:{Authorization:e}});if(!a.ok)throw new Error("获取备份列表失败");k.value=await a.json()}catch(o){r.error(o.message||"获取备份列表失败"),console.error("获取备份列表出错:",o)}finally{b.value=!1}}}async function P(){if(!d.value){r.error("您没有权限执行此操作");return}u.value=!0;try{const o=localStorage.getItem("adminToken"),e=o?o.startsWith("Bearer ")?o:`Bearer ${o}`:"",s=f();console.log("创建备份使用的API地址:",s);const a=await fetch(`${s}/api/admin/database/backup`,{method:"POST",headers:{Authorization:e}});if(!a.ok){const g=await a.json();throw new Error(g.message||"创建备份失败")}const n=await a.json();r.success("数据库备份创建成功"),w()}catch(o){r.error(o.message||"创建备份失败"),console.error("创建备份出错:",o)}finally{u.value=!1}}async function T(o){if(!d.value){r.error("您没有权限执行此操作");return}const e=o.target.files;if(!e||e.length===0)return;const s=e[0],a=new FormData;a.append("backupFile",s),a.append("autoRestore",x.value.toString()),u.value=!0;try{const n=localStorage.getItem("adminToken"),g=n?n.startsWith("Bearer ")?n:`Bearer ${n}`:"",I=f();console.log("上传备份文件使用的API地址:",I);const _=await fetch(`${I}/api/admin/database/upload`,{method:"POST",headers:{Authorization:g},body:a});if(!_.ok){const F=await _.json();throw new Error(F.message||"上传备份文件失败")}const S=await _.json();x.value&&S.restoreResult?(r.success("备份文件上传并自动恢复成功，服务器正在重启中，页面将在10秒后刷新..."),setTimeout(()=>{localStorage.removeItem("courseData"),localStorage.removeItem("coursePackages"),localStorage.removeItem("userSettings"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),localStorage.removeItem("adminUser"),r.info("正在重新连接服务器..."),setTimeout(()=>{window.location.href="/admin/login"},1e3)},1e4)):r.success(S.message||"备份文件上传成功"),y.value&&(y.value.value=""),w()}catch(n){r.error(n.message||"上传备份文件失败"),console.error("上传备份文件出错:",n)}finally{u.value=!1}}function j(o){if(!d.value){r.error("您没有权限执行此操作");return}const e=localStorage.getItem("adminToken"),s=e?e.startsWith("Bearer ")?e:`Bearer ${e}`:"",a=f();console.log("下载备份文件使用的API地址:",a);const n=document.createElement("a");n.href=`${a}/api/admin/database/download/${o}?token=${encodeURIComponent(s)}`,n.download=o,document.body.appendChild(n),n.click(),document.body.removeChild(n)}function z(o){if(!d.value){r.error("您没有权限执行此操作");return}c.value=o,h.value=!0}async function D(){if(!(!c.value||!d.value)){u.value=!0;try{const o=localStorage.getItem("adminToken"),e=o?o.startsWith("Bearer ")?o:`Bearer ${o}`:"",s=f();console.log("恢复数据库使用的API地址:",s);const a=await fetch(`${s}/api/admin/database/restore/${c.value}`,{method:"POST",headers:{Authorization:e}});if(!a.ok){const g=await a.json();throw new Error(g.message||"恢复备份失败")}const n=await a.json();r.success("数据库恢复成功，服务器正在重启中，页面将在10秒后刷新..."),h.value=!1,c.value="",setTimeout(()=>{localStorage.removeItem("courseData"),localStorage.removeItem("coursePackages"),localStorage.removeItem("userSettings"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),localStorage.removeItem("adminUser"),r.info("正在重新连接服务器..."),setTimeout(()=>{window.location.href="/admin/login"},1e3)},1e4)}catch(o){r.error(o.message||"恢复备份失败"),console.error("恢复备份出错:",o)}finally{u.value=!1}}}function U(o){if(!d.value){r.error("您没有权限执行此操作");return}c.value=o,v.value=!0}async function W(){if(!(!c.value||!d.value))try{const o=localStorage.getItem("adminToken"),e=o?o.startsWith("Bearer ")?o:`Bearer ${o}`:"",s=f();console.log("删除备份文件使用的API地址:",s);const a=await fetch(`${s}/api/admin/database/backup/${c.value}`,{method:"DELETE",headers:{Authorization:e}});if(!a.ok){const n=await a.json();throw new Error(n.message||"删除备份失败")}r.success("备份文件删除成功"),w(),v.value=!1,c.value=""}catch(o){r.error(o.message||"删除备份失败"),console.error("删除备份出错:",o)}}function E(o){if(o===0)return"0 Bytes";const e=1024,s=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(o)/Math.log(e));return parseFloat((o/Math.pow(e,a)).toFixed(2))+" "+s[a]}function M(o){return new Date(o).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})}return O(()=>{w()}),(o,e)=>(i(),l("div",Y,[q(X),t("div",Z,[t("div",ee,[t("div",te,[e[13]||(e[13]=t("h1",{class:"text-2xl font-bold mb-8 text-center"},"数据管理",-1)),d.value?(i(),l("div",se,[t("div",ae,[e[9]||(e[9]=t("h2",{class:"text-xl font-semibold mb-4"},"数据库操作",-1)),t("div",re,[t("button",{onClick:P,class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center",disabled:u.value},[u.value?(i(),l("svg",le,e[4]||(e[4]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(i(),l("svg",ie,e[5]||(e[5]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"},null,-1)]))),e[6]||(e[6]=$(" 创建数据库备份 ",-1))],8,ne),t("div",ce,[t("input",{type:"file",ref_key:"fileInput",ref:y,onChange:T,accept:".sql,.gz",class:"absolute inset-0 opacity-0 w-full h-full cursor-pointer"},null,544),e[7]||(e[7]=t("button",{class:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12"})]),$(" 上传备份文件 ")],-1))]),t("div",de,[t("label",ue,[H(t("input",{type:"checkbox","onUpdate:modelValue":e[0]||(e[0]=s=>x.value=s),class:"form-checkbox h-5 w-5 text-blue-600"},null,512),[[K,x.value]]),e[8]||(e[8]=t("span",{class:"ml-2 text-gray-700"},"上传后自动恢复",-1))])])])]),t("div",me,[e[12]||(e[12]=t("div",{class:"p-4 border-b"},[t("h2",{class:"text-xl font-semibold"},"备份文件列表"),t("p",{class:"text-sm text-gray-500 mt-1"},"请谨慎操作，恢复数据将覆盖当前数据库内容")],-1)),b.value?(i(),l("div",fe,e[10]||(e[10]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"},null,-1),t("p",{class:"mt-2 text-gray-600"},"加载备份列表中...",-1)]))):k.value.length?(i(),l("div",pe,[t("table",he,[e[11]||(e[11]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 文件名 "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 大小 "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 创建时间 "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 操作 ")])],-1)),t("tbody",ve,[(i(!0),l(G,null,J(k.value,s=>(i(),l("tr",{key:s.filename,class:"hover:bg-gray-50"},[t("td",xe,p(s.filename),1),t("td",we,p(E(s.size)),1),t("td",be,p(M(s.createdAt)),1),t("td",ke,[t("div",ye,[t("button",{onClick:a=>j(s.filename),class:"text-blue-600 hover:text-blue-900"}," 下载 ",8,_e),t("button",{onClick:a=>z(s.filename),class:"text-green-600 hover:text-green-900"}," 恢复 ",8,Be),t("button",{onClick:a=>U(s.filename),class:"text-red-600 hover:text-red-900"}," 删除 ",8,Ie)])])]))),128))])])])):(i(),l("div",ge," 暂无备份文件 "))])])):(i(),l("div",oe,e[3]||(e[3]=[t("strong",{class:"font-bold"},"无权限：",-1),t("span",{class:"block sm:inline"},"您不是超级管理员，无法访问数据管理功能。",-1)])))])])]),h.value?(i(),l("div",Se,[t("div",Ce,[e[14]||(e[14]=t("h3",{class:"text-xl font-bold text-red-600 mb-4"},"⚠️ 警告：数据库恢复",-1)),e[15]||(e[15]=t("p",{class:"mb-4"},"您确定要恢复此备份文件吗？此操作将覆盖当前数据库中的所有数据！",-1)),t("p",$e,"文件名："+p(c.value),1),t("div",Ae,[t("button",{onClick:e[1]||(e[1]=s=>h.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"}," 取消 "),t("button",{onClick:D,class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"}," 确认恢复 ")])])])):C("",!0),v.value?(i(),l("div",Pe,[t("div",Te,[e[16]||(e[16]=t("h3",{class:"text-xl font-bold mb-4"},"确认删除备份",-1)),e[17]||(e[17]=t("p",{class:"mb-4"},"您确定要删除此备份文件吗？此操作不可恢复。",-1)),t("p",je,"文件名："+p(c.value),1),t("div",ze,[t("button",{onClick:e[2]||(e[2]=s=>v.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"}," 取消 "),t("button",{onClick:W,class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"}," 确认删除 ")])])])):C("",!0)]))}});export{Ve as default};
