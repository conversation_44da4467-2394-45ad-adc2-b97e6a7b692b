function l(){if(typeof window<"u"){if(console.log("当前主机名:",window.location.hostname),window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1")return console.log("使用本地API地址: http://localhost:5000"),"http://localhost:5000";const t=window.location.protocol,o=window.location.hostname,e=`${t}//${o}`;return console.log("使用生产环境API地址:",e),e}return console.log("使用相对API路径"),""}function i(){const t={"Content-Type":"application/json"};if(typeof window<"u"){const o=localStorage.getItem("adminToken");if(o){const e=o.startsWith("Bearer ")?o:`Bearer ${o}`;t.Authorization=e,console.log("添加认证令牌到请求头:",e.substring(0,20)+"..."),console.log("完整请求头:",t)}else console.warn("未找到认证令牌!")}return t}async function u(t){if(console.log(`API响应: ${t.status} ${t.statusText} - ${t.url}`),t.status===401||t.status===403){const o=await t.json().catch(()=>({}));throw console.error("API授权错误:",o.message||t.statusText),typeof window<"u"&&(console.warn("清除认证状态并准备跳转到登录页..."),localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),localStorage.removeItem("adminUser"),window.location.href="/admin/login"),new Error(o.message||"认证失败，请重新登录")}if(!t.ok){let o="";try{const e=t.headers.get("content-type");if(e&&e.includes("application/json")){const s=await t.json();console.error("API错误响应数据:",s),o=s.message||`${t.status} ${t.statusText}`}else{const s=await t.text();console.error("API错误响应文本:",s),o=s||`${t.status} ${t.statusText}`}}catch(e){console.error("解析API错误响应失败:",e),o=`请求失败: ${t.status} ${t.statusText}`}throw console.error(`API请求失败 (${t.status}): ${o}`),new Error(o)}if(t.status===204)return{};try{return await t.json()}catch(o){return console.error("解析API响应JSON失败:",o),{}}}async function d(t,o){try{const e=new URL(t,l());o&&Object.entries(o).forEach(([n,a])=>{a!=null&&e.searchParams.append(n,String(a))}),console.log("API GET 请求 URL:",e.toString()),console.log("API GET 请求参数:",o);const s=await i();console.log("请求头:",s);const r=await fetch(e.toString(),{method:"GET",headers:s});if(console.log("响应状态:",r.status,r.statusText),!r.ok){const n=await r.text();throw console.error("API 错误响应:",{status:r.status,statusText:r.statusText,body:n}),new Error(`HTTP 错误! 状态: ${r.status}, 消息: ${n}`)}const c=await r.json();return console.group("API 响应数据详情"),console.log("响应数据类型:",typeof c),console.log("响应数据键:",Object.keys(c)),console.log("响应数据内容(JSON):",JSON.stringify(c,null,2)),console.groupEnd(),c}catch(e){throw console.error("API GET 请求错误:",{url:t,params:o,errorName:e==null?void 0:e.name,errorMessage:e==null?void 0:e.message,errorStack:e==null?void 0:e.stack}),e}}async function g(t,o){const e=await fetch(`${l()}${t}`,{method:"POST",headers:i(),body:JSON.stringify(o)});return u(e)}async function h(t,o,e={}){try{console.log(`[API] PUT 请求: ${t}`,{data:typeof o=="object"?"对象数据":o,hasOptions:!!e});const s=`${l()}${t}`;console.log(`[API] 完整URL: ${s}`);const c={method:"PUT",headers:i(),body:JSON.stringify(o),credentials:"include",...e};console.log(`[API] 发送PUT请求到 ${s}`);const n=await fetch(s,c);if(n.ok){const a=await n.json();return console.log(`[API] PUT请求成功: ${t}`,{status:n.status}),a}else{console.error(`[API] PUT请求失败: ${t}`,{status:n.status,statusText:n.statusText});let a={};try{a=await n.json(),console.error("[API] 错误详情:",a)}catch{a={message:n.statusText||"未知错误"}}throw n.status===401?(console.error("[API] 认证失败，请重新登录"),new Error("认证失败，请重新登录")):n.status===403?(console.error("[API] 权限不足，无法执行此操作"),new Error(a.message||"无权执行此操作")):new Error(a.message||"请求失败")}}catch(s){throw console.error(`[API] PUT请求异常: ${t}`,s),s}}async function w(t){const o=await fetch(`${l()}${t}`,{method:"DELETE",headers:i()});return u(o)}const P={get:d,post:g,put:h,delete:w};export{P as a};
