import{e as i,Y as d,g as s,V as m,Z as b,Q as h,c as g,$ as k}from"./qi-a-hqW.js";const p=Symbol.for("nuxt:client-only"),_=i({name:"ClientOnly",inheritAttrs:!1,props:["fallback","placeholder","placeholderTag","fallbackTag"],setup(a,{slots:e,attrs:l}){const t=d(!1);s(()=>{t.value=!0});const c=m();return c&&(c._nuxtClientOnly=!0),k(p,!0),()=>{var r;if(t.value){const n=(r=e.default)==null?void 0:r.call(e);return n&&n.length===1?[b(n[0],l)]:n}const o=e.fallback||e.placeholder;if(o)return h(o);const f=a.fallback||a.placeholder||"",u=a.fallbackTag||a.placeholderTag||"span";return g(u,l,f)}}});export{_};
