import{_ as u}from"./DqjI2rGC.js";import{k as f,y as v,c as h,o as w,a as s,b as e,w as n,d as i,s as x,ah as y,D as a}from"./qi-a-hqW.js";import{L as b}from"./D0FjqKqG.js";import{_ as L}from"./DlAUqK2U.js";const k={class:"flex h-screen"},I={class:"w-64 bg-gray-800 text-white"},S={class:"mt-6"},B={class:"px-6 py-3 hover:bg-gray-700"},N={class:"px-6 py-3 hover:bg-gray-700"},T={class:"px-6 py-3 hover:bg-gray-700"},V={class:"px-6 py-3 hover:bg-gray-700"},$={class:"px-6 py-3 hover:bg-gray-700"},C={class:"flex-1 overflow-y-auto bg-gray-100"},M={__name:"admin",setup(D){const r=f(),{isLoading:l,loadingTitle:g,loadingMessage:_,showProgress:m,progress:c}=r;typeof window<"u"&&(window.__globalLoadingInstance=r,console.log("🏗️ admin.vue 全局加载实例已暴露到window.__globalLoadingInstance")),v(l,(d,o)=>{console.log(`🏗️ admin.vue isLoading变化: ${o} -> ${d}`),console.log("🏗️ admin.vue 加载状态:",{isLoading:l.value,title:g.value,message:_.value})},{immediate:!0});const p=()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),window.location.href="/admin/login"};return(d,o)=>{const t=u;return w(),h("div",k,[s("div",I,[o[4]||(o[4]=s("div",{class:"p-6"},[s("h1",{class:"text-2xl font-bold"},"管理后台")],-1)),s("nav",S,[s("ul",null,[s("li",B,[e(t,{to:"/admin/dashboard"},{default:n(()=>o[0]||(o[0]=[i("仪表盘",-1)])),_:1,__:[0]})]),s("li",N,[e(t,{to:"/admin/users"},{default:n(()=>o[1]||(o[1]=[i("用户管理",-1)])),_:1,__:[1]})]),s("li",T,[e(t,{to:"/admin/courses"},{default:n(()=>o[2]||(o[2]=[i("课程管理",-1)])),_:1,__:[2]})]),s("li",V,[e(t,{to:"/admin/audio"},{default:n(()=>o[3]||(o[3]=[i("音频管理",-1)])),_:1,__:[3]})]),s("li",$,[s("a",{href:"#",onClick:x(p,["prevent"])},"退出登录")])])])]),s("div",C,[y(d.$slots,"default",{},void 0,!0)]),e(b,{show:a(l),title:a(g),message:a(_),"show-progress":a(m),progress:a(c)},null,8,["show","title","message","show-progress","progress"])])}}},q=L(M,[["__scopeId","data-v-6d2cbca8"]]);export{q as default};
