import{e as j,r as u,z as S,g as z,c as r,o as i,b as V,a as o,t as d,i as w,h as $,v as B,l as E,F,j as P,n as C}from"./qi-a-hqW.js";import{a as R}from"./CxzfvvNm.js";import{A as T}from"./oUsj6fiR.js";import{_ as Q}from"./DlAUqK2U.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";const W={class:"flex"},q={class:"flex-1 p-6"},G={class:"debug-info bg-yellow-100 p-4 mb-4 rounded"},H={class:"flex justify-between mb-6"},K={class:"flex space-x-2"},X={class:"relative"},Y={key:0,class:"text-center py-4"},Z={key:1,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"},ee={key:2},te={key:0,class:"text-center py-4"},oe={key:1,class:"user-list grid gap-4"},se={class:"font-bold"},le={class:"text-gray-600"},ne={class:"flex gap-2 mt-2"},ae={class:"actions flex gap-2"},re=["onClick"],ie=["onClick"],ce=j({__name:"index",setup(ue){const a=u([]),p=u(""),m=u(""),y=u(1),x=u(10),_=u(0),k=u(!0),h=u("");function A(){setTimeout(()=>{b()},300)}function N(){p.value="",b()}function U(){b()}S(()=>a.value.filter(e=>{const s=!p.value||(e.name||"").toLowerCase().includes(p.value.toLowerCase())||(e.email||"").toLowerCase().includes(p.value.toLowerCase()),t=!m.value||e.role===m.value;return s&&t}));const M=S(()=>Math.ceil(_.value/x.value));S(()=>{const e=M.value,s=y.value,t=2;if(e<=7)return Array.from({length:e},(l,I)=>I+1);const c=s-t,n=s+t,v=[],g=[];let f;for(let l=1;l<=e;l++)(l===1||l===e||l>=c&&l<=n)&&v.push(l);for(let l of v)f&&(l-f===2?g.push(f+1):l-f!==1&&g.push("...")),g.push(l),f=l;return g});async function b(){try{k.value=!0,h.value="",console.group("🔍 用户数据获取详细诊断");const e=await R.get("/api/admin/users");console.log("📦 完整 API 响应:",JSON.stringify(e,null,2)),console.log("📦 响应类型:",typeof e),console.log("📦 响应对象键:",Object.keys(e));const s=e.users,t=e.pagination;if(console.log("👥 原始用户数据:",JSON.stringify(s,null,2)),console.log("📊 分页信息:",JSON.stringify(t,null,2)),!s||!Array.isArray(s))throw new Error("无效的用户数据：不是数组或为空");const c=s.map((n,v)=>{console.log(`🔍 原始用户对象 ${v}:`,JSON.stringify(n,null,2));const g={id:n.id||n._id||`temp-${v}`,name:n.username||n.name||"未知用户",email:n.email||"未知邮箱",role:n.role||"user",isActive:n.isActive!==void 0?!!n.isActive:!0};return console.log(`🧩 处理后的用户 ${v}:`,JSON.stringify(g,null,2)),g});console.log("🚨 处理后的用户数组:",JSON.stringify(c,null,2)),console.log("🚨 处理后的用户数组长度:",c.length),a.value=c,y.value=t.page||1,x.value=t.pageSize||10,_.value=t.total||c.length,console.log("📊 响应式用户列表长度:",a.value.length),console.log("📊 响应式用户列表内容:",JSON.stringify(a.value,null,2)),console.log("📊 分页信息:",{currentPage:y.value,pageSize:x.value,totalUsers:_.value}),console.groupEnd()}catch(e){console.group("❌ 用户数据获取错误详情"),console.error("🚨 错误信息:",{errorName:e.name,errorMessage:e.message,errorStack:e.stack}),console.groupEnd(),h.value=e.message||"获取用户数据失败",a.value=[]}finally{k.value=!1}}function O(e){switch(e){case"superadmin":return"超级管理员";case"ADMIN":return"管理员";case"user":return"普通用户";default:return"未知角色"}}function J(){console.log("打开添加用户模态框")}function D(e){console.log("编辑用户:",e)}function L(e){console.log("删除用户ID:",e)}return z(()=>{console.log("🚀 页面挂载，开始获取用户数据"),b()}),(e,s)=>(i(),r("div",W,[V(T),o("div",q,[o("div",G,[s[2]||(s[2]=o("h3",{class:"font-bold mb-2"},"🔍 渲染调试信息",-1)),o("p",null,"用户数组长度: "+d(a.value.length),1),o("p",null,"用户数组类型: "+d(typeof a.value),1),o("pre",null,"用户数据详情: "+d(JSON.stringify(a.value,null,2)),1)]),o("div",{class:"flex justify-between items-center mb-6"},[s[3]||(s[3]=o("h1",{class:"text-2xl font-bold"},"用户管理",-1)),o("button",{onClick:J,class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"}," 添加用户 ")]),o("div",H,[o("div",K,[o("div",X,[w(o("input",{"onUpdate:modelValue":s[0]||(s[0]=t=>p.value=t),type:"text",placeholder:"搜索用户名或邮箱...",class:"border rounded px-3 py-2 pr-8",onInput:A},null,544),[[B,p.value]]),p.value?(i(),r("span",{key:0,onClick:N,class:"absolute right-2 top-2 cursor-pointer text-gray-500"}," × ")):$("",!0)]),w(o("select",{"onUpdate:modelValue":s[1]||(s[1]=t=>m.value=t),class:"border rounded px-3 py-2",onChange:U},s[4]||(s[4]=[o("option",{value:""},"所有角色",-1),o("option",{value:"admin"},"管理员",-1),o("option",{value:"user"},"普通用户",-1)]),544),[[E,m.value]])])]),k.value?(i(),r("div",Y,"加载中...")):h.value?(i(),r("div",Z,d(h.value),1)):(i(),r("div",ee,[a.value.length===0?(i(),r("div",te," 没有找到用户 ")):(i(),r("div",oe,[(i(!0),r(F,null,P(a.value,t=>(i(),r("div",{key:t.id,class:"user-card border rounded p-4 flex justify-between items-center"},[o("div",null,[o("h3",se,d(t.name),1),o("p",le,d(t.email),1),o("div",ne,[o("span",{class:C(["user-role px-2 py-1 rounded text-xs",{"bg-blue-100 text-blue-800":t.role==="superadmin"||t.role==="ADMIN","bg-green-100 text-green-800":t.role==="user"}])},d(O(t.role)),3),o("span",{class:C(["user-status px-2 py-1 rounded text-xs",{"bg-green-100 text-green-800":t.isActive,"bg-red-100 text-red-800":!t.isActive}])},d(t.isActive?"活跃":"禁用"),3)])]),o("div",ae,[o("button",{onClick:c=>D(t),class:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"}," 编辑 ",8,re),o("button",{onClick:c=>L(t.id),class:"bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"}," 删除 ",8,ie)])]))),128))]))]))])]))}}),he=Q(ce,[["__scopeId","data-v-4ac8eb83"]]);export{he as default};
