import{_ as L}from"./eAbK7LvP.js";import{e as V,f as z,r,g as N,c as u,o as p,b as C,w as P,a as e,h as b,i as f,v,F as T,j as U,t as c}from"./qi-a-hqW.js";import{A as B}from"./oUsj6fiR.js";import{a as y}from"./CxzfvvNm.js";import{_ as F}from"./DlAUqK2U.js";import"./DqjI2rGC.js";const I={class:"flex min-h-screen bg-gray-100"},M={class:"flex-1 pl-64"},E={class:"p-8"},q={class:"w-full max-w-6xl mx-auto"},G={class:"bg-white p-4 rounded-lg shadow mb-6 flex flex-wrap gap-4 items-end"},H={class:"bg-white rounded-lg shadow overflow-x-auto"},J={class:"min-w-full divide-y divide-gray-200"},K={class:"bg-white divide-y divide-gray-200"},O={class:"px-6 py-4 whitespace-nowrap text-sm"},Q={class:"px-6 py-4 whitespace-nowrap text-sm"},R={class:"px-6 py-4 whitespace-nowrap text-sm"},W={class:"px-6 py-4 whitespace-nowrap text-sm"},X={class:"px-6 py-4 whitespace-nowrap text-sm"},Y=["onClick"],Z={key:0},ee={class:"mt-4 flex justify-center"},te={class:"flex items-center space-x-2"},se=["disabled"],le={class:"text-gray-700"},oe=["disabled"],ae={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ne={class:"bg-white p-6 rounded-lg shadow-lg w-96"},re={class:"flex justify-end space-x-3"},ie={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},de={class:"bg-white p-6 rounded-lg shadow-lg w-96"},ue={class:"flex justify-end space-x-3"},pe=V({__name:"logs",setup(ce){const i=z(),o=r([]),n=r({username:"",email:"",start:"",end:""}),w=r(!1),h=r(null),g=r(!1),x=r(!1),d=r(null),a=r({page:1,pageSize:10,total:0,totalPages:0});function D(s){if(!s)return"-";const t=new Date(s);return isNaN(t.getTime())?s:t.toLocaleString("zh-CN",{hour12:!1})}async function m(){w.value=!0,h.value=null;try{console.log("正在获取日志...");const s=await y.get("/api/admin/logs");console.log("日志数据获取成功:",s),s&&Array.isArray(s.items)?(o.value=s.items.map(t=>({...t,actionDesc:_(t)})),a.value={page:s.page||1,pageSize:s.pageSize||10,total:s.total||0,totalPages:s.totalPages||0},console.log("使用分页格式，获取到",o.value.length,"条日志")):s&&Array.isArray(s)?(o.value=s.map(t=>({...t,actionDesc:_(t)})),console.log("使用数组格式，获取到",o.value.length,"条日志")):(o.value=[],console.warn("未获取到有效日志数据:",s))}catch(s){console.error("获取日志失败:",s),h.value=s.message||"获取日志时发生错误",i.error("获取日志失败: "+(s.message||"未知错误"))}finally{w.value=!1}}function _(s){if(s.action&&s.resource)return`${s.action}（${s.resource}）`;if(s.details&&typeof s.details=="object"){if(s.details.newStatus!==void 0)return`状态变更为：${s.details.newStatus?"激活":"停用"}`;if(s.details.username&&s.details.email)return`用户信息：${s.details.username}（${s.details.email}）`}return s.action||"操作"}function $(s){d.value=s,g.value=!0}async function S(){if(d.value)try{console.log("删除日志:",d.value.id),await y.delete(`/api/admin/logs/${d.value.id}`),o.value=o.value.filter(s=>s.id!==d.value.id),i.success("日志删除成功")}catch(s){console.error("删除日志失败:",s),i.error(s.message||"删除日志失败")}finally{g.value=!1,d.value=null}}async function A(){try{console.log("准备删除所有日志..."),await y.delete("/api/admin/logs/all/confirm"),console.log("日志删除成功"),o.value=[],i.success("所有日志已成功删除"),x.value=!1,await m()}catch(s){console.error("删除日志失败:",s.message),i.error(s.message||"删除日志时发生错误")}}function k(s){a.value.page=s,m()}return N(()=>{if(!localStorage.getItem("adminToken")){i.error("未找到登录凭证，请先登录"),setTimeout(()=>{window.location.href="/admin/login"},1e3);return}m()}),(s,t)=>{const j=L;return p(),u("div",I,[C(B),C(j,null,{default:P(()=>[e("div",M,[e("div",E,[e("div",q,[t[15]||(t[15]=e("h1",{class:"text-2xl font-bold mb-8 text-center"},"管理日志",-1)),e("div",G,[e("div",null,[t[9]||(t[9]=e("label",{class:"block text-gray-700 text-sm font-bold mb-1"},"用户名",-1)),f(e("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>n.value.username=l),type:"text",placeholder:"用户名",class:"px-3 py-2 border rounded-lg"},null,512),[[v,n.value.username]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-gray-700 text-sm font-bold mb-1"},"邮箱",-1)),f(e("input",{"onUpdate:modelValue":t[1]||(t[1]=l=>n.value.email=l),type:"text",placeholder:"邮箱",class:"px-3 py-2 border rounded-lg"},null,512),[[v,n.value.email]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-gray-700 text-sm font-bold mb-1"},"起始时间",-1)),f(e("input",{"onUpdate:modelValue":t[2]||(t[2]=l=>n.value.start=l),type:"datetime-local",class:"px-3 py-2 border rounded-lg"},null,512),[[v,n.value.start]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-gray-700 text-sm font-bold mb-1"},"结束时间",-1)),f(e("input",{"onUpdate:modelValue":t[3]||(t[3]=l=>n.value.end=l),type:"datetime-local",class:"px-3 py-2 border rounded-lg"},null,512),[[v,n.value.end]])]),e("button",{onClick:m,class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg ml-2"},"查询"),e("button",{onClick:t[4]||(t[4]=l=>x.value=!0),class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg ml-auto"}," 清空所有日志 ")]),e("div",H,[e("table",J,[t[14]||(t[14]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"},"用户名"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"},"邮箱"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"},"操作内容"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"},"操作时间"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"},"操作")])],-1)),e("tbody",K,[(p(!0),u(T,null,U(o.value,l=>(p(),u("tr",{key:l.id},[e("td",O,c(l.username||"-"),1),e("td",Q,c(l.email||"-"),1),e("td",R,c(l.actionDesc),1),e("td",W,c(D(l.timestamp)),1),e("td",X,[e("button",{onClick:ge=>$(l),class:"text-red-500 hover:text-red-700"}," 删除 ",8,Y)])]))),128)),o.value.length?b("",!0):(p(),u("tr",Z,t[13]||(t[13]=[e("td",{colspan:"5",class:"text-center text-gray-400 py-8"},"暂无日志数据",-1)])))])])]),e("div",ee,[e("div",te,[e("button",{onClick:t[5]||(t[5]=l=>k(a.value.page-1)),disabled:a.value.page===1,class:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50"}," 上一页 ",8,se),e("span",le," 第 "+c(a.value.page)+" 页 / 共 "+c(a.value.totalPages)+" 页 ",1),e("button",{onClick:t[6]||(t[6]=l=>k(a.value.page+1)),disabled:a.value.page===a.value.totalPages,class:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50"}," 下一页 ",8,oe)])])])])]),g.value?(p(),u("div",ae,[e("div",ne,[t[16]||(t[16]=e("h3",{class:"text-lg font-bold mb-4"},"确认删除",-1)),t[17]||(t[17]=e("p",{class:"mb-6"},"确定要删除这条日志记录吗？此操作不可恢复。",-1)),e("div",re,[e("button",{onClick:t[7]||(t[7]=l=>g.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"}," 取消 "),e("button",{onClick:S,class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"}," 确认删除 ")])])])):b("",!0),x.value?(p(),u("div",ie,[e("div",de,[t[18]||(t[18]=e("h3",{class:"text-lg font-bold mb-4"},"确认清空所有日志",-1)),t[19]||(t[19]=e("p",{class:"mb-2"},"确定要删除所有日志记录吗？此操作不可恢复。",-1)),t[20]||(t[20]=e("p",{class:"mb-6 text-red-500 font-semibold"},"此操作将以您当前登录的管理员身份记录。",-1)),e("div",ue,[e("button",{onClick:t[8]||(t[8]=l=>x.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"}," 取消 "),e("button",{onClick:A,class:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"}," 确认清空 ")])])])):b("",!0)]),_:1})])}}}),we=F(pe,[["__scopeId","data-v-0cb5022d"]]);export{we as default};
