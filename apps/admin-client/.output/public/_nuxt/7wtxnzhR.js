import{ad as e,a7 as t}from"./qi-a-hqW.js";const l=e((a,i)=>{if(console.log("[admin-auth] 检查管理员权限"),a.path!=="/admin/login"){const o=localStorage.getItem("adminToken"),n=localStorage.getItem("adminLoggedIn")==="true";if(console.log("[admin-auth] Token:",o?"存在":"不存在"),console.log("[admin-auth] LoggedIn:",n?"true":"false"),!o||!n)return console.warn("[admin-auth] 管理员未登录或token不存在，重定向到登录页"),t("/admin/login");console.log("[admin-auth] 管理员权限验证通过")}});export{l as default};
