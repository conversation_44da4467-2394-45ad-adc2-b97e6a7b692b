import{p as _,r as s,z as c,g as w,c as r,o as d,a as e,t as l}from"./qi-a-hqW.js";import{a as I}from"./CxzfvvNm.js";const J={class:"p-6"},L={class:"bg-white shadow rounded-lg p-6"},N={class:"mb-6"},T={class:"grid grid-cols-2 gap-2"},B={class:"mt-4"},C={class:"bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40"},O={class:"mb-6"},A={key:0},U={key:1,class:"text-red-600"},z={key:2},D={class:"bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40"},R={__name:"debug",setup(E){const f=_(),v=s(!1),g=s(!1),n=s(""),u=s(null),m=s(null),i=s(!1),a=s(""),b=c(()=>n.value?n.value.substring(0,10)+"...":"无"),x=c(()=>JSON.stringify(u.value,null,2)),h=c(()=>JSON.stringify(m.value,null,2)),y=()=>{if(typeof window<"u"){v.value=localStorage.getItem("adminLoggedIn")==="true",n.value=localStorage.getItem("adminToken")||"",g.value=!!n.value;try{const o=localStorage.getItem("adminUser");o&&(u.value=JSON.parse(o))}catch(o){console.error("解析用户信息失败:",o),u.value=null}}},p=async()=>{i.value=!0,a.value="";try{m.value=await I.get("/api/admin/content/debug")}catch(o){console.error("获取服务器状态失败:",o),a.value=o.message||"未知错误"}finally{i.value=!1}},k=()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),localStorage.removeItem("adminUser"),window.location.reload()},S=()=>{f.push("/admin/login")};return w(()=>{y(),p()}),(o,t)=>(d(),r("div",J,[t[7]||(t[7]=e("h1",{class:"text-2xl font-bold mb-6"},"认证调试",-1)),e("div",L,[t[6]||(t[6]=e("h2",{class:"text-xl font-bold mb-4"},"客户端认证状态",-1)),e("div",N,[e("div",T,[t[0]||(t[0]=e("div",{class:"font-bold"},"是否已登录:",-1)),e("div",null,l(v.value?"是":"否"),1),t[1]||(t[1]=e("div",{class:"font-bold"},"令牌存在:",-1)),e("div",null,l(g.value?"是":"否"),1),t[2]||(t[2]=e("div",{class:"font-bold"},"令牌长度:",-1)),e("div",null,l(n.value?n.value.length:0)+" 字符",1),t[3]||(t[3]=e("div",{class:"font-bold"},"令牌开头:",-1)),e("div",null,l(b.value),1)]),e("div",B,[t[4]||(t[4]=e("h3",{class:"font-bold mb-2"},"用户信息:",-1)),e("pre",C,l(x.value),1)])]),e("div",O,[t[5]||(t[5]=e("h3",{class:"font-bold mb-2"},"服务器认证状态:",-1)),i.value?(d(),r("div",A,"加载中...")):a.value?(d(),r("div",U,l(a.value),1)):(d(),r("div",z,[e("pre",D,l(h.value),1)]))]),e("div",{class:"flex space-x-2"},[e("button",{onClick:p,class:"bg-blue-600 text-white px-4 py-2 rounded"}," 检查服务器状态 "),e("button",{onClick:k,class:"bg-red-600 text-white px-4 py-2 rounded"}," 清除认证 "),e("button",{onClick:S,class:"bg-green-600 text-white px-4 py-2 rounded"}," 去登录 ")])])]))}};export{R as default};
