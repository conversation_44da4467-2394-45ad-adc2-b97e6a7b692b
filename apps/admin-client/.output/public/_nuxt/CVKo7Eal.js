import{a as _}from"./CxzfvvNm.js";import{A as k}from"./oUsj6fiR.js";import{e as A,r as c,g as C,c as o,o as r,b as S,a as e,h as u,t as l,F as y,j as f,n as D}from"./qi-a-hqW.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const T={class:"flex"},L={class:"flex-1 ml-64 p-8 flex flex-col"},N={key:0,class:"mb-6 p-4 bg-red-100 border border-red-300 text-red-700 rounded-lg w-full max-w-4xl self-center"},B={key:1,class:"mb-6 p-4 bg-blue-100 border border-blue-300 text-blue-700 rounded-lg w-full max-w-4xl self-center"},M={key:2,class:"w-full max-w-6xl self-center"},V={class:"bg-white shadow-md rounded-lg overflow-hidden mb-6"},$={class:"w-full"},E={class:"bg-white divide-y divide-gray-200"},F={class:"px-6 py-4 whitespace-nowrap"},R={class:"px-6 py-4 whitespace-nowrap"},j={class:"px-6 py-4 whitespace-nowrap"},z={class:"px-6 py-4 whitespace-nowrap"},I={class:"px-6 py-4 whitespace-nowrap"},J={key:0,class:"bg-white shadow-md rounded-lg p-6"},O={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},q={class:"font-semibold text-gray-700"},G={class:"text-2xl font-bold text-blue-600"},Y=A({__name:"activities",setup(H){const x=c([]),p=c(null),i=c(!0),n=c(""),b={login:"登录",logout:"注销",course_create:"创建课程",course_update:"更新课程",course_delete:"删除课程",vocabulary_add:"添加词汇",vocabulary_update:"更新词汇",vocabulary_delete:"删除词汇",statement_create:"创建句子",statement_update:"更新句子",statement_delete:"删除句子"};function g(t){return b[t]||t}function h(t){return{login:"text-green-600",logout:"text-red-600",course_create:"text-blue-600",course_update:"text-yellow-600",course_delete:"text-red-600",vocabulary_add:"text-purple-600",vocabulary_update:"text-indigo-600",vocabulary_delete:"text-pink-600"}[t]||"text-gray-600"}function v(t){return new Date(t).toLocaleString()}function w(t){var s,a,d;switch(t.type){case"login":return`用户 ${(s=t.user)==null?void 0:s.username} 登录`;case"course_create":return`创建了课程：${(a=t.details)==null?void 0:a.courseTitle}`;case"vocabulary_add":return`添加了词汇：${(d=t.details)==null?void 0:d.word}`;default:return JSON.stringify(t.details)}}async function m(){try{console.log("开始获取活动数据..."),i.value=!0,n.value="";const t=await _.get("/api/admin/activities"),s=await _.get("/api/admin/activities/stats");console.log("获取到的活动数据:",t),console.log("获取到的活动统计:",s),t&&Array.isArray(t)?x.value=t:(console.error("获取到空活动响应"),n.value="服务器返回了空数据"),s&&(p.value=s)}catch(t){console.error("获取活动数据失败:",t),n.value=t instanceof Error?t.message:"获取数据失败，请稍后再试"}finally{i.value=!1}}return C(()=>{console.log("活动管理页面已挂载，开始获取数据"),m()}),(t,s)=>(r(),o("div",T,[S(k),e("div",L,[s[3]||(s[3]=e("h1",{class:"text-3xl font-bold mb-6 text-gray-800 text-center w-full"},"系统活动",-1)),n.value?(r(),o("div",N,[e("p",null,l(n.value),1),e("button",{onClick:m,class:"mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"}," 重试 ")])):u("",!0),i.value?(r(),o("div",B,s[0]||(s[0]=[e("p",null,"正在加载活动数据...",-1)]))):u("",!0),!i.value&&!n.value?(r(),o("div",M,[e("div",V,[e("table",$,[s[1]||(s[1]=e("thead",{class:"bg-gray-100 border-b"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"活动ID"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"活动类型"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"用户"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"时间"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"详情")])],-1)),e("tbody",E,[(r(!0),o(y,null,f(x.value,a=>{var d;return r(),o("tr",{key:a.id},[e("td",F,l(a.id),1),e("td",R,[e("span",{class:D(h(a.type))},l(g(a.type)),3)]),e("td",j,l(((d=a.user)==null?void 0:d.username)||"系统"),1),e("td",z,l(v(a.timestamp)),1),e("td",I,l(w(a)),1)])}),128))])])]),p.value?(r(),o("div",J,[s[2]||(s[2]=e("h2",{class:"text-xl font-bold mb-4 text-gray-800"},"活动统计",-1)),e("div",O,[(r(!0),o(y,null,f(p.value.typeStats,a=>(r(),o("div",{key:a._id,class:"bg-gray-100 p-4 rounded-lg"},[e("h3",q,l(g(a._id)),1),e("p",G,l(a.count),1)]))),128))])])):u("",!0)])):u("",!0)])]))}});export{Y as default};
