import{_ as $,a as w}from"./DgOJsROy.js";import{a as _}from"./CxzfvvNm.js";import{e as B,r as c,g as P,c as a,o as l,a as t,I as b,h as j,F as y,j as k,t as C,n as F}from"./qi-a-hqW.js";import"./DlAUqK2U.js";import"./D0FjqKqG.js";import"./x_rD_Ya3.js";const M={class:"p-4"},N={class:"flex"},V={class:"w-1/3 border-r p-4"},z={key:0,class:"text-center py-4"},D={key:1,class:"text-center py-4"},E={key:2},I={class:"font-bold text-lg"},L={class:"pl-2 mt-2"},S={class:"flex justify-between items-center"},U=["onClick"],q={class:"flex space-x-1"},A=["onClick"],G=["onClick"],H={class:"flex-1 p-4"},J={key:0,class:"text-center py-8 text-gray-500"},Y=B({__name:"index",setup(K){const i=c([]),s=c(null),d=c(!1),u=c(!1),v=async()=>{u.value=!0;try{const e=await _.get("/api/admin/courses/tree");i.value=e||[]}catch(e){console.error("获取课程包失败:",e)}finally{u.value=!1}},p=e=>{s.value=e},x=e=>{p(e)},h=async e=>{if(confirm(`确定删除课程"${e.title}"吗？此操作不可恢复！`))try{await _.delete(`/api/admin/courses/${e.id}`),s.value&&s.value.id===e.id&&(s.value=null),await v()}catch(o){console.error("删除课程失败:",o),alert("删除课程失败，请重试")}},f=()=>{v()},g=async()=>{if(s.value)try{const e=await _.get(`/api/admin/courses/${s.value.id}`);e&&(s.value=e,f())}catch(e){console.error("刷新课程数据失败:",e)}};return P(v),(e,o)=>(l(),a("div",M,[o[4]||(o[4]=t("h1",{class:"text-2xl font-bold mb-4"},"课程管理",-1)),t("div",N,[t("div",V,[u.value?(l(),a("div",z,"加载中...")):i.value.length===0?(l(),a("div",D,o[2]||(o[2]=[t("p",null,"暂无课程包数据",-1)]))):(l(),a("div",E,[(l(!0),a(y,null,k(i.value,r=>(l(),a("div",{key:r.id,class:"mb-4"},[t("div",I,C(r.title),1),t("ul",L,[(l(!0),a(y,null,k(r.courses,n=>(l(),a("li",{key:n.id,class:F(["border-l-2 pl-3 py-1 mb-1 hover:bg-gray-50",{"border-blue-500 bg-blue-50":s.value&&s.value.id===n.id,"border-gray-200":!s.value||s.value.id!==n.id}])},[t("div",S,[t("span",{onClick:m=>p(n),class:"cursor-pointer flex-grow"},C(n.title),9,U),t("div",q,[t("button",{onClick:m=>x(n),class:"btn btn-xs btn-outline"},"编辑",8,A),t("button",{onClick:m=>h(n),class:"btn btn-xs btn-error"},"删除",8,G)])])],2))),128))])]))),128))])),t("button",{class:"mt-4 btn btn-primary",onClick:o[0]||(o[0]=r=>d.value=!0)},"创建新课程")]),t("div",H,[s.value?(l(),b($,{key:1,course:s.value,onUpdated:g},null,8,["course"])):(l(),a("div",J,o[3]||(o[3]=[t("p",null,"请从左侧选择一个课程查看详情",-1)])))])]),d.value?(l(),b(w,{key:0,onClose:o[1]||(o[1]=r=>d.value=!1),onCreated:f,coursePacks:i.value},null,8,["coursePacks"])):j("",!0)]))}});export{Y as default};
