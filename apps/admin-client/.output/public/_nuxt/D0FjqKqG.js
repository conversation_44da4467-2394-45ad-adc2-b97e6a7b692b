import{e as p,y as d,c as n,h as l,o as i,n as c,a as s,N as g,t as o,M as f}from"./qi-a-hqW.js";import{_ as v}from"./DlAUqK2U.js";const u={class:"loading-container"},m={class:"loading-text"},h={class:"loading-title"},_={class:"loading-message"},y={key:0,class:"loading-progress"},w={class:"progress-bar"},S={class:"progress-text"},B=p({__name:"LoadingSpinner",props:{show:{type:Boolean,default:!1},title:{default:"程序在加载中"},message:{default:"请稍候"},fullscreen:{type:Boolean,default:!0},showProgress:{type:Boolean,default:!1},progress:{default:0}},setup(r){const t=r;return d(()=>t.show,(e,a)=>{console.log(e?"✅ LoadingSpinner 显示":"❌ LoadingSpinner 隐藏")},{immediate:!0}),d(()=>t.showProgress,e=>{},{immediate:!0}),(e,a)=>e.show?(i(),n("div",{key:0,class:c(["loading-overlay",{"loading-overlay--fullscreen":e.fullscreen}])},[s("div",u,[a[1]||(a[1]=s("div",{class:"loading-spinner"},[s("div",{class:"spinner-ring"},[s("div"),s("div"),s("div"),s("div")])],-1)),s("div",m,[s("h3",h,o(e.title),1),s("p",_,o(e.message),1),a[0]||(a[0]=g('<div class="loading-dots" data-v-9ef416d4><span class="dot dot-1" data-v-9ef416d4>.</span><span class="dot dot-2" data-v-9ef416d4>.</span><span class="dot dot-3" data-v-9ef416d4>.</span><span class="dot dot-4" data-v-9ef416d4>.</span><span class="dot dot-5" data-v-9ef416d4>.</span></div>',1))]),e.showProgress?(i(),n("div",y,[s("div",w,[s("div",{class:"progress-fill",style:f({width:`${e.progress}%`})},null,4)]),s("div",S,o(Math.round(e.progress))+"%",1)])):l("",!0)])],2)):l("",!0)}}),k=v(B,[["__scopeId","data-v-9ef416d4"]]);export{k as L};
