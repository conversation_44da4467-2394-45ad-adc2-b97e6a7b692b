import{p as B,B as j,f as q,r as v,C as M,g as R,c as a,o as l,b as k,a as t,h as D,D as h,t as C,s as P,i as m,v as f,l as F,N as O,F as A,j as S}from"./qi-a-hqW.js";import{u as Q}from"./tJcwIiS2.js";import{a as W}from"./CxzfvvNm.js";import{A as X}from"./oUsj6fiR.js";import{Q as z}from"./47Na0wgx.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const G={class:"flex"},H={class:"flex-1 p-6"},J={class:"flex justify-between items-center mb-6"},K={class:"flex space-x-3"},Y={key:0,class:"text-center py-10"},Z={key:1,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"},tt={key:2,class:"bg-white shadow-md rounded p-6"},et={class:"mb-4"},ot={class:"mb-4"},st={class:"mb-4"},nt={class:"flex flex-wrap gap-2"},rt=["onUpdate:modelValue"],it=["onClick"],at={class:"mb-6"},lt={class:"mb-6"},dt={key:0,class:"border rounded-md overflow-hidden"},ut={class:"col-span-5"},ct=["onUpdate:modelValue"],pt={class:"col-span-5"},mt=["onUpdate:modelValue","onBlur"],bt={class:"col-span-2 flex items-center"},yt=["onClick"],gt={key:1,class:"text-gray-500 text-sm"},vt={class:"flex justify-end"},ft=["disabled"],xt={key:0},ht={key:1},wt={key:3,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4"},Ut={__name:"[id]",setup(_t){const b=B(),E=j(),V=Q(),d=q(),y=E.params.id,x=v(!0),g=v(!1),p=v(""),u=v(""),e=M({id:"",title:"",type:"",body:"",categories:[],annotations:[]}),w=async()=>{x.value=!0,p.value="";try{const n=await W.get(`/api/admin/content/${y}`);e.id=n.id,e.title=n.title||"",e.type=n.type||"",e.body=n.body||"",e.categories=Array.isArray(n.categories)?[...n.categories]:[],e.annotations=Array.isArray(n.annotations)?[...n.annotations]:[]}catch(n){p.value=n.message||"获取内容详情失败",d.error(p.value)}finally{x.value=!1}},$=()=>{e.categories.push("")},T=n=>{e.categories.splice(n,1)},U=()=>{Array.isArray(e.annotations)||(e.annotations=[]),e.annotations.push({word:"",pronunciation:""})},I=n=>{e.annotations.splice(n,1)},_=(n,o)=>{const s=n.pronunciation;s&&!s.startsWith("/")&&(d.warning('音标应以"/"开头，已自动添加'),n.pronunciation=`/${s}`),s&&!s.endsWith("/")&&(d.warning('音标应以"/"结尾，已自动添加'),n.pronunciation=`${n.pronunciation}/`)},L=({text:n,range:o,quill:s})=>{const i=n.trim();if(!i){d.warning("请选择要添加音标的单词");return}const r=e.annotations.find(c=>c.word.toLowerCase()===i.toLowerCase());if(r){const c=r.pronunciation;s.deleteText(o.index,o.length),s.insertText(o.index,i,{pronunciation:c}),d.success(`已为 "${i}" 添加音标: ${c}`)}else{const c={word:i,pronunciation:""};e.annotations.push(c),d.info(`已添加单词 "${i}" 到注解列表，请在下方补充音标信息`),setTimeout(()=>{document.querySelector(".col-span-5:last-child input").focus()},100)}},N=async()=>{var n,o,s,i;e.categories=e.categories.filter(r=>r.trim()!==""),Array.isArray(e.annotations)&&(e.annotations=e.annotations.filter(r=>r.word.trim()!==""&&r.pronunciation.trim()!==""),e.annotations.forEach((r,c)=>{_(r)})),g.value=!0,u.value="";try{if(console.log("准备保存内容:",{id:y,title:e.title,type:e.type,categories:e.categories,annotationsCount:((n=e.annotations)==null?void 0:n.length)||0,bodyLength:((o=e.body)==null?void 0:o.length)||0}),!((s=e.title)!=null&&s.trim()))throw new Error("标题不能为空");if(!e.type)throw new Error("请选择内容类型");if(!((i=e.body)!=null&&i.trim()))throw new Error("内容正文不能为空");console.log("开始保存内容...");const r=await V.updateContent(y,e);console.log("内容保存成功:",r),d.success("内容更新成功"),setTimeout(()=>{b.push("/admin/content")},500)}catch(r){console.error("保存内容失败:",r),u.value=r.message||"更新内容失败",d.error(u.value),(u.value.includes("认证失败")||u.value.includes("权限")||u.value.includes("令牌"))&&(d.error("认证失败，请重新登录",{timeout:5e3}),setTimeout(()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),localStorage.removeItem("adminUser"),b.push("/admin/login")},2e3))}finally{g.value=!1}};return R(()=>{w()}),(n,o)=>(l(),a("div",G,[k(X),t("div",H,[t("div",J,[o[5]||(o[5]=t("h1",{class:"text-2xl font-bold"},"编辑内容",-1)),t("div",K,[t("button",{onClick:o[0]||(o[0]=s=>h(b).push(`/admin/content/preview/${h(y)}`)),class:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"}," 预览 "),t("button",{onClick:o[1]||(o[1]=s=>h(b).push("/admin/content")),class:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"}," 返回列表 ")])]),x.value?(l(),a("div",Y,o[6]||(o[6]=[t("p",null,"加载中...",-1)]))):p.value?(l(),a("div",Z,[o[7]||(o[7]=t("p",{class:"font-bold"},"加载失败:",-1)),t("p",null,C(p.value),1),t("button",{onClick:w,class:"mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"}," 重试 ")])):(l(),a("div",tt,[t("form",{onSubmit:P(N,["prevent"])},[t("div",et,[o[8]||(o[8]=t("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-1"},"标题",-1)),m(t("input",{id:"title","onUpdate:modelValue":o[2]||(o[2]=s=>e.title=s),type:"text",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"输入内容标题",required:""},null,512),[[f,e.title]])]),t("div",ot,[o[10]||(o[10]=t("label",{for:"type",class:"block text-sm font-medium text-gray-700 mb-1"},"内容类型",-1)),m(t("select",{id:"type","onUpdate:modelValue":o[3]||(o[3]=s=>e.type=s),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",required:""},o[9]||(o[9]=[O('<option value="">请选择类型</option><option value="ARTICLE">文章</option><option value="LESSON">课程</option><option value="EXERCISE">练习</option><option value="NOTE">笔记</option>',5)]),512),[[F,e.type]])]),t("div",st,[o[11]||(o[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"栏目",-1)),t("div",nt,[(l(!0),a(A,null,S(e.categories,(s,i)=>(l(),a("div",{key:i,class:"flex items-center"},[m(t("input",{"onUpdate:modelValue":r=>e.categories[i]=r,type:"text",class:"border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 mr-2",placeholder:"栏目名称"},null,8,rt),[[f,e.categories[i]]]),t("button",{type:"button",onClick:r=>T(i),class:"text-red-500 hover:text-red-700"}," 删除 ",8,it)]))),128)),t("button",{type:"button",onClick:$,class:"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"}," 添加栏目 ")])]),t("div",at,[o[12]||(o[12]=t("label",{for:"body",class:"block text-sm font-medium text-gray-700 mb-1"},"内容正文",-1)),k(z,{content:e.body,"onUpdate:content":o[4]||(o[4]=s=>e.body=s),placeholder:"输入内容正文",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",onAddPronunciation:L},null,8,["content"])]),t("div",lt,[t("div",{class:"flex justify-between items-center mb-2"},[o[13]||(o[13]=t("label",{class:"block text-sm font-medium text-gray-700"},"单词注解",-1)),t("button",{type:"button",onClick:U,class:"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"}," 添加注解 ")]),o[15]||(o[15]=t("div",{class:"mb-2"},[t("p",{class:"text-sm text-gray-500"},"添加单词的发音注解，在预览时点击单词可以听到发音。音标格式示例：/həˈloʊ/、/ɡʊd/、/noʊ/")],-1)),e.annotations&&e.annotations.length>0?(l(),a("div",dt,[o[14]||(o[14]=t("div",{class:"grid grid-cols-12 bg-gray-50 border-b p-2"},[t("div",{class:"col-span-5 font-medium text-sm text-gray-700"},"单词"),t("div",{class:"col-span-5 font-medium text-sm text-gray-700"},"音标"),t("div",{class:"col-span-2 font-medium text-sm text-gray-700"},"操作")],-1)),(l(!0),a(A,null,S(e.annotations,(s,i)=>(l(),a("div",{key:i,class:"grid grid-cols-12 border-b p-2 last:border-b-0"},[t("div",ut,[m(t("input",{"onUpdate:modelValue":r=>s.word=r,type:"text",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"单词",required:""},null,8,ct),[[f,s.word]])]),t("div",pt,[m(t("input",{"onUpdate:modelValue":r=>s.pronunciation=r,type:"text",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"音标 (例如: /həˈloʊ/)",required:"",onBlur:r=>_(s)},null,40,mt),[[f,s.pronunciation]])]),t("div",bt,[t("button",{type:"button",onClick:r=>I(i),class:"text-red-500 hover:text-red-700 ml-2"}," 删除 ",8,yt)])]))),128))])):(l(),a("div",gt,' 暂无单词注解，点击"添加注解"按钮添加 '))]),t("div",vt,[t("button",{type:"submit",class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded",disabled:g.value},[g.value?(l(),a("span",xt,"保存中...")):(l(),a("span",ht,"保存内容"))],8,ft)])],32)])),u.value?(l(),a("div",wt,C(u.value),1)):D("",!0)])]))}};export{Ut as default};
