import{e as a,p as o,g as r,c as i,o as c,a as t}from"./qi-a-hqW.js";const d={class:"flex h-screen items-center justify-center"},p=a({__name:"index",setup(l){const n=o();return r(()=>{const s=localStorage.getItem("adminLoggedIn")==="true",e=localStorage.getItem("adminToken");s&&e?n.push("/admin/dashboard"):n.push("/admin/login")}),(s,e)=>(c(),i("div",d,e[0]||(e[0]=[t("div",{class:"text-center"},[t("span",{class:"i-ph-spinner animate-spin h-12 w-12 text-purple-600"}),t("p",{class:"mt-4 text-lg"},"正在检查管理员登录状态...")],-1)])))}});export{p as default};
