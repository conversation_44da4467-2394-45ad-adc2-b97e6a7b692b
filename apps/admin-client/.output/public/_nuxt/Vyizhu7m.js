import{r as u,z as R,g as I,c as n,o as r,b as P,a as t,F as h,j as w,t as o,i as S,l as E,h as C}from"./qi-a-hqW.js";import{u as g,a as U}from"./BMvTVg1A.js";import{A as T}from"./oUsj6fiR.js";import{_ as V}from"./DlAUqK2U.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";const $={class:"flex"},G={class:"flex-1 p-6 bg-gray-100 min-h-screen ml-64"},O={class:"message-management bg-white p-6 rounded-lg shadow"},B={class:"grid grid-cols-4 gap-4 mb-6"},F={class:"text-sm text-gray-500"},z={class:"text-2xl font-bold mt-2"},j={class:"filters mb-4 flex space-x-4"},K={class:"w-full border-collapse"},L={class:"border p-2"},Y={class:"border p-2 truncate max-w-xs"},q={class:"border p-2"},H={class:"border p-2"},J={class:"border p-2 space-x-2"},Q=["onClick"],W=["onClick"],X=["onClick"],Z={class:"pagination mt-4 flex justify-between items-center"},tt=["disabled"],et=["disabled"],st={__name:"index",setup(at){const y=u([]),l=u(1),_=u(10),v=u(1),d=u({type:"",status:""}),c=u({total:0,unread:0,processing:0,processed:0,typeDistribution:[]}),D=R(()=>[{label:"总消息数",value:c.value.total,color:"text-blue-600"},{label:"未读消息",value:c.value.unread,color:"text-red-600"},{label:"处理中",value:c.value.processing,color:"text-yellow-600"},{label:"已处理",value:c.value.processed,color:"text-green-600"}]),i=U();async function m(){try{const e=await g.get("/api/admin/messages/stats");c.value=e}catch{i.error("获取消息统计失败")}}async function p(){try{const e=await g.get("/api/admin/messages",{params:{page:l.value,pageSize:_.value,type:d.value.type||void 0,status:d.value.status||void 0}});y.value=e.messages,v.value=Math.ceil(e.total/_.value)}catch{i.error("获取消息列表失败")}}async function x(e,s){try{await g.patch(`/api/admin/messages/${e}/status`,{status:s}),i.success("状态更新成功"),await p(),await m()}catch{i.error("更新状态失败")}}async function k(e){try{await g.delete(`/api/admin/messages/${e}`),i.success("消息删除成功"),await p(),await m()}catch{i.error("删除消息失败")}}function A(e){return{FEEDBACK:"反馈",SUGGESTION:"建议",ACTIVITY_SIGNUP:"活动报名"}[e]||e}function M(e){return{UNREAD:"未读",READ:"已读",PROCESSED:"已处理"}[e]||e}function N(e){return new Date(e).toLocaleString()}function f(e){e>=1&&e<=v.value&&(l.value=e,p())}return I(async()=>{await p(),await m()}),(e,s)=>(r(),n("div",$,[P(T),t("div",G,[t("div",O,[s[7]||(s[7]=t("h1",{class:"text-2xl font-bold mb-6"},"用户留言管理",-1)),t("div",B,[(r(!0),n(h,null,w(D.value,(a,b)=>(r(),n("div",{key:b,class:"bg-gray-50 p-4 rounded-lg shadow-sm"},[t("div",F,o(a.label),1),t("div",z,o(a.value),1)]))),128))]),t("div",j,[S(t("select",{"onUpdate:modelValue":s[0]||(s[0]=a=>d.value.type=a),class:"p-2 border rounded"},s[4]||(s[4]=[t("option",{value:""},"全部类型",-1),t("option",{value:"FEEDBACK"},"反馈",-1),t("option",{value:"SUGGESTION"},"建议",-1),t("option",{value:"ACTIVITY_SIGNUP"},"活动报名",-1)]),512),[[E,d.value.type]]),S(t("select",{"onUpdate:modelValue":s[1]||(s[1]=a=>d.value.status=a),class:"p-2 border rounded"},s[5]||(s[5]=[t("option",{value:""},"全部状态",-1),t("option",{value:"UNREAD"},"未读",-1),t("option",{value:"READ"},"已读",-1),t("option",{value:"PROCESSED"},"已处理",-1)]),512),[[E,d.value.status]]),t("button",{onClick:p,class:"bg-blue-500 text-white px-4 py-2 rounded"}," 筛选 ")]),t("table",K,[s[6]||(s[6]=t("thead",null,[t("tr",{class:"bg-gray-100"},[t("th",{class:"border p-2"},"类型"),t("th",{class:"border p-2"},"内容"),t("th",{class:"border p-2"},"提交时间"),t("th",{class:"border p-2"},"状态"),t("th",{class:"border p-2"},"操作")])],-1)),t("tbody",null,[(r(!0),n(h,null,w(y.value,a=>(r(),n("tr",{key:a.id,class:"hover:bg-gray-50"},[t("td",L,o(A(a.type)),1),t("td",Y,o(a.content),1),t("td",q,o(N(a.createdAt)),1),t("td",H,o(M(a.status)),1),t("td",J,[a.status==="UNREAD"?(r(),n("button",{key:0,onClick:b=>x(a.id,"READ"),class:"bg-green-500 text-white px-2 py-1 rounded text-sm"}," 标记已读 ",8,Q)):C("",!0),a.status!=="PROCESSED"?(r(),n("button",{key:1,onClick:b=>x(a.id,"PROCESSED"),class:"bg-blue-500 text-white px-2 py-1 rounded text-sm"}," 标记处理 ",8,W)):C("",!0),t("button",{onClick:b=>k(a.id),class:"bg-red-500 text-white px-2 py-1 rounded text-sm"}," 删除 ",8,X)])]))),128))])]),t("div",Z,[t("button",{disabled:l.value===1,onClick:s[2]||(s[2]=a=>f(l.value-1)),class:"bg-gray-200 px-4 py-2 rounded disabled:opacity-50"}," 上一页 ",8,tt),t("span",null,"第 "+o(l.value)+" 页，共 "+o(v.value)+" 页",1),t("button",{disabled:l.value>=v.value,onClick:s[3]||(s[3]=a=>f(l.value+1)),class:"bg-gray-200 px-4 py-2 rounded disabled:opacity-50"}," 下一页 ",8,et)])])])]))}},ut=V(st,[["__scopeId","data-v-7ccde3e3"]]);export{ut as default};
