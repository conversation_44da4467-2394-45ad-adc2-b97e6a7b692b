import{r as c,y as P,g as M,c as l,o as d,a as t,F as h,j as S,t as n,i as C,l as D,h as w}from"./qi-a-hqW.js";import{u as v,a as N}from"./BMvTVg1A.js";import{_ as U}from"./DlAUqK2U.js";const I={class:"message-management p-6"},O={class:"grid grid-cols-3 gap-4 mb-6"},T={class:"text-xl font-bold"},$={class:"text-3xl font-bold text-blue-600"},G={class:"filters mb-4 flex space-x-4"},V={class:"w-full border-collapse"},B={class:"border p-2"},F={class:"border p-2 truncate max-w-xs"},j={class:"border p-2"},z={class:"border p-2"},K={class:"border p-2 space-x-2"},L=["onClick"],Y=["onClick"],q=["onClick"],H={class:"pagination mt-4 flex justify-between items-center"},J=["disabled"],Q=["disabled"],W={__name:"index",setup(X){const y=c([]),a=c(1),m=c(10),b=c(1),r=c({type:"",status:""}),_=c({UNREAD:0,READ:0,PROCESSED:0}),i=N();async function g(){try{const e=await v.get("/admin/messages/stats");_.value={UNREAD:e.unread,READ:e.processing,PROCESSED:e.processed}}catch(e){console.error("获取消息统计失败",e),i.error("获取消息统计失败")}}async function u(){try{const e=await v.get("/admin/messages",{params:{page:a.value,pageSize:m.value,type:r.value.type||void 0,status:r.value.status||void 0}});y.value=e.messages,b.value=Math.ceil(e.total/m.value)}catch(e){console.error("获取消息列表失败",e),i.error("获取消息列表失败")}}async function x(e,s){try{await v.patch(`/admin/messages/${e}/status`,{status:s}),i.success("状态更新成功"),await Promise.all([u(),g()])}catch(o){console.error("更新状态失败",o),i.error("更新状态失败")}}async function A(e){try{await v.delete(`/admin/messages/${e}`),i.success("消息删除成功"),await Promise.all([u(),g()])}catch(s){console.error("删除消息失败",s),i.error("删除消息失败")}}function f(e){a.value=e,u()}function R(e){return{FEEDBACK:"反馈",SUGGESTION:"建议",ACTIVITY_SIGNUP:"活动报名"}[e]||e}function E(e){return{UNREAD:"未读消息",READ:"已读消息",PROCESSED:"已处理消息"}[e]||e}function k(e){return new Date(e).toLocaleString()}return P(r,()=>{a.value=1,u()}),M(()=>{Promise.all([u(),g()])}),(e,s)=>(d(),l("div",I,[s[7]||(s[7]=t("h1",{class:"text-2xl font-bold mb-6"},"用户留言管理",-1)),t("div",O,[(d(!0),l(h,null,S(_.value,(o,p)=>(d(),l("div",{key:p,class:"bg-white shadow rounded-lg p-4 text-center"},[t("div",T,n(E(p)),1),t("div",$,n(o),1)]))),128))]),t("div",G,[C(t("select",{"onUpdate:modelValue":s[0]||(s[0]=o=>r.value.type=o),class:"p-2 border rounded"},s[4]||(s[4]=[t("option",{value:""},"全部类型",-1),t("option",{value:"FEEDBACK"},"反馈",-1),t("option",{value:"SUGGESTION"},"建议",-1),t("option",{value:"ACTIVITY_SIGNUP"},"活动报名",-1)]),512),[[D,r.value.type]]),C(t("select",{"onUpdate:modelValue":s[1]||(s[1]=o=>r.value.status=o),class:"p-2 border rounded"},s[5]||(s[5]=[t("option",{value:""},"全部状态",-1),t("option",{value:"UNREAD"},"未读",-1),t("option",{value:"READ"},"已读",-1),t("option",{value:"PROCESSED"},"已处理",-1)]),512),[[D,r.value.status]]),t("button",{onClick:u,class:"bg-blue-500 text-white px-4 py-2 rounded"}," 筛选 ")]),t("table",V,[s[6]||(s[6]=t("thead",null,[t("tr",{class:"bg-gray-100"},[t("th",{class:"border p-2"},"类型"),t("th",{class:"border p-2"},"内容"),t("th",{class:"border p-2"},"提交时间"),t("th",{class:"border p-2"},"状态"),t("th",{class:"border p-2"},"操作")])],-1)),t("tbody",null,[(d(!0),l(h,null,S(y.value,o=>(d(),l("tr",{key:o.id,class:"hover:bg-gray-50"},[t("td",B,n(R(o.type)),1),t("td",F,n(o.content),1),t("td",j,n(k(o.createdAt)),1),t("td",z,n(E(o.status)),1),t("td",K,[o.status==="UNREAD"?(d(),l("button",{key:0,onClick:p=>x(o.id,"READ"),class:"bg-green-500 text-white px-2 py-1 rounded text-sm"}," 标记已读 ",8,L)):w("",!0),o.status!=="PROCESSED"?(d(),l("button",{key:1,onClick:p=>x(o.id,"PROCESSED"),class:"bg-blue-500 text-white px-2 py-1 rounded text-sm"}," 标记处理 ",8,Y)):w("",!0),t("button",{onClick:p=>A(o.id),class:"bg-red-500 text-white px-2 py-1 rounded text-sm"}," 删除 ",8,q)])]))),128))])]),t("div",H,[t("button",{disabled:a.value===1,onClick:s[2]||(s[2]=o=>f(a.value-1)),class:"bg-gray-200 px-4 py-2 rounded disabled:opacity-50"}," 上一页 ",8,J),t("span",null,"第 "+n(a.value)+" 页，共 "+n(b.value)+" 页",1),t("button",{disabled:a.value>=b.value,onClick:s[3]||(s[3]=o=>f(a.value+1)),class:"bg-gray-200 px-4 py-2 rounded disabled:opacity-50"}," 下一页 ",8,Q)])]))}},st=U(W,[["__scopeId","data-v-12633087"]]);export{st as default};
