var u=Object.defineProperty;var d=(t,e,r)=>e in t?u(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var i=(t,e,r)=>d(t,typeof e!="symbol"?e+"":e,r);import{W as h}from"./qi-a-hqW.js";const o=class o{constructor(){}static getInstance(){return o.instance||(o.instance=new o),o.instance}createToast(e,r,s=3e3){const n=document.createElement("div");n.textContent=e;const p={success:"#4CAF50",error:"#F44336",warning:"#FF9800",info:"#2196F3"};n.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: ${p[r]};
      color: white;
      padding: 15px;
      border-radius: 5px;
      z-index: 1000;
      transition: opacity 0.3s;
    `,document.body.appendChild(n),setTimeout(()=>{n.style.opacity="0",setTimeout(()=>{document.body.removeChild(n)},300)},s)}success(e,r=3e3){this.createToast(e,"success",r)}error(e,r=3e3){this.createToast(e,"error",r)}warning(e,r=3e3){this.createToast(e,"warning",r)}info(e,r=3e3){this.createToast(e,"info",r)}};i(o,"instance");let c=o;function l(){return c.getInstance()}const m="http://localhost:5000",a=h.create({baseURL:m,timeout:1e4});a.interceptors.request.use(t=>{const e=localStorage.getItem("adminToken");return e&&(t.headers.Authorization=e.startsWith("Bearer ")?e:`Bearer ${e}`),t},t=>Promise.reject(t));a.interceptors.response.use(t=>t,t=>{const e=l(),{response:r,message:s}=t;if(r)switch(r.status){case 400:e.error("请求错误");break;case 401:e.error("未授权，请重新登录");break;case 403:e.error("拒绝访问");break;case 404:e.error("请求地址不存在");break;case 500:e.error("服务器内部错误");break;default:e.error(`连接错误 ${r.status}`)}else s==="Network Error"?e.error("网络连接失败"):e.error("未知错误");return Promise.reject(t)});const T={async get(t,e={}){try{return(await a.get(t,e)).data}catch(r){throw console.error("API GET Error:",r),r}},async post(t,e={},r={}){try{return(await a.post(t,e,r)).data}catch(s){throw console.error("API POST Error:",s),s}},async patch(t,e={},r={}){try{return(await a.patch(t,e,r)).data}catch(s){throw console.error("API PATCH Error:",s),s}},async delete(t,e={}){try{return(await a.delete(t,e)).data}catch(r){throw console.error("API DELETE Error:",r),r}}};export{l as a,T as u};
