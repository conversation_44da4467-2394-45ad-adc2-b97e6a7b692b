import{r as d,g as r,c as i,o as c,a as e,d as g,t as m}from"./qi-a-hqW.js";const u={class:"min-h-screen flex items-center justify-center bg-red-50"},p={class:"w-full max-w-md"},b={class:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4"},x={class:"mb-4 p-4 bg-gray-100 rounded"},h={__name:"reset",setup(f){const o=d(!1),s=()=>{{const n=localStorage.getItem("adminToken"),t=localStorage.getItem("adminLoggedIn");o.value=!!(n&&t==="true")}},l=()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),document.cookie="adminToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",console.log("[Reset] 登录状态已完全重置"),s()},a=()=>{window.location.href="/admin/login"};return r(()=>{s()}),(n,t)=>(c(),i("div",u,[e("div",p,[e("div",b,[t[2]||(t[2]=e("div",{class:"mb-6 text-center"},[e("h1",{class:"text-3xl font-bold text-red-600"},"登录状态重置"),e("p",{class:"text-gray-600 mt-2"},"清除所有管理后台登录状态")],-1)),e("div",x,[t[1]||(t[1]=e("h2",{class:"font-bold mb-2"},"当前状态:",-1)),e("p",null,[t[0]||(t[0]=g("登录状态: ",-1)),e("strong",null,m(o.value?"已登录":"未登录"),1)])]),e("div",{class:"flex flex-col space-y-2 mt-6"},[e("button",{onClick:l,class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"}," 彻底重置登录状态 "),e("button",{onClick:a,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4"}," 返回登录页 ")]),t[3]||(t[3]=e("div",{class:"mt-6 text-sm text-gray-600"},[e("p",null,"如果你遇到登录问题，此工具可以彻底清除登录状态")],-1))])])]))}};export{h as default};
