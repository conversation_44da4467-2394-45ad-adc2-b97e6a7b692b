import{p as q,f as G,r,z as L,y as $,g as H,c as l,o as n,a as t,h as z,i as B,v as K,l as Q,N as E,t as i,F as I,j as D,n as p,d as w}from"./qi-a-hqW.js";import{a as T}from"./CxzfvvNm.js";const W={class:"p-6"},Y={class:"flex justify-between mb-6"},Z={class:"flex space-x-2"},ee={class:"relative"},te={key:0,class:"mb-6 p-4 bg-gray-100 border border-gray-300 rounded"},se={class:"text-xs overflow-auto max-h-40"},ae={key:1,class:"text-center py-10"},oe={key:2,class:"text-center py-10"},le={key:0,class:"mt-4 p-3 bg-red-100 text-red-700 rounded-md text-sm max-w-lg mx-auto"},ne={key:3,class:"bg-white shadow rounded-lg overflow-hidden"},re={class:"min-w-full divide-y divide-gray-200"},ie={class:"bg-white divide-y divide-gray-200"},de={class:"px-6 py-4 whitespace-nowrap"},ue={class:"px-6 py-4 whitespace-nowrap"},pe={class:"px-6 py-4 whitespace-nowrap"},ve={class:"px-6 py-4 whitespace-nowrap"},ce={class:"px-6 py-4 whitespace-nowrap"},ge=["onClick"],xe=["onClick"],me={class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},ye={class:"flex-1 flex justify-between sm:hidden"},be=["disabled"],he=["disabled"],we={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},fe={class:"text-sm text-gray-700"},ke={class:"font-medium"},Ce={class:"font-medium"},_e={class:"font-medium"},Se={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},Le=["disabled"],ze=["disabled"],Ee=["onClick"],Ie={key:1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},Te=["disabled"],Me=["disabled"],Oe={__name:"index",setup(Ne){const M=q(),f=G(),v=r([]),c=r(""),k=r(!1),g=r(""),x=r(!1),C=r({}),u=r(""),_=r(null),o=r(1),m=r(10),h=r(0),O=()=>{x.value=!x.value,x.value&&N()},N=async()=>{try{const s=localStorage.getItem("adminToken"),e=JSON.parse(localStorage.getItem("adminUser")||"{}"),a=await T.get("/api/admin/content/debug");C.value={client:{hasToken:!!s,tokenLength:s?s.length:0,tokenStart:s?s.substring(0,10)+"...":null,user:e,isLoggedIn:localStorage.getItem("adminLoggedIn")==="true"},server:a,timestamp:new Date().toISOString()}}catch(s){C.value={error:s.message,timestamp:new Date().toISOString()}}},y=async()=>{k.value=!0,g.value="";try{const s=new URLSearchParams;s.append("page",o.value.toString()),s.append("pageSize",m.value.toString()),c.value&&s.append("type",c.value),u.value&&s.append("category",u.value);const e=await T.get(`/api/admin/content?${s.toString()}`);if(v.value=e.contents||[],e.pagination){const{total:a,page:b,pageSize:S,pageCount:$e}=e.pagination;o.value=b,m.value=S,h.value=a}}catch(s){console.error("Error fetching content:",s),g.value=s.message||"未知错误",f.error("获取内容列表失败: "+g.value)}finally{k.value=!1}},j=()=>{_.value&&clearTimeout(_.value),_.value=setTimeout(()=>{o.value=1,y()},300)},R=()=>{u.value="",o.value=1,y()},d=L(()=>Math.max(1,Math.ceil(h.value/m.value))),V=L(()=>v.value),A=L(()=>{const s=d.value,e=o.value;if(s<=7)return Array.from({length:s},(b,S)=>S+1);const a=[];return a.push(1),e<=3?a.push(2,3,4,"...",s-1,s):e>=s-2?a.push("...",s-4,s-3,s-2,s-1,s):a.push("...",e-1,e,e+1,"...",s),a}),P=s=>({ARTICLE:"文章",LESSON:"课程",EXERCISE:"练习",NOTE:"笔记"})[s]||s,U=s=>new Date(s).toLocaleString("zh-CN"),F=()=>{M.push("/admin/content/new")},X=s=>{M.push(`/admin/content/edit/${s.id}`)},J=async s=>{if(confirm("确定要删除这个内容吗？"))try{await T.delete(`/api/admin/content/${s}`),v.value=v.value.filter(e=>e.id!==s),f.success("内容已成功删除")}catch(e){console.error("Error deleting content:",e),f.error("删除内容失败: "+(e.message||"未知错误"))}};return $([c],()=>{o.value=1}),$([o,c,u],()=>{y()},{deep:!0}),H(()=>{y()}),(s,e)=>(n(),l("div",W,[e[22]||(e[22]=t("h1",{class:"text-2xl font-bold mb-6"},"内容管理",-1)),t("div",Y,[t("div",null,[t("button",{onClick:F,class:"bg-blue-600 text-white px-4 py-2 rounded"}," 创建内容 ")]),t("div",Z,[t("div",ee,[B(t("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>u.value=a),type:"text",placeholder:"搜索栏目...",class:"border rounded px-3 py-2 pr-8",onInput:j},null,544),[[K,u.value]]),u.value?(n(),l("span",{key:0,onClick:R,class:"absolute right-2 top-2 cursor-pointer text-gray-500"}," × ")):z("",!0)]),B(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>c.value=a),class:"border rounded px-3 py-2"},e[8]||(e[8]=[E('<option value="">全部类型</option><option value="ARTICLE">文章</option><option value="LESSON">课程</option><option value="EXERCISE">练习</option><option value="NOTE">笔记</option>',5)]),512),[[Q,c.value]])])]),x.value?(n(),l("div",te,[e[9]||(e[9]=t("h3",{class:"font-bold mb-2"},"调试信息",-1)),t("pre",se,i(C.value),1),t("button",{onClick:N,class:"mt-2 bg-gray-500 text-white px-2 py-1 text-xs rounded"}," 刷新状态 ")])):z("",!0),k.value?(n(),l("div",ae,e[10]||(e[10]=[t("p",null,"加载中...",-1)]))):v.value.length===0?(n(),l("div",oe,[e[12]||(e[12]=t("p",null,"暂无内容",-1)),g.value?(n(),l("div",le,[e[11]||(e[11]=t("p",{class:"font-bold"},"加载失败:",-1)),t("p",null,i(g.value),1),t("button",{onClick:y,class:"mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"}," 重试 "),t("button",{onClick:O,class:"mt-2 ml-2 bg-gray-600 text-white px-2 py-1 rounded text-xs"},i(x.value?"隐藏调试":"显示调试"),1)])):z("",!0)])):(n(),l("div",ne,[t("table",re,[e[13]||(e[13]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"标题"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"类型"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"栏目"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"创建时间"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"操作")])],-1)),t("tbody",ie,[(n(!0),l(I,null,D(V.value,a=>(n(),l("tr",{key:a.id},[t("td",de,i(a.title||"无标题"),1),t("td",ue,i(P(a.type)),1),t("td",pe,i(a.categories?a.categories.join(", "):"-"),1),t("td",ve,i(U(a.createdAt)),1),t("td",ce,[t("button",{onClick:b=>X(a),class:"text-blue-600 hover:text-blue-900 mr-3"},"编辑",8,ge),t("button",{onClick:b=>J(a.id),class:"text-red-600 hover:text-red-900"},"删除",8,xe)])]))),128))])]),t("div",me,[t("div",ye,[t("button",{onClick:e[2]||(e[2]=a=>o.value>1&&o.value--),disabled:o.value===1,class:p(["relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])}," 上一页 ",10,be),t("button",{onClick:e[3]||(e[3]=a=>o.value<d.value&&o.value++),disabled:o.value===d.value,class:p(["ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===d.value}])}," 下一页 ",10,he)]),t("div",we,[t("div",null,[t("p",fe,[e[14]||(e[14]=w(" 显示第 ",-1)),t("span",ke,i(v.value.length>0?(o.value-1)*m.value+1:0),1),e[15]||(e[15]=w(" 至 ",-1)),t("span",Ce,i(Math.min(o.value*m.value,h.value)),1),e[16]||(e[16]=w(" 条，共 ",-1)),t("span",_e,i(h.value),1),e[17]||(e[17]=w(" 条 ",-1))])]),t("div",null,[t("nav",Se,[t("button",{onClick:e[4]||(e[4]=a=>o.value=1),disabled:o.value===1,class:p(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])},e[18]||(e[18]=[E('<span class="sr-only">首页</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',3)]),10,Le),t("button",{onClick:e[5]||(e[5]=a=>o.value>1&&o.value--),disabled:o.value===1,class:p(["relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])},e[19]||(e[19]=[t("span",{class:"sr-only"},"上一页",-1),t("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,ze),(n(!0),l(I,null,D(A.value,a=>(n(),l(I,{key:a},[a!=="..."?(n(),l("button",{key:0,onClick:b=>o.value=a,class:p([o.value===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},i(a),11,Ee)):(n(),l("span",Ie," ... "))],64))),128)),t("button",{onClick:e[6]||(e[6]=a=>o.value<d.value&&o.value++),disabled:o.value===d.value,class:p(["relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===d.value}])},e[20]||(e[20]=[t("span",{class:"sr-only"},"下一页",-1),t("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,Te),t("button",{onClick:e[7]||(e[7]=a=>o.value=d.value),disabled:o.value===d.value,class:p(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===d.value}])},e[21]||(e[21]=[E('<span class="sr-only">尾页</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>',3)]),10,Me)])])])])]))]))}};export{Oe as default};
