const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BD6aUi4i.js","./qi-a-hqW.js","./entry.DXiz2RTu.css"])))=>i.map(i=>d[i]);
import{e as m,r as c,g,c as i,o as d,b as t,a,h as C,t as p,D as u,O as b,_}from"./qi-a-hqW.js";import{A as f}from"./oUsj6fiR.js";import{a as x}from"./CxzfvvNm.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const y={class:"flex"},h={class:"p-8"},A={key:0,class:"mb-6 p-4 bg-red-100 border border-red-300 text-red-700 rounded-lg w-full max-w-4xl"},k={key:1,class:"mb-6 p-4 bg-blue-100 border border-blue-300 text-blue-700 rounded-lg w-full max-w-4xl"},w={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6 w-full max-w-6xl"},O=m({__name:"dashboard",setup(P){const l=b(()=>_(()=>import("./BD6aUi4i.js"),__vite__mapDeps([0,1,2]),import.meta.url)),o=c({userCount:0,coursePackCount:0,courseCount:0,vocabularyCount:0,messageCount:0,articleCount:0,generatedAudioCount:0,reusedAudioCount:0}),s=c(!0),r=c("");async function v(){try{console.log("开始获取仪表盘数据..."),s.value=!0,r.value="";const e=await x.get("/api/admin/dashboard");console.log("获取到的仪表盘数据:",e),e?(o.value={userCount:e.userCount||0,coursePackCount:e.coursePackCount||0,courseCount:e.courseCount||0,vocabularyCount:e.vocabularyCount||0,messageCount:e.messageCount||0,articleCount:e.articleCount||0,generatedAudioCount:e.generatedAudioCount||0,reusedAudioCount:e.reusedAudioCount||0},console.log("仪表盘数据已更新:",o.value)):(console.error("获取到空响应"),r.value="服务器返回了空数据")}catch(e){console.error("获取仪表盘数据失败:",e),r.value=e instanceof Error?e.message:"获取数据失败，请稍后再试"}finally{s.value=!1}}return g(()=>{console.log("仪表盘组件已挂载，开始获取数据"),v()}),(e,n)=>(d(),i("div",y,[t(f),a("div",h,[n[1]||(n[1]=a("h1",{class:"text-3xl font-bold mb-6 text-gray-800 text-center w-full"},"管理后台仪表盘",-1)),r.value?(d(),i("div",A,[a("p",null,p(r.value),1),a("button",{onClick:v,class:"mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"}," 重试 ")])):C("",!0),s.value?(d(),i("div",k,n[0]||(n[0]=[a("p",null,"正在加载数据...",-1)]))):C("",!0),a("div",w,[t(u(l),{title:"用户总数",value:o.value.userCount,icon:"👤",color:"blue"},null,8,["value"]),t(u(l),{title:"课程包数量",value:o.value.coursePackCount,icon:"📚",color:"green"},null,8,["value"]),t(u(l),{title:"课程总数",value:o.value.courseCount,icon:"📖",color:"purple"},null,8,["value"]),t(u(l),{title:"词汇总数",value:o.value.vocabularyCount,icon:"🔤",color:"orange"},null,8,["value"]),t(u(l),{title:"留言总数",value:o.value.messageCount,icon:"💬",color:"red"},null,8,["value"]),t(u(l),{title:"文章数量",value:o.value.articleCount,icon:"📝",color:"yellow"},null,8,["value"]),t(u(l),{title:"生成音频",value:o.value.generatedAudioCount,icon:"🎵",color:"indigo"},null,8,["value"]),t(u(l),{title:"复用音频",value:o.value.reusedAudioCount,icon:"🔄",color:"pink"},null,8,["value"])])])]))}});export{O as default};
