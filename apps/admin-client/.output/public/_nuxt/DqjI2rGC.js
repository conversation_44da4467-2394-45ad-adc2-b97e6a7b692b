import{e as T,a0 as P,Y as j,r as E,X as A,g as N,a1 as B,P as U,Q as _,E as L,a2 as I,a3 as k,a4 as D,z as p,a5 as O,a6 as F,K as H,a7 as V,a8 as z,a9 as M,aa as W,ab as Q,ac as $}from"./qi-a-hqW.js";const G=(...t)=>t.find(o=>o!==void 0);function K(t){const o=t.componentName||"NuxtLink";function y(e){return typeof e=="string"&&e.startsWith("#")}function C(e,n,h){const a=h??t.trailingSlash;if(!e||a!=="append"&&a!=="remove")return e;if(typeof e=="string")return R(e,a);const i="path"in e&&e.path!==void 0?e.path:n(e).path;return{...e,name:void 0,path:R(i,a)}}function q(e){const n=P(),h=H(),a=p(()=>!!e.target&&e.target!=="_self"),i=p(()=>{const f=e.to||e.href||"";return typeof f=="string"&&O(f,{acceptRelative:!0})}),x=L("RouterLink"),g=x&&typeof x!="string"?x.useLink:void 0,d=p(()=>{if(e.external)return!0;const f=e.to||e.href||"";return typeof f=="object"?!1:f===""||i.value}),l=p(()=>{const f=e.to||e.href||"";return d.value?f:C(f,n.resolve,e.trailingSlash)}),c=d.value||g==null?void 0:g({...e,to:l}),b=p(()=>{var S;const f=e.trailingSlash??t.trailingSlash;if(!l.value||i.value||y(l.value))return l.value;if(d.value){const m=typeof l.value=="object"&&"path"in l.value?k(l.value):l.value,r=typeof m=="object"?n.resolve(m).href:m;return R(r,f)}return typeof l.value=="object"?((S=n.resolve(l.value))==null?void 0:S.href)??null:R(F(h.app.baseURL,l.value),f)});return{to:l,hasTarget:a,isAbsoluteUrl:i,isExternal:d,href:b,isActive:(c==null?void 0:c.isActive)??p(()=>l.value===n.currentRoute.value.path),isExactActive:(c==null?void 0:c.isExactActive)??p(()=>l.value===n.currentRoute.value.path),route:(c==null?void 0:c.route)??p(()=>n.resolve(l.value)),async navigate(f){await V(b.value,{replace:e.replace,external:d.value||a.value})}}}return T({name:o,props:{to:{type:[String,Object],default:void 0,required:!1},href:{type:[String,Object],default:void 0,required:!1},target:{type:String,default:void 0,required:!1},rel:{type:String,default:void 0,required:!1},noRel:{type:Boolean,default:void 0,required:!1},prefetch:{type:Boolean,default:void 0,required:!1},prefetchOn:{type:[String,Object],default:void 0,required:!1},noPrefetch:{type:Boolean,default:void 0,required:!1},activeClass:{type:String,default:void 0,required:!1},exactActiveClass:{type:String,default:void 0,required:!1},prefetchedClass:{type:String,default:void 0,required:!1},replace:{type:Boolean,default:void 0,required:!1},ariaCurrentValue:{type:String,default:void 0,required:!1},external:{type:Boolean,default:void 0,required:!1},custom:{type:Boolean,default:void 0,required:!1},trailingSlash:{type:String,default:void 0,required:!1}},useLink:q,setup(e,{slots:n}){const h=P(),{to:a,href:i,navigate:x,isExternal:g,hasTarget:d,isAbsoluteUrl:l}=q(e),c=j(!1),b=E(null),f=r=>{var s;b.value=e.custom?(s=r==null?void 0:r.$el)==null?void 0:s.nextElementSibling:r==null?void 0:r.$el};function S(r){var s,u;return!c.value&&(typeof e.prefetchOn=="string"?e.prefetchOn===r:((s=e.prefetchOn)==null?void 0:s[r])??((u=t.prefetchOn)==null?void 0:u[r]))&&(e.prefetch??t.prefetch)!==!1&&e.noPrefetch!==!0&&e.target!=="_blank"&&!J()}async function m(r=A()){if(c.value)return;c.value=!0;const s=typeof a.value=="string"?a.value:g.value?k(a.value):h.resolve(a.value).fullPath,u=g.value?new URL(s,window.location.href).href:s;await Promise.all([r.hooks.callHook("link:prefetch",u).catch(()=>{}),!g.value&&!d.value&&D(a.value,h).catch(()=>{})])}if(S("visibility")){const r=A();let s,u=null;N(()=>{const v=X();B(()=>{s=W(()=>{var w;(w=b==null?void 0:b.value)!=null&&w.tagName&&(u=v.observe(b.value,async()=>{u==null||u(),u=null,await m(r)}))})})}),U(()=>{s&&Q(s),u==null||u(),u=null})}return()=>{var u;if(!g.value&&!d.value&&!y(a.value)){const v={ref:f,to:a.value,activeClass:e.activeClass||t.activeClass,exactActiveClass:e.exactActiveClass||t.exactActiveClass,replace:e.replace,ariaCurrentValue:e.ariaCurrentValue,custom:e.custom};return e.custom||(S("interaction")&&(v.onPointerenter=m.bind(null,void 0),v.onFocus=m.bind(null,void 0)),c.value&&(v.class=e.prefetchedClass||t.prefetchedClass),v.rel=e.rel||void 0),_(L("RouterLink"),v,n.default)}const r=e.target||null,s=G(e.noRel?"":e.rel,t.externalRelAttribute,l.value||d.value?"noopener noreferrer":"")||null;return e.custom?n.default?n.default({href:i.value,navigate:x,prefetch:m,get route(){if(!i.value)return;const v=new URL(i.value,window.location.href);return{path:v.pathname,fullPath:v.pathname,get query(){return I(v.search)},hash:v.hash,params:{},name:void 0,matched:[],redirectedFrom:void 0,meta:{},href:i.value}},rel:s,target:r,isExternal:g.value||d.value,isActive:!1,isExactActive:!1}):null:_("a",{ref:b,href:i.value||null,rel:s,target:r,onClick:v=>{if(!(g.value||d.value))return v.preventDefault(),e.replace?h.replace(i.value):h.push(i.value)}},(u=n.default)==null?void 0:u.call(n))}}})}const ee=K($);function R(t,o){const y=o==="append"?z:M;return O(t)&&!t.startsWith("http")?t:y(t,!0)}function X(){const t=A();if(t._observer)return t._observer;let o=null;const y=new Map,C=(e,n)=>(o||(o=new IntersectionObserver(h=>{for(const a of h){const i=y.get(a.target);(a.isIntersecting||a.intersectionRatio>0)&&i&&i()}})),y.set(e,n),o.observe(e),()=>{y.delete(e),o==null||o.unobserve(e),y.size===0&&(o==null||o.disconnect(),o=null)});return t._observer={observe:C}}const Y=/2g/;function J(){const t=navigator.connection;return!!(t&&(t.saveData||Y.test(t.effectiveType)))}export{ee as _};
