import{e as U,p as N,B,z as L,r as d,g as H,E as R,c as o,o as n,a as e,b as V,D as $,d as u,t as l,n as g,h as z,F as A,j as C}from"./qi-a-hqW.js";const F={class:"mb-6 flex items-center"},O={key:0},I={key:1,class:"rounded-md bg-red-50 p-4 dark:bg-red-900/30"},J={class:"flex"},q={class:"ml-3"},G={class:"text-sm font-medium text-red-800 dark:text-red-200"},K={key:2},Q={class:"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800"},W={class:"px-6 py-5"},X={class:"flex items-center"},Y={class:"h-20 w-20 flex-shrink-0"},Z=["src"],ee={class:"ml-5 flex-1"},te={class:"text-xl font-bold text-gray-900 dark:text-white"},se={class:"text-sm text-gray-500 dark:text-gray-400"},re={class:"mt-2 flex items-center space-x-3"},ae={class:"inline-flex rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"},le={class:"border-t border-gray-200 dark:border-gray-700"},oe={class:"flex border-b border-gray-200 dark:border-gray-700"},ne=["onClick"],de={key:0,class:"px-6 py-5"},ie={class:"grid grid-cols-1 gap-y-6 sm:grid-cols-2"},ue={class:"mt-1 text-sm text-gray-900 dark:text-white"},ce={class:"mt-1 text-sm text-gray-900 dark:text-white"},ve={class:"mt-1 text-sm text-gray-900 dark:text-white"},ge={class:"mt-1 text-sm"},me={class:"mt-1 text-sm text-gray-900 dark:text-white"},xe={key:1,class:"px-6 py-5"},pe={key:0},he={class:"divide-y divide-gray-200 dark:divide-gray-700"},fe={class:"flex items-center"},ye={class:"h-10 w-10 flex-shrink-0 overflow-hidden rounded-md bg-gray-200 dark:bg-gray-700"},ke=["src"],be={key:1,class:"i-ph-book-open h-full w-full p-2 text-gray-500 dark:text-gray-400"},we={class:"ml-4 flex-1"},_e={class:"text-sm font-medium text-gray-900 dark:text-white"},$e={class:"text-xs text-gray-500 dark:text-gray-400"},Ae={class:"ml-3"},Ce={key:1,class:"py-4 text-center text-sm text-gray-500 dark:text-gray-400"},Te={key:2,class:"px-6 py-5"},Me={class:"space-y-4"},Pe=U({__name:"index",setup(je){const b=N(),T=B(),w=L(()=>T.params.id),s=d({}),m=d(!0),x=d(""),M=[{id:"profile",name:"基本信息"},{id:"courses",name:"课程记录"},{id:"actions",name:"账户操作"}],v=d("profile"),_=d([]),c=d(!1),p=d(""),h=d(""),f=d(""),i=d("");function y(r){return new Date(r).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}async function j(){m.value=!0,x.value="";try{const r=await fetch(`/api/admin/users/${w.value}`);if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const t=await r.json();s.value=t;const k=await fetch(`/api/admin/users/${w.value}/courses`);if(k.ok){const a=await k.json();_.value=a}m.value=!1}catch(r){console.error("获取用户信息失败:",r),x.value="获取用户信息失败，请稍后重试",m.value=!1}}async function D(){p.value=s.value.isActive?"禁用用户":"启用用户",h.value=s.value.isActive?`确定要禁用用户 "${s.value.username}" 吗？禁用后用户将无法登录系统。`:`确定要启用用户 "${s.value.username}" 吗？启用后用户将可以正常登录系统。`,f.value=s.value.isActive?"bg-red-600 hover:bg-red-700":"bg-green-600 hover:bg-green-700",i.value="toggleStatus",c.value=!0}function E(){p.value="重置密码",h.value=`确定要重置用户 "${s.value.username}" 的密码吗？系统将生成一个新密码并发送到用户邮箱。`,f.value="bg-yellow-600 hover:bg-yellow-700",i.value="resetPassword",c.value=!0}function P(){p.value="删除用户",h.value=`确定要删除用户 "${s.value.username}" 吗？此操作不可逆。`,f.value="bg-red-600 hover:bg-red-700",i.value="deleteUser",c.value=!0}async function S(){try{if(i.value==="toggleStatus"){const r=!s.value.isActive,t=await fetch(`/api/admin/users/${s.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...s.value,isActive:r})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);s.value.isActive=r,console.log(`用户 ${s.value.id} 状态已更新为: ${s.value.isActive?"活跃":"未激活"}`)}else if(i.value==="resetPassword"){const r=await fetch(`/api/admin/users/${s.value.id}/reset-password`,{method:"POST"});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);console.log(`已重置用户 ${s.value.id} 的密码`)}else if(i.value==="deleteUser"){const r=await fetch(`/api/admin/users/${s.value.id}`,{method:"DELETE"});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);console.log(`已删除用户 ${s.value.id}`),b.push("/admin/users")}}catch(r){console.error("操作失败:",r)}finally{c.value=!1,i.value=""}}return H(()=>{j()}),(r,t)=>{const k=R("MainMessageBox");return n(),o("div",null,[e("div",F,[e("button",{onClick:t[0]||(t[0]=a=>$(b).back()),class:"mr-3 inline-flex items-center text-sm font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"},t[3]||(t[3]=[e("span",{class:"i-ph-arrow-left-bold mr-1 h-5 w-5"},null,-1),u(" 返回 ",-1)])),t[4]||(t[4]=e("h1",{class:"text-2xl font-bold"},"用户详情",-1))]),m.value?(n(),o("div",O,t[5]||(t[5]=[e("div",{class:"flex h-64 items-center justify-center"},[e("span",{class:"i-ph-spinner animate-spin h-10 w-10 text-purple-600"})],-1)]))):x.value?(n(),o("div",I,[e("div",J,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("span",{class:"i-ph-warning-circle h-5 w-5 text-red-400","aria-hidden":"true"})],-1)),e("div",q,[e("h3",G,l(x.value),1)])])])):(n(),o("div",K,[e("div",Q,[e("div",W,[e("div",X,[e("div",Y,[e("img",{class:"h-20 w-20 rounded-full",src:s.value.avatar,alt:"用户头像"},null,8,Z)]),e("div",ee,[e("h2",te,l(s.value.username),1),e("p",se,[t[7]||(t[7]=e("span",{class:"i-ph-envelope mr-1 inline-block h-4 w-4 align-text-bottom"},null,-1)),u(" "+l(s.value.email),1)]),e("div",re,[e("span",{class:g(["inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium",s.value.isActive?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"])},l(s.value.isActive?"活跃":"未激活"),3),e("span",ae,l(s.value.isNewUser?"新用户":"老用户"),1)])]),e("div",null,[e("button",{onClick:t[1]||(t[1]=a=>$(b).push(`/admin/users/${w.value}/edit`)),class:"inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:ring-gray-600 dark:hover:bg-gray-600"},t[8]||(t[8]=[e("span",{class:"i-ph-pencil-simple-bold mr-1 h-4 w-4"},null,-1),u(" 编辑 ",-1)]))])])]),e("div",le,[e("div",oe,[(n(),o(A,null,C(M,a=>e("button",{key:a.id,onClick:De=>v.value=a.id,class:g(["px-4 py-3 text-sm font-medium",[v.value===a.id?"border-b-2 border-purple-500 text-purple-600 dark:text-purple-400":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"]])},l(a.name),11,ne)),64))]),v.value==="profile"?(n(),o("div",de,[e("div",ie,[e("div",null,[t[9]||(t[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"用户ID",-1)),e("dd",ue,l(s.value.id),1)]),e("div",null,[t[10]||(t[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"注册时间",-1)),e("dd",ce,l(y(s.value.createdAt)),1)]),e("div",null,[t[11]||(t[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"最后登录",-1)),e("dd",ve,l(s.value.lastLoginAt?y(s.value.lastLoginAt):"从未登录"),1)]),e("div",null,[t[12]||(t[12]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"账户状态",-1)),e("dd",ge,[e("span",{class:g(["inline-flex rounded-full px-2 py-0.5 text-xs font-medium",s.value.isActive?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"])},l(s.value.isActive?"活跃":"未激活"),3)])]),e("div",null,[t[13]||(t[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"最后更新",-1)),e("dd",me,l(y(s.value.updatedAt)),1)])])])):v.value==="courses"?(n(),o("div",xe,[_.value.length>0?(n(),o("div",pe,[e("ul",he,[(n(!0),o(A,null,C(_.value,a=>(n(),o("li",{key:a.id,class:"py-4"},[e("div",fe,[e("div",ye,[a.cover?(n(),o("img",{key:0,src:a.cover,alt:"课程封面",class:"h-full w-full object-cover"},null,8,ke)):(n(),o("span",be))]),e("div",we,[e("div",_e,l(a.title),1),e("div",$e,[u(" 进度: "+l(a.progress)+"% ",1),e("span",Ae,"最后学习: "+l(y(a.lastStudied)),1)])])])]))),128))])])):(n(),o("div",Ce," 该用户尚未学习任何课程 "))])):v.value==="actions"?(n(),o("div",Te,[e("div",Me,[e("div",null,[e("button",{onClick:D,class:g(["inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-medium shadow-sm",s.value.isActive?"bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50":"bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"])},[e("span",{class:g([s.value.isActive?"i-ph-prohibit-bold":"i-ph-check-circle-bold","mr-2 h-5 w-5"])},null,2),u(" "+l(s.value.isActive?"禁用此账户":"启用此账户"),1)],2)]),e("div",null,[e("button",{onClick:E,class:"inline-flex w-full items-center justify-center rounded-md bg-yellow-100 px-3 py-2 text-sm font-medium text-yellow-700 shadow-sm hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"},t[14]||(t[14]=[e("span",{class:"i-ph-key-bold mr-2 h-5 w-5"},null,-1),u(" 重置密码 ",-1)]))]),e("div",null,[e("button",{onClick:P,class:"inline-flex w-full items-center justify-center rounded-md bg-red-100 px-3 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"},t[15]||(t[15]=[e("span",{class:"i-ph-trash-bold mr-2 h-5 w-5"},null,-1),u(" 删除此账户 ",-1)]))])])])):z("",!0)])])])),V(k,{"show-modal":c.value,"onUpdate:showModal":t[2]||(t[2]=a=>c.value=a),title:p.value,content:h.value,"confirm-btn-text":"确认","confirm-btn-class":f.value,onConfirm:S},null,8,["show-modal","title","content","confirm-btn-class"])])}}});export{Pe as default};
