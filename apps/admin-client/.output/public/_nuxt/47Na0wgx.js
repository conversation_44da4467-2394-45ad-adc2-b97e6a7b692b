var Ho=Object.defineProperty;var Fo=(n,t,e)=>t in n?Ho(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var I=(n,t,e)=>Fo(n,typeof t!="symbol"?t+"":t,e);import{r as fn,g as zo,y as Si,P as Ko,c as qi,o as Oi,a as Tr,h as Vo,i as Go,v as Zo}from"./qi-a-hqW.js";import{_ as Wo}from"./DlAUqK2U.js";var Rl=typeof globalThis=="object"&&globalThis&&globalThis.Object===Object&&globalThis,Xo=typeof self=="object"&&self&&self.Object===Object&&self,te=Rl||Xo||Function("return this")(),be=te.Symbol,Ml=Object.prototype,Yo=Ml.hasOwnProperty,Qo=Ml.toString,xr=be?be.toStringTag:void 0;function Jo(n){var t=Yo.call(n,xr),e=n[xr];try{n[xr]=void 0;var r=!0}catch{}var s=Qo.call(n);return r&&(t?n[xr]=e:delete n[xr]),s}var ta=Object.prototype,ea=ta.toString;function ra(n){return ea.call(n)}var na="[object Null]",sa="[object Undefined]",Ci=be?be.toStringTag:void 0;function or(n){return n==null?n===void 0?sa:na:Ci&&Ci in Object(n)?Jo(n):ra(n)}function le(n){return n!=null&&typeof n=="object"}var _e=Array.isArray;function ye(n){var t=typeof n;return n!=null&&(t=="object"||t=="function")}function Bl(n){return n}var ia="[object AsyncFunction]",la="[object Function]",oa="[object GeneratorFunction]",aa="[object Proxy]";function ai(n){if(!ye(n))return!1;var t=or(n);return t==la||t==oa||t==ia||t==aa}var xs=te["__core-js_shared__"],Ii=function(){var n=/[^.]+$/.exec(xs&&xs.keys&&xs.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function ca(n){return!!Ii&&Ii in n}var ua=Function.prototype,ha=ua.toString;function Oe(n){if(n!=null){try{return ha.call(n)}catch{}try{return n+""}catch{}}return""}var fa=/[\\^$.*+?()[\]{}|]/g,da=/^\[object .+?Constructor\]$/,ga=Function.prototype,pa=Object.prototype,ma=ga.toString,ba=pa.hasOwnProperty,ya=RegExp("^"+ma.call(ba).replace(fa,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function va(n){if(!ye(n)||ca(n))return!1;var t=ai(n)?ya:da;return t.test(Oe(n))}function Ea(n,t){return n==null?void 0:n[t]}function Ce(n,t){var e=Ea(n,t);return va(e)?e:void 0}var $s=Ce(te,"WeakMap"),ki=Object.create,Aa=function(){function n(){}return function(t){if(!ye(t))return{};if(ki)return ki(t);n.prototype=t;var e=new n;return n.prototype=void 0,e}}();function wa(n,t,e){switch(e.length){case 0:return n.call(t);case 1:return n.call(t,e[0]);case 2:return n.call(t,e[0],e[1]);case 3:return n.call(t,e[0],e[1],e[2])}return n.apply(t,e)}function Na(n,t){var e=-1,r=n.length;for(t||(t=Array(r));++e<r;)t[e]=n[e];return t}var Ta=800,xa=16,La=Date.now;function _a(n){var t=0,e=0;return function(){var r=La(),s=xa-(r-e);if(e=r,s>0){if(++t>=Ta)return arguments[0]}else t=0;return n.apply(void 0,arguments)}}function Sa(n){return function(){return n}}var Ln=function(){try{var n=Ce(Object,"defineProperty");return n({},"",{}),n}catch{}}(),qa=Ln?function(n,t){return Ln(n,"toString",{configurable:!0,enumerable:!1,value:Sa(t),writable:!0})}:Bl,Oa=_a(qa);function Ca(n,t){for(var e=-1,r=n==null?0:n.length;++e<r&&t(n[e],e,n)!==!1;);return n}var Ia=9007199254740991,ka=/^(?:0|[1-9]\d*)$/;function Dl(n,t){var e=typeof n;return t=t??Ia,!!t&&(e=="number"||e!="symbol"&&ka.test(n))&&n>-1&&n%1==0&&n<t}function ci(n,t,e){t=="__proto__"&&Ln?Ln(n,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):n[t]=e}function Ur(n,t){return n===t||n!==n&&t!==t}var Ra=Object.prototype,Ma=Ra.hasOwnProperty;function jl(n,t,e){var r=n[t];(!(Ma.call(n,t)&&Ur(r,e))||e===void 0&&!(t in n))&&ci(n,t,e)}function Ba(n,t,e,r){var s=!e;e||(e={});for(var i=-1,o=t.length;++i<o;){var a=t[i],u=void 0;u===void 0&&(u=n[a]),s?ci(e,a,u):jl(e,a,u)}return e}var Ri=Math.max;function Da(n,t,e){return t=Ri(t===void 0?n.length-1:t,0),function(){for(var r=arguments,s=-1,i=Ri(r.length-t,0),o=Array(i);++s<i;)o[s]=r[t+s];s=-1;for(var a=Array(t+1);++s<t;)a[s]=r[s];return a[t]=e(o),wa(n,this,a)}}function ja(n,t){return Oa(Da(n,t,Bl),n+"")}var $a=9007199254740991;function $l(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=$a}function On(n){return n!=null&&$l(n.length)&&!ai(n)}function Pa(n,t,e){if(!ye(e))return!1;var r=typeof t;return(r=="number"?On(e)&&Dl(t,e.length):r=="string"&&t in e)?Ur(e[t],n):!1}function Ua(n){return ja(function(t,e){var r=-1,s=e.length,i=s>1?e[s-1]:void 0,o=s>2?e[2]:void 0;for(i=n.length>3&&typeof i=="function"?(s--,i):void 0,o&&Pa(e[0],e[1],o)&&(i=s<3?void 0:i,s=1),t=Object(t);++r<s;){var a=e[r];a&&n(t,a,r,i)}return t})}var Ha=Object.prototype;function ui(n){var t=n&&n.constructor,e=typeof t=="function"&&t.prototype||Ha;return n===e}function Fa(n,t){for(var e=-1,r=Array(n);++e<n;)r[e]=t(e);return r}var za="[object Arguments]";function Mi(n){return le(n)&&or(n)==za}var Pl=Object.prototype,Ka=Pl.hasOwnProperty,Va=Pl.propertyIsEnumerable,Ps=Mi(function(){return arguments}())?Mi:function(n){return le(n)&&Ka.call(n,"callee")&&!Va.call(n,"callee")};function Ga(){return!1}var Ul=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Bi=Ul&&typeof module=="object"&&module&&!module.nodeType&&module,Za=Bi&&Bi.exports===Ul,Di=Za?te.Buffer:void 0,Wa=Di?Di.isBuffer:void 0,Rr=Wa||Ga,Xa="[object Arguments]",Ya="[object Array]",Qa="[object Boolean]",Ja="[object Date]",tc="[object Error]",ec="[object Function]",rc="[object Map]",nc="[object Number]",sc="[object Object]",ic="[object RegExp]",lc="[object Set]",oc="[object String]",ac="[object WeakMap]",cc="[object ArrayBuffer]",uc="[object DataView]",hc="[object Float32Array]",fc="[object Float64Array]",dc="[object Int8Array]",gc="[object Int16Array]",pc="[object Int32Array]",mc="[object Uint8Array]",bc="[object Uint8ClampedArray]",yc="[object Uint16Array]",vc="[object Uint32Array]",lt={};lt[hc]=lt[fc]=lt[dc]=lt[gc]=lt[pc]=lt[mc]=lt[bc]=lt[yc]=lt[vc]=!0;lt[Xa]=lt[Ya]=lt[cc]=lt[Qa]=lt[uc]=lt[Ja]=lt[tc]=lt[ec]=lt[rc]=lt[nc]=lt[sc]=lt[ic]=lt[lc]=lt[oc]=lt[ac]=!1;function Ec(n){return le(n)&&$l(n.length)&&!!lt[or(n)]}function hi(n){return function(t){return n(t)}}var Hl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Or=Hl&&typeof module=="object"&&module&&!module.nodeType&&module,Ac=Or&&Or.exports===Hl,Ls=Ac&&Rl.process,rr=function(){try{var n=Or&&Or.require&&Or.require("util").types;return n||Ls&&Ls.binding&&Ls.binding("util")}catch{}}(),ji=rr&&rr.isTypedArray,fi=ji?hi(ji):Ec,wc=Object.prototype,Nc=wc.hasOwnProperty;function Fl(n,t){var e=_e(n),r=!e&&Ps(n),s=!e&&!r&&Rr(n),i=!e&&!r&&!s&&fi(n),o=e||r||s||i,a=o?Fa(n.length,String):[],u=a.length;for(var h in n)(t||Nc.call(n,h))&&!(o&&(h=="length"||s&&(h=="offset"||h=="parent")||i&&(h=="buffer"||h=="byteLength"||h=="byteOffset")||Dl(h,u)))&&a.push(h);return a}function zl(n,t){return function(e){return n(t(e))}}var Tc=zl(Object.keys,Object),xc=Object.prototype,Lc=xc.hasOwnProperty;function _c(n){if(!ui(n))return Tc(n);var t=[];for(var e in Object(n))Lc.call(n,e)&&e!="constructor"&&t.push(e);return t}function Sc(n){return On(n)?Fl(n):_c(n)}function qc(n){var t=[];if(n!=null)for(var e in Object(n))t.push(e);return t}var Oc=Object.prototype,Cc=Oc.hasOwnProperty;function Ic(n){if(!ye(n))return qc(n);var t=ui(n),e=[];for(var r in n)r=="constructor"&&(t||!Cc.call(n,r))||e.push(r);return e}function Kl(n){return On(n)?Fl(n,!0):Ic(n)}var Mr=Ce(Object,"create");function kc(){this.__data__=Mr?Mr(null):{},this.size=0}function Rc(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t}var Mc="__lodash_hash_undefined__",Bc=Object.prototype,Dc=Bc.hasOwnProperty;function jc(n){var t=this.__data__;if(Mr){var e=t[n];return e===Mc?void 0:e}return Dc.call(t,n)?t[n]:void 0}var $c=Object.prototype,Pc=$c.hasOwnProperty;function Uc(n){var t=this.__data__;return Mr?t[n]!==void 0:Pc.call(t,n)}var Hc="__lodash_hash_undefined__";function Fc(n,t){var e=this.__data__;return this.size+=this.has(n)?0:1,e[n]=Mr&&t===void 0?Hc:t,this}function Se(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}Se.prototype.clear=kc;Se.prototype.delete=Rc;Se.prototype.get=jc;Se.prototype.has=Uc;Se.prototype.set=Fc;function zc(){this.__data__=[],this.size=0}function Cn(n,t){for(var e=n.length;e--;)if(Ur(n[e][0],t))return e;return-1}var Kc=Array.prototype,Vc=Kc.splice;function Gc(n){var t=this.__data__,e=Cn(t,n);if(e<0)return!1;var r=t.length-1;return e==r?t.pop():Vc.call(t,e,1),--this.size,!0}function Zc(n){var t=this.__data__,e=Cn(t,n);return e<0?void 0:t[e][1]}function Wc(n){return Cn(this.__data__,n)>-1}function Xc(n,t){var e=this.__data__,r=Cn(e,n);return r<0?(++this.size,e.push([n,t])):e[r][1]=t,this}function ce(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}ce.prototype.clear=zc;ce.prototype.delete=Gc;ce.prototype.get=Zc;ce.prototype.has=Wc;ce.prototype.set=Xc;var Br=Ce(te,"Map");function Yc(){this.size=0,this.__data__={hash:new Se,map:new(Br||ce),string:new Se}}function Qc(n){var t=typeof n;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?n!=="__proto__":n===null}function In(n,t){var e=n.__data__;return Qc(t)?e[typeof t=="string"?"string":"hash"]:e.map}function Jc(n){var t=In(this,n).delete(n);return this.size-=t?1:0,t}function tu(n){return In(this,n).get(n)}function eu(n){return In(this,n).has(n)}function ru(n,t){var e=In(this,n),r=e.size;return e.set(n,t),this.size+=e.size==r?0:1,this}function Ie(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}Ie.prototype.clear=Yc;Ie.prototype.delete=Jc;Ie.prototype.get=tu;Ie.prototype.has=eu;Ie.prototype.set=ru;function nu(n,t){for(var e=-1,r=t.length,s=n.length;++e<r;)n[s+e]=t[e];return n}var Vl=zl(Object.getPrototypeOf,Object),su="[object Object]",iu=Function.prototype,lu=Object.prototype,Gl=iu.toString,ou=lu.hasOwnProperty,au=Gl.call(Object);function cu(n){if(!le(n)||or(n)!=su)return!1;var t=Vl(n);if(t===null)return!0;var e=ou.call(t,"constructor")&&t.constructor;return typeof e=="function"&&e instanceof e&&Gl.call(e)==au}function uu(){this.__data__=new ce,this.size=0}function hu(n){var t=this.__data__,e=t.delete(n);return this.size=t.size,e}function fu(n){return this.__data__.get(n)}function du(n){return this.__data__.has(n)}var gu=200;function pu(n,t){var e=this.__data__;if(e instanceof ce){var r=e.__data__;if(!Br||r.length<gu-1)return r.push([n,t]),this.size=++e.size,this;e=this.__data__=new Ie(r)}return e.set(n,t),this.size=e.size,this}function Xt(n){var t=this.__data__=new ce(n);this.size=t.size}Xt.prototype.clear=uu;Xt.prototype.delete=hu;Xt.prototype.get=fu;Xt.prototype.has=du;Xt.prototype.set=pu;var Zl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,$i=Zl&&typeof module=="object"&&module&&!module.nodeType&&module,mu=$i&&$i.exports===Zl,Pi=mu?te.Buffer:void 0,Ui=Pi?Pi.allocUnsafe:void 0;function Wl(n,t){if(t)return n.slice();var e=n.length,r=Ui?Ui(e):new n.constructor(e);return n.copy(r),r}function bu(n,t){for(var e=-1,r=n==null?0:n.length,s=0,i=[];++e<r;){var o=n[e];t(o,e,n)&&(i[s++]=o)}return i}function yu(){return[]}var vu=Object.prototype,Eu=vu.propertyIsEnumerable,Hi=Object.getOwnPropertySymbols,Au=Hi?function(n){return n==null?[]:(n=Object(n),bu(Hi(n),function(t){return Eu.call(n,t)}))}:yu;function wu(n,t,e){var r=t(n);return _e(n)?r:nu(r,e(n))}function Us(n){return wu(n,Sc,Au)}var Hs=Ce(te,"DataView"),Fs=Ce(te,"Promise"),zs=Ce(te,"Set"),Fi="[object Map]",Nu="[object Object]",zi="[object Promise]",Ki="[object Set]",Vi="[object WeakMap]",Gi="[object DataView]",Tu=Oe(Hs),xu=Oe(Br),Lu=Oe(Fs),_u=Oe(zs),Su=Oe($s),jt=or;(Hs&&jt(new Hs(new ArrayBuffer(1)))!=Gi||Br&&jt(new Br)!=Fi||Fs&&jt(Fs.resolve())!=zi||zs&&jt(new zs)!=Ki||$s&&jt(new $s)!=Vi)&&(jt=function(n){var t=or(n),e=t==Nu?n.constructor:void 0,r=e?Oe(e):"";if(r)switch(r){case Tu:return Gi;case xu:return Fi;case Lu:return zi;case _u:return Ki;case Su:return Vi}return t});var qu=Object.prototype,Ou=qu.hasOwnProperty;function Cu(n){var t=n.length,e=new n.constructor(t);return t&&typeof n[0]=="string"&&Ou.call(n,"index")&&(e.index=n.index,e.input=n.input),e}var _n=te.Uint8Array;function di(n){var t=new n.constructor(n.byteLength);return new _n(t).set(new _n(n)),t}function Iu(n,t){var e=di(n.buffer);return new n.constructor(e,n.byteOffset,n.byteLength)}var ku=/\w*$/;function Ru(n){var t=new n.constructor(n.source,ku.exec(n));return t.lastIndex=n.lastIndex,t}var Zi=be?be.prototype:void 0,Wi=Zi?Zi.valueOf:void 0;function Mu(n){return Wi?Object(Wi.call(n)):{}}function Xl(n,t){var e=t?di(n.buffer):n.buffer;return new n.constructor(e,n.byteOffset,n.length)}var Bu="[object Boolean]",Du="[object Date]",ju="[object Map]",$u="[object Number]",Pu="[object RegExp]",Uu="[object Set]",Hu="[object String]",Fu="[object Symbol]",zu="[object ArrayBuffer]",Ku="[object DataView]",Vu="[object Float32Array]",Gu="[object Float64Array]",Zu="[object Int8Array]",Wu="[object Int16Array]",Xu="[object Int32Array]",Yu="[object Uint8Array]",Qu="[object Uint8ClampedArray]",Ju="[object Uint16Array]",th="[object Uint32Array]";function eh(n,t,e){var r=n.constructor;switch(t){case zu:return di(n);case Bu:case Du:return new r(+n);case Ku:return Iu(n);case Vu:case Gu:case Zu:case Wu:case Xu:case Yu:case Qu:case Ju:case th:return Xl(n,e);case ju:return new r;case $u:case Hu:return new r(n);case Pu:return Ru(n);case Uu:return new r;case Fu:return Mu(n)}}function Yl(n){return typeof n.constructor=="function"&&!ui(n)?Aa(Vl(n)):{}}var rh="[object Map]";function nh(n){return le(n)&&jt(n)==rh}var Xi=rr&&rr.isMap,sh=Xi?hi(Xi):nh,ih="[object Set]";function lh(n){return le(n)&&jt(n)==ih}var Yi=rr&&rr.isSet,oh=Yi?hi(Yi):lh,ah=1,Ql="[object Arguments]",ch="[object Array]",uh="[object Boolean]",hh="[object Date]",fh="[object Error]",Jl="[object Function]",dh="[object GeneratorFunction]",gh="[object Map]",ph="[object Number]",to="[object Object]",mh="[object RegExp]",bh="[object Set]",yh="[object String]",vh="[object Symbol]",Eh="[object WeakMap]",Ah="[object ArrayBuffer]",wh="[object DataView]",Nh="[object Float32Array]",Th="[object Float64Array]",xh="[object Int8Array]",Lh="[object Int16Array]",_h="[object Int32Array]",Sh="[object Uint8Array]",qh="[object Uint8ClampedArray]",Oh="[object Uint16Array]",Ch="[object Uint32Array]",st={};st[Ql]=st[ch]=st[Ah]=st[wh]=st[uh]=st[hh]=st[Nh]=st[Th]=st[xh]=st[Lh]=st[_h]=st[gh]=st[ph]=st[to]=st[mh]=st[bh]=st[yh]=st[vh]=st[Sh]=st[qh]=st[Oh]=st[Ch]=!0;st[fh]=st[Jl]=st[Eh]=!1;function Nn(n,t,e,r,s,i){var o,a=t&ah;if(o!==void 0)return o;if(!ye(n))return n;var u=_e(n);if(u)o=Cu(n);else{var h=jt(n),p=h==Jl||h==dh;if(Rr(n))return Wl(n,a);if(h==to||h==Ql||p&&!s)o=p?{}:Yl(n);else{if(!st[h])return s?n:{};o=eh(n,h,a)}}i||(i=new Xt);var v=i.get(n);if(v)return v;i.set(n,o),oh(n)?n.forEach(function(y){o.add(Nn(y,t,e,y,n,i))}):sh(n)&&n.forEach(function(y,E){o.set(E,Nn(y,t,e,E,n,i))});var f=Us,m=u?void 0:f(n);return Ca(m||n,function(y,E){m&&(E=y,y=n[E]),jl(o,E,Nn(y,t,e,E,n,i))}),o}var Ih=1,kh=4;function Je(n){return Nn(n,Ih|kh)}var Rh="__lodash_hash_undefined__";function Mh(n){return this.__data__.set(n,Rh),this}function Bh(n){return this.__data__.has(n)}function Sn(n){var t=-1,e=n==null?0:n.length;for(this.__data__=new Ie;++t<e;)this.add(n[t])}Sn.prototype.add=Sn.prototype.push=Mh;Sn.prototype.has=Bh;function Dh(n,t){for(var e=-1,r=n==null?0:n.length;++e<r;)if(t(n[e],e,n))return!0;return!1}function jh(n,t){return n.has(t)}var $h=1,Ph=2;function eo(n,t,e,r,s,i){var o=e&$h,a=n.length,u=t.length;if(a!=u&&!(o&&u>a))return!1;var h=i.get(n),p=i.get(t);if(h&&p)return h==t&&p==n;var v=-1,f=!0,m=e&Ph?new Sn:void 0;for(i.set(n,t),i.set(t,n);++v<a;){var y=n[v],E=t[v];if(r)var N=o?r(E,y,v,t,n,i):r(y,E,v,n,t,i);if(N!==void 0){if(N)continue;f=!1;break}if(m){if(!Dh(t,function(T,_){if(!jh(m,_)&&(y===T||s(y,T,e,r,i)))return m.push(_)})){f=!1;break}}else if(!(y===E||s(y,E,e,r,i))){f=!1;break}}return i.delete(n),i.delete(t),f}function Uh(n){var t=-1,e=Array(n.size);return n.forEach(function(r,s){e[++t]=[s,r]}),e}function Hh(n){var t=-1,e=Array(n.size);return n.forEach(function(r){e[++t]=r}),e}var Fh=1,zh=2,Kh="[object Boolean]",Vh="[object Date]",Gh="[object Error]",Zh="[object Map]",Wh="[object Number]",Xh="[object RegExp]",Yh="[object Set]",Qh="[object String]",Jh="[object Symbol]",tf="[object ArrayBuffer]",ef="[object DataView]",Qi=be?be.prototype:void 0,_s=Qi?Qi.valueOf:void 0;function rf(n,t,e,r,s,i,o){switch(e){case ef:if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case tf:return!(n.byteLength!=t.byteLength||!i(new _n(n),new _n(t)));case Kh:case Vh:case Wh:return Ur(+n,+t);case Gh:return n.name==t.name&&n.message==t.message;case Xh:case Qh:return n==t+"";case Zh:var a=Uh;case Yh:var u=r&Fh;if(a||(a=Hh),n.size!=t.size&&!u)return!1;var h=o.get(n);if(h)return h==t;r|=zh,o.set(n,t);var p=eo(a(n),a(t),r,s,i,o);return o.delete(n),p;case Jh:if(_s)return _s.call(n)==_s.call(t)}return!1}var nf=1,sf=Object.prototype,lf=sf.hasOwnProperty;function of(n,t,e,r,s,i){var o=e&nf,a=Us(n),u=a.length,h=Us(t),p=h.length;if(u!=p&&!o)return!1;for(var v=u;v--;){var f=a[v];if(!(o?f in t:lf.call(t,f)))return!1}var m=i.get(n),y=i.get(t);if(m&&y)return m==t&&y==n;var E=!0;i.set(n,t),i.set(t,n);for(var N=o;++v<u;){f=a[v];var T=n[f],_=t[f];if(r)var C=o?r(_,T,f,t,n,i):r(T,_,f,n,t,i);if(!(C===void 0?T===_||s(T,_,e,r,i):C)){E=!1;break}N||(N=f=="constructor")}if(E&&!N){var H=n.constructor,P=t.constructor;H!=P&&"constructor"in n&&"constructor"in t&&!(typeof H=="function"&&H instanceof H&&typeof P=="function"&&P instanceof P)&&(E=!1)}return i.delete(n),i.delete(t),E}var af=1,Ji="[object Arguments]",tl="[object Array]",dn="[object Object]",cf=Object.prototype,el=cf.hasOwnProperty;function uf(n,t,e,r,s,i){var o=_e(n),a=_e(t),u=o?tl:jt(n),h=a?tl:jt(t);u=u==Ji?dn:u,h=h==Ji?dn:h;var p=u==dn,v=h==dn,f=u==h;if(f&&Rr(n)){if(!Rr(t))return!1;o=!0,p=!1}if(f&&!p)return i||(i=new Xt),o||fi(n)?eo(n,t,e,r,s,i):rf(n,t,u,e,r,s,i);if(!(e&af)){var m=p&&el.call(n,"__wrapped__"),y=v&&el.call(t,"__wrapped__");if(m||y){var E=m?n.value():n,N=y?t.value():t;return i||(i=new Xt),s(E,N,e,r,i)}}return f?(i||(i=new Xt),of(n,t,e,r,s,i)):!1}function ro(n,t,e,r,s){return n===t?!0:n==null||t==null||!le(n)&&!le(t)?n!==n&&t!==t:uf(n,t,e,r,ro,s)}function hf(n){return function(t,e,r){for(var s=-1,i=Object(t),o=r(t),a=o.length;a--;){var u=o[++s];if(e(i[u],u,i)===!1)break}return t}}var ff=hf();function Ks(n,t,e){(e!==void 0&&!Ur(n[t],e)||e===void 0&&!(t in n))&&ci(n,t,e)}function df(n){return le(n)&&On(n)}function Vs(n,t){if(!(t==="constructor"&&typeof n[t]=="function")&&t!="__proto__")return n[t]}function gf(n){return Ba(n,Kl(n))}function pf(n,t,e,r,s,i,o){var a=Vs(n,e),u=Vs(t,e),h=o.get(u);if(h){Ks(n,e,h);return}var p=i?i(a,u,e+"",n,t,o):void 0,v=p===void 0;if(v){var f=_e(u),m=!f&&Rr(u),y=!f&&!m&&fi(u);p=u,f||m||y?_e(a)?p=a:df(a)?p=Na(a):m?(v=!1,p=Wl(u,!0)):y?(v=!1,p=Xl(u,!0)):p=[]:cu(u)||Ps(u)?(p=a,Ps(a)?p=gf(a):(!ye(a)||ai(a))&&(p=Yl(u))):v=!1}v&&(o.set(u,p),s(p,u,r,i,o),o.delete(u)),Ks(n,e,p)}function no(n,t,e,r,s){n!==t&&ff(t,function(i,o){if(s||(s=new Xt),ye(i))pf(n,t,o,e,no,r,s);else{var a=r?r(Vs(n,o),i,o+"",n,t,s):void 0;a===void 0&&(a=i),Ks(n,o,a)}},Kl)}function gi(n,t){return ro(n,t)}var me=Ua(function(n,t,e){no(n,t,e)}),D=(n=>(n[n.TYPE=3]="TYPE",n[n.LEVEL=12]="LEVEL",n[n.ATTRIBUTE=13]="ATTRIBUTE",n[n.BLOT=14]="BLOT",n[n.INLINE=7]="INLINE",n[n.BLOCK=11]="BLOCK",n[n.BLOCK_BLOT=10]="BLOCK_BLOT",n[n.INLINE_BLOT=6]="INLINE_BLOT",n[n.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",n[n.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",n[n.ANY=15]="ANY",n))(D||{});class Qt{constructor(t,e,r={}){this.attrName=t,this.keyName=e;const s=D.TYPE&D.ATTRIBUTE;this.scope=r.scope!=null?r.scope&D.LEVEL|s:D.ATTRIBUTE,r.whitelist!=null&&(this.whitelist=r.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class tr extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}}const so=class Gs{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let r=null;try{r=t.parentNode}catch{return null}return this.find(r,e)}return null}create(t,e,r){const s=this.query(e);if(s==null)throw new tr(`Unable to create ${e} blot`);const i=s,o=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:i.create(r),a=new i(t,o,r);return Gs.blots.set(a.domNode,a),a}find(t,e=!1){return Gs.find(t,e)}query(t,e=D.ANY){let r;return typeof t=="string"?r=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?r=this.types.text:typeof t=="number"?t&D.LEVEL&D.BLOCK?r=this.types.block:t&D.LEVEL&D.INLINE&&(r=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(s=>(r=this.classes[s],!!r)),r=r||this.tags[t.tagName]),r==null?null:"scope"in r&&e&D.LEVEL&r.scope&&e&D.TYPE&r.scope?r:null}register(...t){return t.map(e=>{const r="blotName"in e,s="attrName"in e;if(!r&&!s)throw new tr("Invalid definition");if(r&&e.blotName==="abstract")throw new tr("Cannot register abstract class");const i=r?e.blotName:s?e.attrName:void 0;return this.types[i]=e,s?typeof e.keyName=="string"&&(this.attributes[e.keyName]=e):r&&(e.className&&(this.classes[e.className]=e),e.tagName&&(Array.isArray(e.tagName)?e.tagName=e.tagName.map(o=>o.toUpperCase()):e.tagName=e.tagName.toUpperCase(),(Array.isArray(e.tagName)?e.tagName:[e.tagName]).forEach(o=>{(this.tags[o]==null||e.className==null)&&(this.tags[o]=e)}))),e})}};so.blots=new WeakMap;let nr=so;function rl(n,t){return(n.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}class mf extends Qt{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){rl(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){const e=(rl(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}const Ht=mf;function Ss(n){const t=n.split("-"),e=t.slice(1).map(r=>r[0].toUpperCase()+r.slice(1)).join("");return t[0]+e}class bf extends Qt{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[Ss(this.keyName)]=e,!0):!1}remove(t){t.style[Ss(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[Ss(this.keyName)];return this.canAdd(t,e)?e:""}}const ve=bf;class yf{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=nr.find(this.domNode);if(t==null)return;const e=Qt.keys(this.domNode),r=Ht.keys(this.domNode),s=ve.keys(this.domNode);e.concat(r).concat(s).forEach(i=>{const o=t.scroll.query(i,D.ATTRIBUTE);o instanceof Qt&&(this.attributes[o.attrName]=o)})}copy(t){Object.keys(this.attributes).forEach(e=>{const r=this.attributes[e].value(this.domNode);t.format(e,r)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}const kn=yf,io=class{constructor(t,e){this.scroll=t,this.domNode=e,nr.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new tr("Blot definition missing tagName");let e,r;return Array.isArray(this.tagName)?(typeof t=="string"?(r=t.toUpperCase(),parseInt(r,10).toString()===r&&(r=parseInt(r,10))):typeof t=="number"&&(r=t),typeof r=="number"?e=document.createElement(this.tagName[r-1]):r&&this.tagName.indexOf(r)>-1?e=document.createElement(r):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),nr.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,r,s){const i=this.isolate(t,e);if(this.scroll.query(r,D.BLOT)!=null&&s)i.wrap(r,s);else if(this.scroll.query(r,D.ATTRIBUTE)!=null){const o=this.scroll.create(this.statics.scope);i.wrap(o),o.format(r,s)}}insertAt(t,e,r){const s=r==null?this.scroll.create("text",e):this.scroll.create(e,r),i=this.split(t);this.parent.insertBefore(s,i||void 0)}isolate(t,e){const r=this.split(t);if(r==null)throw new Error("Attempt to isolate at end");return r.split(e),r}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(r,this.next||void 0),this.remove()),r}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(r,this.next||void 0),typeof r.appendChild!="function")throw new tr(`Cannot wrap ${t}`);return r.appendChild(this),r}};io.blotName="abstract";let lo=io;const oo=class extends lo{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let r=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(r+=1),[this.parent.domNode,r]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};oo.scope=D.INLINE_BLOT;let vf=oo;const Et=vf;class Ef{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let r=e();for(;r&&t>0;)t-=1,r=e();return r}contains(t){const e=this.iterator();let r=e();for(;r;){if(r===t)return!0;r=e()}return!1}indexOf(t){const e=this.iterator();let r=e(),s=0;for(;r;){if(r===t)return s;s+=1,r=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,r=this.head;for(;r!=null;){if(r===t)return e;e+=r.length(),r=r.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){const r=this.iterator();let s=r();for(;s;){const i=s.length();if(t<i||e&&t===i&&(s.next==null||s.next.length()!==0))return[s,t];t-=i,s=r()}return[null,0]}forEach(t){const e=this.iterator();let r=e();for(;r;)t(r),r=e()}forEachAt(t,e,r){if(e<=0)return;const[s,i]=this.find(t);let o=t-i;const a=this.iterator(s);let u=a();for(;u&&o<t+e;){const h=u.length();t>o?r(u,t-o,Math.min(e,o+h-t)):r(u,0,Math.min(h,t+e-o)),o+=h,u=a()}}map(t){return this.reduce((e,r)=>(e.push(t(r)),e),[])}reduce(t,e){const r=this.iterator();let s=r();for(;s;)e=t(e,s),s=r();return e}}function nl(n,t){const e=t.find(n);if(e)return e;try{return t.create(n)}catch{const r=t.create(D.INLINE);return Array.from(n.childNodes).forEach(s=>{r.domNode.appendChild(s)}),n.parentNode&&n.parentNode.replaceChild(r.domNode,n),r.attach(),r}}const ao=class de extends lo{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,de.uiClass&&this.uiNode.classList.add(de.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new Ef,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const e=nl(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(e){if(e instanceof tr)return;throw e}})}deleteAt(t,e){if(t===0&&e===this.length())return this.remove();this.children.forEachAt(t,e,(r,s,i)=>{r.deleteAt(s,i)})}descendant(t,e=0){const[r,s]=this.children.find(e);return t.blotName==null&&t(r)||t.blotName!=null&&r instanceof t?[r,s]:r instanceof de?r.descendant(t,s):[null,-1]}descendants(t,e=0,r=Number.MAX_VALUE){let s=[],i=r;return this.children.forEachAt(e,r,(o,a,u)=>{(t.blotName==null&&t(o)||t.blotName!=null&&o instanceof t)&&s.push(o),o instanceof de&&(s=s.concat(o.descendants(t,a,i))),i-=u}),s}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(e=>{t||this.statics.allowedChildren.some(r=>e instanceof r)||(e.statics.scope===D.BLOCK_BLOT?(e.next!=null&&this.splitAfter(e),e.prev!=null&&this.splitAfter(e.prev),e.parent.unwrap(),t=!0):e instanceof de?e.unwrap():e.remove())})}formatAt(t,e,r,s){this.children.forEachAt(t,e,(i,o,a)=>{i.formatAt(o,a,r,s)})}insertAt(t,e,r){const[s,i]=this.children.find(t);if(s)s.insertAt(i,e,r);else{const o=r==null?this.scroll.create("text",e):this.scroll.create(e,r);this.appendChild(o)}}insertBefore(t,e){t.parent!=null&&t.parent.children.remove(t);let r=null;this.children.insertBefore(t,e||null),t.parent=this,e!=null&&(r=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==r)&&this.domNode.insertBefore(t.domNode,r),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(r=>{t.insertBefore(r,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){const e=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(e)}else this.remove()}path(t,e=!1){const[r,s]=this.children.find(t,e),i=[[this,t]];return r instanceof de?i.concat(r.path(s,e)):(r!=null&&i.push([r,s]),i)}removeChild(t){this.children.remove(t)}replaceWith(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;return r instanceof de&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(t,this.length(),(s,i,o)=>{const a=s.split(i,e);a!=null&&r.appendChild(a)}),r}splitAfter(t){const e=this.clone();for(;t.next!=null;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const r=[],s=[];t.forEach(i=>{i.target===this.domNode&&i.type==="childList"&&(r.push(...i.addedNodes),s.push(...i.removedNodes))}),s.forEach(i=>{if(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const o=this.scroll.find(i);o!=null&&(o.domNode.parentNode==null||o.domNode.parentNode===this.domNode)&&o.detach()}),r.filter(i=>i.parentNode===this.domNode&&i!==this.uiNode).sort((i,o)=>i===o?0:i.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(i=>{let o=null;i.nextSibling!=null&&(o=this.scroll.find(i.nextSibling));const a=nl(i,this.scroll);(a.next!==o||a.next==null)&&(a.parent!=null&&a.parent.removeChild(this),this.insertBefore(a,o||void 0))}),this.enforceAllowedChildren()}};ao.uiClass="";let Af=ao;const Pt=Af;function wf(n,t){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(const e in n)if(n[e]!==t[e])return!1;return!0}const Ge=class Ze extends Pt{static create(t){return super.create(t)}static formats(t,e){const r=e.query(Ze.blotName);if(!(r!=null&&t.tagName===r.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new kn(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(r=>{r instanceof Ze||(r=r.wrap(Ze.blotName,!0)),this.attributes.copy(r)}),this.unwrap();else{const r=this.scroll.query(t,D.INLINE);if(r==null)return;r instanceof Qt?this.attributes.attribute(r,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,s){this.formats()[r]!=null||this.scroll.query(r,D.ATTRIBUTE)?this.isolate(t,e).format(r,s):super.formatAt(t,e,r,s)}optimize(t){super.optimize(t);const e=this.formats();if(Object.keys(e).length===0)return this.unwrap();const r=this.next;r instanceof Ze&&r.prev===this&&wf(e,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){const r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(r=>r.target===this.domNode&&r.type==="attributes")&&this.attributes.build()}wrap(t,e){const r=super.wrap(t,e);return r instanceof Ze&&this.attributes.move(r),r}};Ge.allowedChildren=[Ge,Et],Ge.blotName="inline",Ge.scope=D.INLINE_BLOT,Ge.tagName="SPAN";let Nf=Ge;const pi=Nf,We=class Zs extends Pt{static create(t){return super.create(t)}static formats(t,e){const r=e.query(Zs.blotName);if(!(r!=null&&t.tagName===r.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new kn(this.domNode)}format(t,e){const r=this.scroll.query(t,D.BLOCK);r!=null&&(r instanceof Qt?this.attributes.attribute(r,e):t===this.statics.blotName&&!e?this.replaceWith(Zs.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,s){this.scroll.query(r,D.BLOCK)!=null?this.format(r,s):super.formatAt(t,e,r,s)}insertAt(t,e,r){if(r==null||this.scroll.query(e,D.INLINE)!=null)super.insertAt(t,e,r);else{const s=this.split(t);if(s!=null){const i=this.scroll.create(e,r);s.parent.insertBefore(i,s)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){const r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(r=>r.target===this.domNode&&r.type==="attributes")&&this.attributes.build()}};We.blotName="block",We.scope=D.BLOCK_BLOT,We.tagName="P",We.allowedChildren=[pi,We,Et];let Tf=We;const Dr=Tf,Ws=class extends Pt{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,r,s){super.formatAt(t,e,r,s),this.enforceAllowedChildren()}insertAt(t,e,r){super.insertAt(t,e,r),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Ws.blotName="container",Ws.scope=D.BLOCK_BLOT;let xf=Ws;const Rn=xf;class Lf extends Et{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,r,s){t===0&&e===this.length()?this.format(r,s):super.formatAt(t,e,r,s)}formats(){return this.statics.formats(this.domNode,this.scroll)}}const Lt=Lf,_f={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Sf=100,Xe=class extends Pt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(r=>{this.update(r)}),this.observer.observe(this.domNode,_f),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const r=this.registry.find(t,e);return r?r.scroll===this?r:e?this.find(r.scroll.domNode.parentNode,!0):null:null}query(t,e=D.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(r=>{r.remove()}):super.deleteAt(t,e)}formatAt(t,e,r,s){this.update(),super.formatAt(t,e,r,s)}insertAt(t,e,r){this.update(),super.insertAt(t,e,r)}optimize(t=[],e={}){super.optimize(e);const r=e.mutationsMap||new WeakMap;let s=Array.from(this.observer.takeRecords());for(;s.length>0;)t.push(s.pop());const i=(u,h=!0)=>{u==null||u===this||u.domNode.parentNode!=null&&(r.has(u.domNode)||r.set(u.domNode,[]),h&&i(u.parent))},o=u=>{r.has(u.domNode)&&(u instanceof Pt&&u.children.forEach(o),r.delete(u.domNode),u.optimize(e))};let a=t;for(let u=0;a.length>0;u+=1){if(u>=Sf)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(h=>{const p=this.find(h.target,!0);p!=null&&(p.domNode===h.target&&(h.type==="childList"?(i(this.find(h.previousSibling,!1)),Array.from(h.addedNodes).forEach(v=>{const f=this.find(v,!1);i(f,!1),f instanceof Pt&&f.children.forEach(m=>{i(m,!1)})})):h.type==="attributes"&&i(p.prev)),i(p))}),this.children.forEach(o),a=Array.from(this.observer.takeRecords()),s=a.slice();s.length>0;)t.push(s.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const r=new WeakMap;t.map(s=>{const i=this.find(s.target,!0);return i==null?null:r.has(i.domNode)?(r.get(i.domNode).push(s),null):(r.set(i.domNode,[s]),i)}).forEach(s=>{s!=null&&s!==this&&r.has(s.domNode)&&s.update(r.get(s.domNode)||[],e)}),e.mutationsMap=r,r.has(this.domNode)&&super.update(r.get(this.domNode),e),this.optimize(t,e)}};Xe.blotName="scroll",Xe.defaultChild=Dr,Xe.allowedChildren=[Dr,Rn],Xe.scope=D.BLOCK_BLOT,Xe.tagName="DIV";let qf=Xe;const mi=qf,Xs=class co extends Et{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,r){r==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,r)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof co&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const r=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(r,this.next||void 0),this.text=this.statics.value(this.domNode),r}update(t,e){t.some(r=>r.type==="characterData"&&r.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};Xs.blotName="text",Xs.scope=D.INLINE_BLOT;let Of=Xs;const qn=Of,Cf=Object.freeze(Object.defineProperty({__proto__:null,Attributor:Qt,AttributorStore:kn,BlockBlot:Dr,ClassAttributor:Ht,ContainerBlot:Rn,EmbedBlot:Lt,InlineBlot:pi,LeafBlot:Et,ParentBlot:Pt,Registry:nr,Scope:D,ScrollBlot:mi,StyleAttributor:ve,TextBlot:qn},Symbol.toStringTag,{value:"Module"}));function uo(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var gn={exports:{}},qs,sl;function If(){if(sl)return qs;sl=1;var n=-1,t=1,e=0;function r(g,L,b,O,S){if(g===L)return g?[[e,g]]:[];if(b!=null){var x=kt(g,L,b);if(x)return x}var q=a(g,L),M=g.substring(0,q);g=g.substring(q),L=L.substring(q),q=h(g,L);var j=g.substring(g.length-q);g=g.substring(0,g.length-q),L=L.substring(0,L.length-q);var k=s(g,L);return M&&k.unshift([e,M]),j&&k.push([e,j]),_(k,S),O&&v(k),k}function s(g,L){var b;if(!g)return[[t,L]];if(!L)return[[n,g]];var O=g.length>L.length?g:L,S=g.length>L.length?L:g,x=O.indexOf(S);if(x!==-1)return b=[[t,O.substring(0,x)],[e,S],[t,O.substring(x+S.length)]],g.length>L.length&&(b[0][0]=b[2][0]=n),b;if(S.length===1)return[[n,g],[t,L]];var q=p(g,L);if(q){var M=q[0],j=q[1],k=q[2],K=q[3],F=q[4],G=r(M,k),Z=r(j,K);return G.concat([[e,F]],Z)}return i(g,L)}function i(g,L){for(var b=g.length,O=L.length,S=Math.ceil((b+O)/2),x=S,q=2*S,M=new Array(q),j=new Array(q),k=0;k<q;k++)M[k]=-1,j[k]=-1;M[x+1]=0,j[x+1]=0;for(var K=b-O,F=K%2!==0,G=0,Z=0,U=0,it=0,ot=0;ot<S;ot++){for(var V=-ot+G;V<=ot-Z;V+=2){var Q=x+V,Y;V===-ot||V!==ot&&M[Q-1]<M[Q+1]?Y=M[Q+1]:Y=M[Q-1]+1;for(var J=Y-V;Y<b&&J<O&&g.charAt(Y)===L.charAt(J);)Y++,J++;if(M[Q]=Y,Y>b)Z+=2;else if(J>O)G+=2;else if(F){var rt=x+K-V;if(rt>=0&&rt<q&&j[rt]!==-1){var nt=b-j[rt];if(Y>=nt)return o(g,L,Y,J)}}}for(var ct=-ot+U;ct<=ot-it;ct+=2){var rt=x+ct,nt;ct===-ot||ct!==ot&&j[rt-1]<j[rt+1]?nt=j[rt+1]:nt=j[rt-1]+1;for(var mt=nt-ct;nt<b&&mt<O&&g.charAt(b-nt-1)===L.charAt(O-mt-1);)nt++,mt++;if(j[rt]=nt,nt>b)it+=2;else if(mt>O)U+=2;else if(!F){var Q=x+K-ct;if(Q>=0&&Q<q&&M[Q]!==-1){var Y=M[Q],J=x+Y-Q;if(nt=b-nt,Y>=nt)return o(g,L,Y,J)}}}}return[[n,g],[t,L]]}function o(g,L,b,O){var S=g.substring(0,b),x=L.substring(0,O),q=g.substring(b),M=L.substring(O),j=r(S,x),k=r(q,M);return j.concat(k)}function a(g,L){if(!g||!L||g.charAt(0)!==L.charAt(0))return 0;for(var b=0,O=Math.min(g.length,L.length),S=O,x=0;b<S;)g.substring(x,S)==L.substring(x,S)?(b=S,x=b):O=S,S=Math.floor((O-b)/2+b);return C(g.charCodeAt(S-1))&&S--,S}function u(g,L){var b=g.length,O=L.length;if(b==0||O==0)return 0;b>O?g=g.substring(b-O):b<O&&(L=L.substring(0,b));var S=Math.min(b,O);if(g==L)return S;for(var x=0,q=1;;){var M=g.substring(S-q),j=L.indexOf(M);if(j==-1)return x;q+=j,(j==0||g.substring(S-q)==L.substring(0,q))&&(x=q,q++)}}function h(g,L){if(!g||!L||g.slice(-1)!==L.slice(-1))return 0;for(var b=0,O=Math.min(g.length,L.length),S=O,x=0;b<S;)g.substring(g.length-S,g.length-x)==L.substring(L.length-S,L.length-x)?(b=S,x=b):O=S,S=Math.floor((O-b)/2+b);return H(g.charCodeAt(g.length-S))&&S--,S}function p(g,L){var b=g.length>L.length?g:L,O=g.length>L.length?L:g;if(b.length<4||O.length*2<b.length)return null;function S(Z,U,it){for(var ot=Z.substring(it,it+Math.floor(Z.length/4)),V=-1,Q="",Y,J,rt,nt;(V=U.indexOf(ot,V+1))!==-1;){var ct=a(Z.substring(it),U.substring(V)),mt=h(Z.substring(0,it),U.substring(0,V));Q.length<mt+ct&&(Q=U.substring(V-mt,V)+U.substring(V,V+ct),Y=Z.substring(0,it-mt),J=Z.substring(it+ct),rt=U.substring(0,V-mt),nt=U.substring(V+ct))}return Q.length*2>=Z.length?[Y,J,rt,nt,Q]:null}var x=S(b,O,Math.ceil(b.length/4)),q=S(b,O,Math.ceil(b.length/2)),M;if(!x&&!q)return null;q?x?M=x[4].length>q[4].length?x:q:M=q:M=x;var j,k,K,F;g.length>L.length?(j=M[0],k=M[1],K=M[2],F=M[3]):(K=M[0],F=M[1],j=M[2],k=M[3]);var G=M[4];return[j,k,K,F,G]}function v(g){for(var L=!1,b=[],O=0,S=null,x=0,q=0,M=0,j=0,k=0;x<g.length;)g[x][0]==e?(b[O++]=x,q=j,M=k,j=0,k=0,S=g[x][1]):(g[x][0]==t?j+=g[x][1].length:k+=g[x][1].length,S&&S.length<=Math.max(q,M)&&S.length<=Math.max(j,k)&&(g.splice(b[O-1],0,[n,S]),g[b[O-1]+1][0]=t,O--,O--,x=O>0?b[O-1]:-1,q=0,M=0,j=0,k=0,S=null,L=!0)),x++;for(L&&_(g),T(g),x=1;x<g.length;){if(g[x-1][0]==n&&g[x][0]==t){var K=g[x-1][1],F=g[x][1],G=u(K,F),Z=u(F,K);G>=Z?(G>=K.length/2||G>=F.length/2)&&(g.splice(x,0,[e,F.substring(0,G)]),g[x-1][1]=K.substring(0,K.length-G),g[x+1][1]=F.substring(G),x++):(Z>=K.length/2||Z>=F.length/2)&&(g.splice(x,0,[e,K.substring(0,Z)]),g[x-1][0]=t,g[x-1][1]=F.substring(0,F.length-Z),g[x+1][0]=n,g[x+1][1]=K.substring(Z),x++),x++}x++}}var f=/[^a-zA-Z0-9]/,m=/\s/,y=/[\r\n]/,E=/\n\r?\n$/,N=/^\r?\n\r?\n/;function T(g){function L(Z,U){if(!Z||!U)return 6;var it=Z.charAt(Z.length-1),ot=U.charAt(0),V=it.match(f),Q=ot.match(f),Y=V&&it.match(m),J=Q&&ot.match(m),rt=Y&&it.match(y),nt=J&&ot.match(y),ct=rt&&Z.match(E),mt=nt&&U.match(N);return ct||mt?5:rt||nt?4:V&&!Y&&J?3:Y||J?2:V||Q?1:0}for(var b=1;b<g.length-1;){if(g[b-1][0]==e&&g[b+1][0]==e){var O=g[b-1][1],S=g[b][1],x=g[b+1][1],q=h(O,S);if(q){var M=S.substring(S.length-q);O=O.substring(0,O.length-q),S=M+S.substring(0,S.length-q),x=M+x}for(var j=O,k=S,K=x,F=L(O,S)+L(S,x);S.charAt(0)===x.charAt(0);){O+=S.charAt(0),S=S.substring(1)+x.charAt(0),x=x.substring(1);var G=L(O,S)+L(S,x);G>=F&&(F=G,j=O,k=S,K=x)}g[b-1][1]!=j&&(j?g[b-1][1]=j:(g.splice(b-1,1),b--),g[b][1]=k,K?g[b+1][1]=K:(g.splice(b+1,1),b--))}b++}}function _(g,L){g.push([e,""]);for(var b=0,O=0,S=0,x="",q="",M;b<g.length;){if(b<g.length-1&&!g[b][1]){g.splice(b,1);continue}switch(g[b][0]){case t:S++,q+=g[b][1],b++;break;case n:O++,x+=g[b][1],b++;break;case e:var j=b-S-O-1;if(L){if(j>=0&&et(g[j][1])){var k=g[j][1].slice(-1);if(g[j][1]=g[j][1].slice(0,-1),x=k+x,q=k+q,!g[j][1]){g.splice(j,1),b--;var K=j-1;g[K]&&g[K][0]===t&&(S++,q=g[K][1]+q,K--),g[K]&&g[K][0]===n&&(O++,x=g[K][1]+x,K--),j=K}}if(P(g[b][1])){var k=g[b][1].charAt(0);g[b][1]=g[b][1].slice(1),x+=k,q+=k}}if(b<g.length-1&&!g[b][1]){g.splice(b,1);break}if(x.length>0||q.length>0){x.length>0&&q.length>0&&(M=a(q,x),M!==0&&(j>=0?g[j][1]+=q.substring(0,M):(g.splice(0,0,[e,q.substring(0,M)]),b++),q=q.substring(M),x=x.substring(M)),M=h(q,x),M!==0&&(g[b][1]=q.substring(q.length-M)+g[b][1],q=q.substring(0,q.length-M),x=x.substring(0,x.length-M)));var F=S+O;x.length===0&&q.length===0?(g.splice(b-F,F),b=b-F):x.length===0?(g.splice(b-F,F,[t,q]),b=b-F+1):q.length===0?(g.splice(b-F,F,[n,x]),b=b-F+1):(g.splice(b-F,F,[n,x],[t,q]),b=b-F+2)}b!==0&&g[b-1][0]===e?(g[b-1][1]+=g[b][1],g.splice(b,1)):b++,S=0,O=0,x="",q="";break}}g[g.length-1][1]===""&&g.pop();var G=!1;for(b=1;b<g.length-1;)g[b-1][0]===e&&g[b+1][0]===e&&(g[b][1].substring(g[b][1].length-g[b-1][1].length)===g[b-1][1]?(g[b][1]=g[b-1][1]+g[b][1].substring(0,g[b][1].length-g[b-1][1].length),g[b+1][1]=g[b-1][1]+g[b+1][1],g.splice(b-1,1),G=!0):g[b][1].substring(0,g[b+1][1].length)==g[b+1][1]&&(g[b-1][1]+=g[b+1][1],g[b][1]=g[b][1].substring(g[b+1][1].length)+g[b+1][1],g.splice(b+1,1),G=!0)),b++;G&&_(g,L)}function C(g){return g>=55296&&g<=56319}function H(g){return g>=56320&&g<=57343}function P(g){return H(g.charCodeAt(0))}function et(g){return C(g.charCodeAt(g.length-1))}function at(g){for(var L=[],b=0;b<g.length;b++)g[b][1].length>0&&L.push(g[b]);return L}function ft(g,L,b,O){return et(g)||P(O)?null:at([[e,g],[n,L],[t,b],[e,O]])}function kt(g,L,b){var O=typeof b=="number"?{index:b,length:0}:b.oldRange,S=typeof b=="number"?null:b.newRange,x=g.length,q=L.length;if(O.length===0&&(S===null||S.length===0)){var M=O.index,j=g.slice(0,M),k=g.slice(M),K=S?S.index:null;t:{var F=M+q-x;if(K!==null&&K!==F||F<0||F>q)break t;var G=L.slice(0,F),Z=L.slice(F);if(Z!==k)break t;var U=Math.min(M,F),it=j.slice(0,U),ot=G.slice(0,U);if(it!==ot)break t;var V=j.slice(U),Q=G.slice(U);return ft(it,V,Q,k)}t:{if(K!==null&&K!==M)break t;var Y=M,G=L.slice(0,Y),Z=L.slice(Y);if(G!==j)break t;var J=Math.min(x-Y,q-Y),rt=k.slice(k.length-J),nt=Z.slice(Z.length-J);if(rt!==nt)break t;var V=k.slice(0,k.length-J),Q=Z.slice(0,Z.length-J);return ft(j,V,Q,rt)}}if(O.length>0&&S&&S.length===0)t:{var it=g.slice(0,O.index),rt=g.slice(O.index+O.length),U=it.length,J=rt.length;if(q<U+J)break t;var ot=L.slice(0,U),nt=L.slice(q-J);if(it!==ot||rt!==nt)break t;var V=g.slice(U,x-J),Q=L.slice(U,q-J);return ft(it,V,Q,rt)}return null}function xt(g,L,b,O){return r(g,L,b,O,!0)}return xt.INSERT=t,xt.DELETE=n,xt.EQUAL=e,qs=xt,qs}var _r={exports:{}};_r.exports;var il;function ho(){return il||(il=1,function(n,t){var e=200,r="__lodash_hash_undefined__",s=9007199254740991,i="[object Arguments]",o="[object Array]",a="[object Boolean]",u="[object Date]",h="[object Error]",p="[object Function]",v="[object GeneratorFunction]",f="[object Map]",m="[object Number]",y="[object Object]",E="[object Promise]",N="[object RegExp]",T="[object Set]",_="[object String]",C="[object Symbol]",H="[object WeakMap]",P="[object ArrayBuffer]",et="[object DataView]",at="[object Float32Array]",ft="[object Float64Array]",kt="[object Int8Array]",xt="[object Int16Array]",g="[object Int32Array]",L="[object Uint8Array]",b="[object Uint8ClampedArray]",O="[object Uint16Array]",S="[object Uint32Array]",x=/[\\^$.*+?()[\]{}|]/g,q=/\w*$/,M=/^\[object .+?Constructor\]$/,j=/^(?:0|[1-9]\d*)$/,k={};k[i]=k[o]=k[P]=k[et]=k[a]=k[u]=k[at]=k[ft]=k[kt]=k[xt]=k[g]=k[f]=k[m]=k[y]=k[N]=k[T]=k[_]=k[C]=k[L]=k[b]=k[O]=k[S]=!0,k[h]=k[p]=k[H]=!1;var K=typeof globalThis=="object"&&globalThis&&globalThis.Object===Object&&globalThis,F=typeof self=="object"&&self&&self.Object===Object&&self,G=K||F||Function("return this")(),Z=t&&!t.nodeType&&t,U=Z&&!0&&n&&!n.nodeType&&n,it=U&&U.exports===Z;function ot(l,c){return l.set(c[0],c[1]),l}function V(l,c){return l.add(c),l}function Q(l,c){for(var d=-1,A=l?l.length:0;++d<A&&c(l[d],d,l)!==!1;);return l}function Y(l,c){for(var d=-1,A=c.length,z=l.length;++d<A;)l[z+d]=c[d];return l}function J(l,c,d,A){for(var z=-1,$=l?l.length:0;++z<$;)d=c(d,l[z],z,l);return d}function rt(l,c){for(var d=-1,A=Array(l);++d<l;)A[d]=c(d);return A}function nt(l,c){return l==null?void 0:l[c]}function ct(l){var c=!1;if(l!=null&&typeof l.toString!="function")try{c=!!(l+"")}catch{}return c}function mt(l){var c=-1,d=Array(l.size);return l.forEach(function(A,z){d[++c]=[z,A]}),d}function ar(l,c){return function(d){return l(c(d))}}function Vr(l){var c=-1,d=Array(l.size);return l.forEach(function(A){d[++c]=A}),d}var jn=Array.prototype,$n=Function.prototype,Be=Object.prototype,cr=G["__core-js_shared__"],Gr=function(){var l=/[^.]+$/.exec(cr&&cr.keys&&cr.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),Zr=$n.toString,Kt=Be.hasOwnProperty,De=Be.toString,Pn=RegExp("^"+Zr.call(Kt).replace(x,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ee=it?G.Buffer:void 0,je=G.Symbol,ur=G.Uint8Array,_t=ar(Object.getPrototypeOf,Object),Wr=Object.create,Xr=Be.propertyIsEnumerable,Un=jn.splice,hr=Object.getOwnPropertySymbols,$e=Ee?Ee.isBuffer:void 0,Yr=ar(Object.keys,Object),Pe=Mt(G,"DataView"),Ae=Mt(G,"Map"),Rt=Mt(G,"Promise"),Ue=Mt(G,"Set"),fr=Mt(G,"WeakMap"),we=Mt(Object,"create"),dr=wt(Pe),Ne=wt(Ae),gr=wt(Rt),pr=wt(Ue),mr=wt(fr),he=je?je.prototype:void 0,Qr=he?he.valueOf:void 0;function ee(l){var c=-1,d=l?l.length:0;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function Hn(){this.__data__=we?we(null):{}}function Fn(l){return this.has(l)&&delete this.__data__[l]}function zn(l){var c=this.__data__;if(we){var d=c[l];return d===r?void 0:d}return Kt.call(c,l)?c[l]:void 0}function Jr(l){var c=this.__data__;return we?c[l]!==void 0:Kt.call(c,l)}function br(l,c){var d=this.__data__;return d[l]=we&&c===void 0?r:c,this}ee.prototype.clear=Hn,ee.prototype.delete=Fn,ee.prototype.get=zn,ee.prototype.has=Jr,ee.prototype.set=br;function dt(l){var c=-1,d=l?l.length:0;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function Kn(){this.__data__=[]}function Vn(l){var c=this.__data__,d=Fe(c,l);if(d<0)return!1;var A=c.length-1;return d==A?c.pop():Un.call(c,d,1),!0}function Gn(l){var c=this.__data__,d=Fe(c,l);return d<0?void 0:c[d][1]}function Zn(l){return Fe(this.__data__,l)>-1}function Wn(l,c){var d=this.__data__,A=Fe(d,l);return A<0?d.push([l,c]):d[A][1]=c,this}dt.prototype.clear=Kn,dt.prototype.delete=Vn,dt.prototype.get=Gn,dt.prototype.has=Zn,dt.prototype.set=Wn;function bt(l){var c=-1,d=l?l.length:0;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function Xn(){this.__data__={hash:new ee,map:new(Ae||dt),string:new ee}}function Yn(l){return xe(this,l).delete(l)}function Qn(l){return xe(this,l).get(l)}function Jn(l){return xe(this,l).has(l)}function ts(l,c){return xe(this,l).set(l,c),this}bt.prototype.clear=Xn,bt.prototype.delete=Yn,bt.prototype.get=Qn,bt.prototype.has=Jn,bt.prototype.set=ts;function Nt(l){this.__data__=new dt(l)}function es(){this.__data__=new dt}function rs(l){return this.__data__.delete(l)}function ns(l){return this.__data__.get(l)}function ss(l){return this.__data__.has(l)}function is(l,c){var d=this.__data__;if(d instanceof dt){var A=d.__data__;if(!Ae||A.length<e-1)return A.push([l,c]),this;d=this.__data__=new bt(A)}return d.set(l,c),this}Nt.prototype.clear=es,Nt.prototype.delete=rs,Nt.prototype.get=ns,Nt.prototype.has=ss,Nt.prototype.set=is;function He(l,c){var d=Ar(l)||Ke(l)?rt(l.length,String):[],A=d.length,z=!!A;for(var $ in l)Kt.call(l,$)&&!(z&&($=="length"||vs($,A)))&&d.push($);return d}function tn(l,c,d){var A=l[c];(!(Kt.call(l,c)&&ln(A,d))||d===void 0&&!(c in l))&&(l[c]=d)}function Fe(l,c){for(var d=l.length;d--;)if(ln(l[d][0],c))return d;return-1}function Vt(l,c){return l&&Er(c,Nr(c),l)}function yr(l,c,d,A,z,$,X){var W;if(A&&(W=$?A(l,z,$,X):A(l)),W!==void 0)return W;if(!Zt(l))return l;var ut=Ar(l);if(ut){if(W=bs(l),!c)return gs(l,W)}else{var tt=ne(l),yt=tt==p||tt==v;if(on(l))return ze(l,c);if(tt==y||tt==i||yt&&!$){if(ct(l))return $?l:{};if(W=Gt(yt?{}:l),!c)return ps(l,Vt(W,l))}else{if(!k[tt])return $?l:{};W=ys(l,tt,yr,c)}}X||(X=new Nt);var Tt=X.get(l);if(Tt)return Tt;if(X.set(l,W),!ut)var ht=d?ms(l):Nr(l);return Q(ht||l,function(vt,gt){ht&&(gt=vt,vt=l[gt]),tn(W,gt,yr(vt,c,d,A,gt,l,X))}),W}function ls(l){return Zt(l)?Wr(l):{}}function os(l,c,d){var A=c(l);return Ar(l)?A:Y(A,d(l))}function as(l){return De.call(l)}function cs(l){if(!Zt(l)||As(l))return!1;var c=wr(l)||ct(l)?Pn:M;return c.test(wt(l))}function us(l){if(!nn(l))return Yr(l);var c=[];for(var d in Object(l))Kt.call(l,d)&&d!="constructor"&&c.push(d);return c}function ze(l,c){if(c)return l.slice();var d=new l.constructor(l.length);return l.copy(d),d}function vr(l){var c=new l.constructor(l.byteLength);return new ur(c).set(new ur(l)),c}function Te(l,c){var d=c?vr(l.buffer):l.buffer;return new l.constructor(d,l.byteOffset,l.byteLength)}function en(l,c,d){var A=c?d(mt(l),!0):mt(l);return J(A,ot,new l.constructor)}function rn(l){var c=new l.constructor(l.source,q.exec(l));return c.lastIndex=l.lastIndex,c}function hs(l,c,d){var A=c?d(Vr(l),!0):Vr(l);return J(A,V,new l.constructor)}function fs(l){return Qr?Object(Qr.call(l)):{}}function ds(l,c){var d=c?vr(l.buffer):l.buffer;return new l.constructor(d,l.byteOffset,l.length)}function gs(l,c){var d=-1,A=l.length;for(c||(c=Array(A));++d<A;)c[d]=l[d];return c}function Er(l,c,d,A){d||(d={});for(var z=-1,$=c.length;++z<$;){var X=c[z],W=void 0;tn(d,X,W===void 0?l[X]:W)}return d}function ps(l,c){return Er(l,re(l),c)}function ms(l){return os(l,Nr,re)}function xe(l,c){var d=l.__data__;return Es(c)?d[typeof c=="string"?"string":"hash"]:d.map}function Mt(l,c){var d=nt(l,c);return cs(d)?d:void 0}var re=hr?ar(hr,Object):Ns,ne=as;(Pe&&ne(new Pe(new ArrayBuffer(1)))!=et||Ae&&ne(new Ae)!=f||Rt&&ne(Rt.resolve())!=E||Ue&&ne(new Ue)!=T||fr&&ne(new fr)!=H)&&(ne=function(l){var c=De.call(l),d=c==y?l.constructor:void 0,A=d?wt(d):void 0;if(A)switch(A){case dr:return et;case Ne:return f;case gr:return E;case pr:return T;case mr:return H}return c});function bs(l){var c=l.length,d=l.constructor(c);return c&&typeof l[0]=="string"&&Kt.call(l,"index")&&(d.index=l.index,d.input=l.input),d}function Gt(l){return typeof l.constructor=="function"&&!nn(l)?ls(_t(l)):{}}function ys(l,c,d,A){var z=l.constructor;switch(c){case P:return vr(l);case a:case u:return new z(+l);case et:return Te(l,A);case at:case ft:case kt:case xt:case g:case L:case b:case O:case S:return ds(l,A);case f:return en(l,A,d);case m:case _:return new z(l);case N:return rn(l);case T:return hs(l,A,d);case C:return fs(l)}}function vs(l,c){return c=c??s,!!c&&(typeof l=="number"||j.test(l))&&l>-1&&l%1==0&&l<c}function Es(l){var c=typeof l;return c=="string"||c=="number"||c=="symbol"||c=="boolean"?l!=="__proto__":l===null}function As(l){return!!Gr&&Gr in l}function nn(l){var c=l&&l.constructor,d=typeof c=="function"&&c.prototype||Be;return l===d}function wt(l){if(l!=null){try{return Zr.call(l)}catch{}try{return l+""}catch{}}return""}function sn(l){return yr(l,!0,!0)}function ln(l,c){return l===c||l!==l&&c!==c}function Ke(l){return ws(l)&&Kt.call(l,"callee")&&(!Xr.call(l,"callee")||De.call(l)==i)}var Ar=Array.isArray;function Ve(l){return l!=null&&an(l.length)&&!wr(l)}function ws(l){return cn(l)&&Ve(l)}var on=$e||Ts;function wr(l){var c=Zt(l)?De.call(l):"";return c==p||c==v}function an(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=s}function Zt(l){var c=typeof l;return!!l&&(c=="object"||c=="function")}function cn(l){return!!l&&typeof l=="object"}function Nr(l){return Ve(l)?He(l):us(l)}function Ns(){return[]}function Ts(){return!1}n.exports=sn}(_r,_r.exports)),_r.exports}var Sr={exports:{}};Sr.exports;var ll;function fo(){return ll||(ll=1,function(n,t){var e=200,r="__lodash_hash_undefined__",s=1,i=2,o=9007199254740991,a="[object Arguments]",u="[object Array]",h="[object AsyncFunction]",p="[object Boolean]",v="[object Date]",f="[object Error]",m="[object Function]",y="[object GeneratorFunction]",E="[object Map]",N="[object Number]",T="[object Null]",_="[object Object]",C="[object Promise]",H="[object Proxy]",P="[object RegExp]",et="[object Set]",at="[object String]",ft="[object Symbol]",kt="[object Undefined]",xt="[object WeakMap]",g="[object ArrayBuffer]",L="[object DataView]",b="[object Float32Array]",O="[object Float64Array]",S="[object Int8Array]",x="[object Int16Array]",q="[object Int32Array]",M="[object Uint8Array]",j="[object Uint8ClampedArray]",k="[object Uint16Array]",K="[object Uint32Array]",F=/[\\^$.*+?()[\]{}|]/g,G=/^\[object .+?Constructor\]$/,Z=/^(?:0|[1-9]\d*)$/,U={};U[b]=U[O]=U[S]=U[x]=U[q]=U[M]=U[j]=U[k]=U[K]=!0,U[a]=U[u]=U[g]=U[p]=U[L]=U[v]=U[f]=U[m]=U[E]=U[N]=U[_]=U[P]=U[et]=U[at]=U[xt]=!1;var it=typeof globalThis=="object"&&globalThis&&globalThis.Object===Object&&globalThis,ot=typeof self=="object"&&self&&self.Object===Object&&self,V=it||ot||Function("return this")(),Q=t&&!t.nodeType&&t,Y=Q&&!0&&n&&!n.nodeType&&n,J=Y&&Y.exports===Q,rt=J&&it.process,nt=function(){try{return rt&&rt.binding&&rt.binding("util")}catch{}}(),ct=nt&&nt.isTypedArray;function mt(l,c){for(var d=-1,A=l==null?0:l.length,z=0,$=[];++d<A;){var X=l[d];c(X,d,l)&&($[z++]=X)}return $}function ar(l,c){for(var d=-1,A=c.length,z=l.length;++d<A;)l[z+d]=c[d];return l}function Vr(l,c){for(var d=-1,A=l==null?0:l.length;++d<A;)if(c(l[d],d,l))return!0;return!1}function jn(l,c){for(var d=-1,A=Array(l);++d<l;)A[d]=c(d);return A}function $n(l){return function(c){return l(c)}}function Be(l,c){return l.has(c)}function cr(l,c){return l==null?void 0:l[c]}function Gr(l){var c=-1,d=Array(l.size);return l.forEach(function(A,z){d[++c]=[z,A]}),d}function Zr(l,c){return function(d){return l(c(d))}}function Kt(l){var c=-1,d=Array(l.size);return l.forEach(function(A){d[++c]=A}),d}var De=Array.prototype,Pn=Function.prototype,Ee=Object.prototype,je=V["__core-js_shared__"],ur=Pn.toString,_t=Ee.hasOwnProperty,Wr=function(){var l=/[^.]+$/.exec(je&&je.keys&&je.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),Xr=Ee.toString,Un=RegExp("^"+ur.call(_t).replace(F,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),hr=J?V.Buffer:void 0,$e=V.Symbol,Yr=V.Uint8Array,Pe=Ee.propertyIsEnumerable,Ae=De.splice,Rt=$e?$e.toStringTag:void 0,Ue=Object.getOwnPropertySymbols,fr=hr?hr.isBuffer:void 0,we=Zr(Object.keys,Object),dr=re(V,"DataView"),Ne=re(V,"Map"),gr=re(V,"Promise"),pr=re(V,"Set"),mr=re(V,"WeakMap"),he=re(Object,"create"),Qr=wt(dr),ee=wt(Ne),Hn=wt(gr),Fn=wt(pr),zn=wt(mr),Jr=$e?$e.prototype:void 0,br=Jr?Jr.valueOf:void 0;function dt(l){var c=-1,d=l==null?0:l.length;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function Kn(){this.__data__=he?he(null):{},this.size=0}function Vn(l){var c=this.has(l)&&delete this.__data__[l];return this.size-=c?1:0,c}function Gn(l){var c=this.__data__;if(he){var d=c[l];return d===r?void 0:d}return _t.call(c,l)?c[l]:void 0}function Zn(l){var c=this.__data__;return he?c[l]!==void 0:_t.call(c,l)}function Wn(l,c){var d=this.__data__;return this.size+=this.has(l)?0:1,d[l]=he&&c===void 0?r:c,this}dt.prototype.clear=Kn,dt.prototype.delete=Vn,dt.prototype.get=Gn,dt.prototype.has=Zn,dt.prototype.set=Wn;function bt(l){var c=-1,d=l==null?0:l.length;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function Xn(){this.__data__=[],this.size=0}function Yn(l){var c=this.__data__,d=ze(c,l);if(d<0)return!1;var A=c.length-1;return d==A?c.pop():Ae.call(c,d,1),--this.size,!0}function Qn(l){var c=this.__data__,d=ze(c,l);return d<0?void 0:c[d][1]}function Jn(l){return ze(this.__data__,l)>-1}function ts(l,c){var d=this.__data__,A=ze(d,l);return A<0?(++this.size,d.push([l,c])):d[A][1]=c,this}bt.prototype.clear=Xn,bt.prototype.delete=Yn,bt.prototype.get=Qn,bt.prototype.has=Jn,bt.prototype.set=ts;function Nt(l){var c=-1,d=l==null?0:l.length;for(this.clear();++c<d;){var A=l[c];this.set(A[0],A[1])}}function es(){this.size=0,this.__data__={hash:new dt,map:new(Ne||bt),string:new dt}}function rs(l){var c=Mt(this,l).delete(l);return this.size-=c?1:0,c}function ns(l){return Mt(this,l).get(l)}function ss(l){return Mt(this,l).has(l)}function is(l,c){var d=Mt(this,l),A=d.size;return d.set(l,c),this.size+=d.size==A?0:1,this}Nt.prototype.clear=es,Nt.prototype.delete=rs,Nt.prototype.get=ns,Nt.prototype.has=ss,Nt.prototype.set=is;function He(l){var c=-1,d=l==null?0:l.length;for(this.__data__=new Nt;++c<d;)this.add(l[c])}function tn(l){return this.__data__.set(l,r),this}function Fe(l){return this.__data__.has(l)}He.prototype.add=He.prototype.push=tn,He.prototype.has=Fe;function Vt(l){var c=this.__data__=new bt(l);this.size=c.size}function yr(){this.__data__=new bt,this.size=0}function ls(l){var c=this.__data__,d=c.delete(l);return this.size=c.size,d}function os(l){return this.__data__.get(l)}function as(l){return this.__data__.has(l)}function cs(l,c){var d=this.__data__;if(d instanceof bt){var A=d.__data__;if(!Ne||A.length<e-1)return A.push([l,c]),this.size=++d.size,this;d=this.__data__=new Nt(A)}return d.set(l,c),this.size=d.size,this}Vt.prototype.clear=yr,Vt.prototype.delete=ls,Vt.prototype.get=os,Vt.prototype.has=as,Vt.prototype.set=cs;function us(l,c){var d=Ke(l),A=!d&&ln(l),z=!d&&!A&&Ve(l),$=!d&&!A&&!z&&cn(l),X=d||A||z||$,W=X?jn(l.length,String):[],ut=W.length;for(var tt in l)_t.call(l,tt)&&!(X&&(tt=="length"||z&&(tt=="offset"||tt=="parent")||$&&(tt=="buffer"||tt=="byteLength"||tt=="byteOffset")||ys(tt,ut)))&&W.push(tt);return W}function ze(l,c){for(var d=l.length;d--;)if(sn(l[d][0],c))return d;return-1}function vr(l,c,d){var A=c(l);return Ke(l)?A:ar(A,d(l))}function Te(l){return l==null?l===void 0?kt:T:Rt&&Rt in Object(l)?ne(l):nn(l)}function en(l){return Zt(l)&&Te(l)==a}function rn(l,c,d,A,z){return l===c?!0:l==null||c==null||!Zt(l)&&!Zt(c)?l!==l&&c!==c:hs(l,c,d,A,rn,z)}function hs(l,c,d,A,z,$){var X=Ke(l),W=Ke(c),ut=X?u:Gt(l),tt=W?u:Gt(c);ut=ut==a?_:ut,tt=tt==a?_:tt;var yt=ut==_,Tt=tt==_,ht=ut==tt;if(ht&&Ve(l)){if(!Ve(c))return!1;X=!0,yt=!1}if(ht&&!yt)return $||($=new Vt),X||cn(l)?Er(l,c,d,A,z,$):ps(l,c,ut,d,A,z,$);if(!(d&s)){var vt=yt&&_t.call(l,"__wrapped__"),gt=Tt&&_t.call(c,"__wrapped__");if(vt||gt){var fe=vt?l.value():l,se=gt?c.value():c;return $||($=new Vt),z(fe,se,d,A,$)}}return ht?($||($=new Vt),ms(l,c,d,A,z,$)):!1}function fs(l){if(!an(l)||Es(l))return!1;var c=on(l)?Un:G;return c.test(wt(l))}function ds(l){return Zt(l)&&wr(l.length)&&!!U[Te(l)]}function gs(l){if(!As(l))return we(l);var c=[];for(var d in Object(l))_t.call(l,d)&&d!="constructor"&&c.push(d);return c}function Er(l,c,d,A,z,$){var X=d&s,W=l.length,ut=c.length;if(W!=ut&&!(X&&ut>W))return!1;var tt=$.get(l);if(tt&&$.get(c))return tt==c;var yt=-1,Tt=!0,ht=d&i?new He:void 0;for($.set(l,c),$.set(c,l);++yt<W;){var vt=l[yt],gt=c[yt];if(A)var fe=X?A(gt,vt,yt,c,l,$):A(vt,gt,yt,l,c,$);if(fe!==void 0){if(fe)continue;Tt=!1;break}if(ht){if(!Vr(c,function(se,Le){if(!Be(ht,Le)&&(vt===se||z(vt,se,d,A,$)))return ht.push(Le)})){Tt=!1;break}}else if(!(vt===gt||z(vt,gt,d,A,$))){Tt=!1;break}}return $.delete(l),$.delete(c),Tt}function ps(l,c,d,A,z,$,X){switch(d){case L:if(l.byteLength!=c.byteLength||l.byteOffset!=c.byteOffset)return!1;l=l.buffer,c=c.buffer;case g:return!(l.byteLength!=c.byteLength||!$(new Yr(l),new Yr(c)));case p:case v:case N:return sn(+l,+c);case f:return l.name==c.name&&l.message==c.message;case P:case at:return l==c+"";case E:var W=Gr;case et:var ut=A&s;if(W||(W=Kt),l.size!=c.size&&!ut)return!1;var tt=X.get(l);if(tt)return tt==c;A|=i,X.set(l,c);var yt=Er(W(l),W(c),A,z,$,X);return X.delete(l),yt;case ft:if(br)return br.call(l)==br.call(c)}return!1}function ms(l,c,d,A,z,$){var X=d&s,W=xe(l),ut=W.length,tt=xe(c),yt=tt.length;if(ut!=yt&&!X)return!1;for(var Tt=ut;Tt--;){var ht=W[Tt];if(!(X?ht in c:_t.call(c,ht)))return!1}var vt=$.get(l);if(vt&&$.get(c))return vt==c;var gt=!0;$.set(l,c),$.set(c,l);for(var fe=X;++Tt<ut;){ht=W[Tt];var se=l[ht],Le=c[ht];if(A)var _i=X?A(Le,se,ht,c,l,$):A(se,Le,ht,l,c,$);if(!(_i===void 0?se===Le||z(se,Le,d,A,$):_i)){gt=!1;break}fe||(fe=ht=="constructor")}if(gt&&!fe){var un=l.constructor,hn=c.constructor;un!=hn&&"constructor"in l&&"constructor"in c&&!(typeof un=="function"&&un instanceof un&&typeof hn=="function"&&hn instanceof hn)&&(gt=!1)}return $.delete(l),$.delete(c),gt}function xe(l){return vr(l,Nr,bs)}function Mt(l,c){var d=l.__data__;return vs(c)?d[typeof c=="string"?"string":"hash"]:d.map}function re(l,c){var d=cr(l,c);return fs(d)?d:void 0}function ne(l){var c=_t.call(l,Rt),d=l[Rt];try{l[Rt]=void 0;var A=!0}catch{}var z=Xr.call(l);return A&&(c?l[Rt]=d:delete l[Rt]),z}var bs=Ue?function(l){return l==null?[]:(l=Object(l),mt(Ue(l),function(c){return Pe.call(l,c)}))}:Ns,Gt=Te;(dr&&Gt(new dr(new ArrayBuffer(1)))!=L||Ne&&Gt(new Ne)!=E||gr&&Gt(gr.resolve())!=C||pr&&Gt(new pr)!=et||mr&&Gt(new mr)!=xt)&&(Gt=function(l){var c=Te(l),d=c==_?l.constructor:void 0,A=d?wt(d):"";if(A)switch(A){case Qr:return L;case ee:return E;case Hn:return C;case Fn:return et;case zn:return xt}return c});function ys(l,c){return c=c??o,!!c&&(typeof l=="number"||Z.test(l))&&l>-1&&l%1==0&&l<c}function vs(l){var c=typeof l;return c=="string"||c=="number"||c=="symbol"||c=="boolean"?l!=="__proto__":l===null}function Es(l){return!!Wr&&Wr in l}function As(l){var c=l&&l.constructor,d=typeof c=="function"&&c.prototype||Ee;return l===d}function nn(l){return Xr.call(l)}function wt(l){if(l!=null){try{return ur.call(l)}catch{}try{return l+""}catch{}}return""}function sn(l,c){return l===c||l!==l&&c!==c}var ln=en(function(){return arguments}())?en:function(l){return Zt(l)&&_t.call(l,"callee")&&!Pe.call(l,"callee")},Ke=Array.isArray;function Ar(l){return l!=null&&wr(l.length)&&!on(l)}var Ve=fr||Ts;function ws(l,c){return rn(l,c)}function on(l){if(!an(l))return!1;var c=Te(l);return c==m||c==y||c==h||c==H}function wr(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=o}function an(l){var c=typeof l;return l!=null&&(c=="object"||c=="function")}function Zt(l){return l!=null&&typeof l=="object"}var cn=ct?$n(ct):ds;function Nr(l){return Ar(l)?us(l):gs(l)}function Ns(){return[]}function Ts(){return!1}n.exports=ws}(Sr,Sr.exports)),Sr.exports}var pn={},ol;function kf(){if(ol)return pn;ol=1,Object.defineProperty(pn,"__esModule",{value:!0});const n=ho(),t=fo();var e;return function(r){function s(u={},h={},p=!1){typeof u!="object"&&(u={}),typeof h!="object"&&(h={});let v=n(h);p||(v=Object.keys(v).reduce((f,m)=>(v[m]!=null&&(f[m]=v[m]),f),{}));for(const f in u)u[f]!==void 0&&h[f]===void 0&&(v[f]=u[f]);return Object.keys(v).length>0?v:void 0}r.compose=s;function i(u={},h={}){typeof u!="object"&&(u={}),typeof h!="object"&&(h={});const p=Object.keys(u).concat(Object.keys(h)).reduce((v,f)=>(t(u[f],h[f])||(v[f]=h[f]===void 0?null:h[f]),v),{});return Object.keys(p).length>0?p:void 0}r.diff=i;function o(u={},h={}){u=u||{};const p=Object.keys(h).reduce((v,f)=>(h[f]!==u[f]&&u[f]!==void 0&&(v[f]=h[f]),v),{});return Object.keys(u).reduce((v,f)=>(u[f]!==h[f]&&h[f]===void 0&&(v[f]=null),v),p)}r.invert=o;function a(u,h,p=!1){if(typeof u!="object")return h;if(typeof h!="object")return;if(!p)return h;const v=Object.keys(h).reduce((f,m)=>(u[m]===void 0&&(f[m]=h[m]),f),{});return Object.keys(v).length>0?v:void 0}r.transform=a}(e||(e={})),pn.default=e,pn}var mn={},al;function go(){if(al)return mn;al=1,Object.defineProperty(mn,"__esModule",{value:!0});var n;return function(t){function e(r){return typeof r.delete=="number"?r.delete:typeof r.retain=="number"?r.retain:typeof r.retain=="object"&&r.retain!==null?1:typeof r.insert=="string"?r.insert.length:1}t.length=e}(n||(n={})),mn.default=n,mn}var bn={},cl;function Rf(){if(cl)return bn;cl=1,Object.defineProperty(bn,"__esModule",{value:!0});const n=go();class t{constructor(r){this.ops=r,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(r){r||(r=1/0);const s=this.ops[this.index];if(s){const i=this.offset,o=n.default.length(s);if(r>=o-i?(r=o-i,this.index+=1,this.offset=0):this.offset+=r,typeof s.delete=="number")return{delete:r};{const a={};return s.attributes&&(a.attributes=s.attributes),typeof s.retain=="number"?a.retain=r:typeof s.retain=="object"&&s.retain!==null?a.retain=s.retain:typeof s.insert=="string"?a.insert=s.insert.substr(i,r):a.insert=s.insert,a}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?n.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const r=this.ops[this.index];return r?typeof r.delete=="number"?"delete":typeof r.retain=="number"||typeof r.retain=="object"&&r.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const r=this.offset,s=this.index,i=this.next(),o=this.ops.slice(this.index);return this.offset=r,this.index=s,[i].concat(o)}}else return[]}}return bn.default=t,bn}var ul;function Mf(){return ul||(ul=1,function(n,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=If(),r=ho(),s=fo(),i=kf();t.AttributeMap=i.default;const o=go();t.Op=o.default;const a=Rf();t.OpIterator=a.default;const u="\0",h=(v,f)=>{if(typeof v!="object"||v===null)throw new Error(`cannot retain a ${typeof v}`);if(typeof f!="object"||f===null)throw new Error(`cannot retain a ${typeof f}`);const m=Object.keys(v)[0];if(!m||m!==Object.keys(f)[0])throw new Error(`embed types not matched: ${m} != ${Object.keys(f)[0]}`);return[m,v[m],f[m]]};class p{constructor(f){Array.isArray(f)?this.ops=f:f!=null&&Array.isArray(f.ops)?this.ops=f.ops:this.ops=[]}static registerEmbed(f,m){this.handlers[f]=m}static unregisterEmbed(f){delete this.handlers[f]}static getHandler(f){const m=this.handlers[f];if(!m)throw new Error(`no handlers for embed type "${f}"`);return m}insert(f,m){const y={};return typeof f=="string"&&f.length===0?this:(y.insert=f,m!=null&&typeof m=="object"&&Object.keys(m).length>0&&(y.attributes=m),this.push(y))}delete(f){return f<=0?this:this.push({delete:f})}retain(f,m){if(typeof f=="number"&&f<=0)return this;const y={retain:f};return m!=null&&typeof m=="object"&&Object.keys(m).length>0&&(y.attributes=m),this.push(y)}push(f){let m=this.ops.length,y=this.ops[m-1];if(f=r(f),typeof y=="object"){if(typeof f.delete=="number"&&typeof y.delete=="number")return this.ops[m-1]={delete:y.delete+f.delete},this;if(typeof y.delete=="number"&&f.insert!=null&&(m-=1,y=this.ops[m-1],typeof y!="object"))return this.ops.unshift(f),this;if(s(f.attributes,y.attributes)){if(typeof f.insert=="string"&&typeof y.insert=="string")return this.ops[m-1]={insert:y.insert+f.insert},typeof f.attributes=="object"&&(this.ops[m-1].attributes=f.attributes),this;if(typeof f.retain=="number"&&typeof y.retain=="number")return this.ops[m-1]={retain:y.retain+f.retain},typeof f.attributes=="object"&&(this.ops[m-1].attributes=f.attributes),this}}return m===this.ops.length?this.ops.push(f):this.ops.splice(m,0,f),this}chop(){const f=this.ops[this.ops.length-1];return f&&typeof f.retain=="number"&&!f.attributes&&this.ops.pop(),this}filter(f){return this.ops.filter(f)}forEach(f){this.ops.forEach(f)}map(f){return this.ops.map(f)}partition(f){const m=[],y=[];return this.forEach(E=>{(f(E)?m:y).push(E)}),[m,y]}reduce(f,m){return this.ops.reduce(f,m)}changeLength(){return this.reduce((f,m)=>m.insert?f+o.default.length(m):m.delete?f-m.delete:f,0)}length(){return this.reduce((f,m)=>f+o.default.length(m),0)}slice(f=0,m=1/0){const y=[],E=new a.default(this.ops);let N=0;for(;N<m&&E.hasNext();){let T;N<f?T=E.next(f-N):(T=E.next(m-N),y.push(T)),N+=o.default.length(T)}return new p(y)}compose(f){const m=new a.default(this.ops),y=new a.default(f.ops),E=[],N=y.peek();if(N!=null&&typeof N.retain=="number"&&N.attributes==null){let _=N.retain;for(;m.peekType()==="insert"&&m.peekLength()<=_;)_-=m.peekLength(),E.push(m.next());N.retain-_>0&&y.next(N.retain-_)}const T=new p(E);for(;m.hasNext()||y.hasNext();)if(y.peekType()==="insert")T.push(y.next());else if(m.peekType()==="delete")T.push(m.next());else{const _=Math.min(m.peekLength(),y.peekLength()),C=m.next(_),H=y.next(_);if(H.retain){const P={};if(typeof C.retain=="number")P.retain=typeof H.retain=="number"?_:H.retain;else if(typeof H.retain=="number")C.retain==null?P.insert=C.insert:P.retain=C.retain;else{const at=C.retain==null?"insert":"retain",[ft,kt,xt]=h(C[at],H.retain),g=p.getHandler(ft);P[at]={[ft]:g.compose(kt,xt,at==="retain")}}const et=i.default.compose(C.attributes,H.attributes,typeof C.retain=="number");if(et&&(P.attributes=et),T.push(P),!y.hasNext()&&s(T.ops[T.ops.length-1],P)){const at=new p(m.rest());return T.concat(at).chop()}}else typeof H.delete=="number"&&(typeof C.retain=="number"||typeof C.retain=="object"&&C.retain!==null)&&T.push(H)}return T.chop()}concat(f){const m=new p(this.ops.slice());return f.ops.length>0&&(m.push(f.ops[0]),m.ops=m.ops.concat(f.ops.slice(1))),m}diff(f,m){if(this.ops===f.ops)return new p;const y=[this,f].map(C=>C.map(H=>{if(H.insert!=null)return typeof H.insert=="string"?H.insert:u;const P=C===f?"on":"with";throw new Error("diff() called "+P+" non-document")}).join("")),E=new p,N=e(y[0],y[1],m,!0),T=new a.default(this.ops),_=new a.default(f.ops);return N.forEach(C=>{let H=C[1].length;for(;H>0;){let P=0;switch(C[0]){case e.INSERT:P=Math.min(_.peekLength(),H),E.push(_.next(P));break;case e.DELETE:P=Math.min(H,T.peekLength()),T.next(P),E.delete(P);break;case e.EQUAL:P=Math.min(T.peekLength(),_.peekLength(),H);const et=T.next(P),at=_.next(P);s(et.insert,at.insert)?E.retain(P,i.default.diff(et.attributes,at.attributes)):E.push(at).delete(P);break}H-=P}}),E.chop()}eachLine(f,m=`
`){const y=new a.default(this.ops);let E=new p,N=0;for(;y.hasNext();){if(y.peekType()!=="insert")return;const T=y.peek(),_=o.default.length(T)-y.peekLength(),C=typeof T.insert=="string"?T.insert.indexOf(m,_)-_:-1;if(C<0)E.push(y.next());else if(C>0)E.push(y.next(C));else{if(f(E,y.next(1).attributes||{},N)===!1)return;N+=1,E=new p}}E.length()>0&&f(E,{},N)}invert(f){const m=new p;return this.reduce((y,E)=>{if(E.insert)m.delete(o.default.length(E));else{if(typeof E.retain=="number"&&E.attributes==null)return m.retain(E.retain),y+E.retain;if(E.delete||typeof E.retain=="number"){const N=E.delete||E.retain;return f.slice(y,y+N).forEach(_=>{E.delete?m.push(_):E.retain&&E.attributes&&m.retain(o.default.length(_),i.default.invert(E.attributes,_.attributes))}),y+N}else if(typeof E.retain=="object"&&E.retain!==null){const N=f.slice(y,y+1),T=new a.default(N.ops).next(),[_,C,H]=h(E.retain,T.insert),P=p.getHandler(_);return m.retain({[_]:P.invert(C,H)},i.default.invert(E.attributes,T.attributes)),y+1}}return y},0),m.chop()}transform(f,m=!1){if(m=!!m,typeof f=="number")return this.transformPosition(f,m);const y=f,E=new a.default(this.ops),N=new a.default(y.ops),T=new p;for(;E.hasNext()||N.hasNext();)if(E.peekType()==="insert"&&(m||N.peekType()!=="insert"))T.retain(o.default.length(E.next()));else if(N.peekType()==="insert")T.push(N.next());else{const _=Math.min(E.peekLength(),N.peekLength()),C=E.next(_),H=N.next(_);if(C.delete)continue;if(H.delete)T.push(H);else{const P=C.retain,et=H.retain;let at=typeof et=="object"&&et!==null?et:_;if(typeof P=="object"&&P!==null&&typeof et=="object"&&et!==null){const ft=Object.keys(P)[0];if(ft===Object.keys(et)[0]){const kt=p.getHandler(ft);kt&&(at={[ft]:kt.transform(P[ft],et[ft],m)})}}T.retain(at,i.default.transform(C.attributes,H.attributes,m))}}return T.chop()}transformPosition(f,m=!1){m=!!m;const y=new a.default(this.ops);let E=0;for(;y.hasNext()&&E<=f;){const N=y.peekLength(),T=y.peekType();if(y.next(),T==="delete"){f-=Math.min(N,f-E);continue}else T==="insert"&&(E<f||!m)&&(f+=N);E+=N}return f}}p.Op=o.default,p.OpIterator=a.default,p.AttributeMap=i.default,p.handlers={},t.default=p,n.exports=p,n.exports.default=p}(gn,gn.exports)),gn.exports}var It=Mf();const B=uo(It);class Ft extends Lt{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}Ft.blotName="break";Ft.tagName="BR";let Ut=class extends qn{};const Bf={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Mn(n){return n.replace(/[&<>"']/g,t=>Bf[t])}const Wt=class Wt extends pi{static compare(t,e){const r=Wt.order.indexOf(t),s=Wt.order.indexOf(e);return r>=0||s>=0?r-s:t===e?0:t<e?-1:1}formatAt(t,e,r,s){if(Wt.compare(this.statics.blotName,r)<0&&this.scroll.query(r,D.BLOT)){const i=this.isolate(t,e);s&&i.wrap(r,s)}else super.formatAt(t,e,r,s)}optimize(t){if(super.optimize(t),this.parent instanceof Wt&&Wt.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}};I(Wt,"allowedChildren",[Wt,Ft,Lt,Ut]),I(Wt,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let Jt=Wt;const hl=1;class pt extends Dr{constructor(){super(...arguments);I(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=po(this)),this.cache.delta}deleteAt(e,r){super.deleteAt(e,r),this.cache={}}formatAt(e,r,s,i){r<=0||(this.scroll.query(s,D.BLOCK)?e+r===this.length()&&this.format(s,i):super.formatAt(e,Math.min(r,this.length()-e-1),s,i),this.cache={})}insertAt(e,r,s){if(s!=null){super.insertAt(e,r,s),this.cache={};return}if(r.length===0)return;const i=r.split(`
`),o=i.shift();o.length>0&&(e<this.length()-1||this.children.tail==null?super.insertAt(Math.min(e,this.length()-1),o):this.children.tail.insertAt(this.children.tail.length(),o),this.cache={});let a=this;i.reduce((u,h)=>(a=a.split(u,!0),a.insertAt(0,h),h.length),e+o.length)}insertBefore(e,r){const{head:s}=this.children;super.insertBefore(e,r),s instanceof Ft&&s.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+hl),this.cache.length}moveChildren(e,r){super.moveChildren(e,r),this.cache={}}optimize(e){super.optimize(e),this.cache={}}path(e){return super.path(e,!0)}removeChild(e){super.removeChild(e),this.cache={}}split(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r&&(e===0||e>=this.length()-hl)){const i=this.clone();return e===0?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}const s=super.split(e,r);return this.cache={},s}}pt.blotName="block";pt.tagName="P";pt.defaultChild=Ft;pt.allowedChildren=[Ft,Jt,Lt,Ut];class Ct extends Lt{attach(){super.attach(),this.attributes=new kn(this.domNode)}delta(){return new B().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const r=this.scroll.query(t,D.BLOCK_ATTRIBUTE);r!=null&&this.attributes.attribute(r,e)}formatAt(t,e,r,s){this.format(r,s)}insertAt(t,e,r){if(r!=null){super.insertAt(t,e,r);return}const s=e.split(`
`),i=s.pop(),o=s.map(u=>{const h=this.scroll.create(pt.blotName);return h.insertAt(0,u),h}),a=this.split(t);o.forEach(u=>{this.parent.insertBefore(u,a)}),i&&this.parent.insertBefore(this.scroll.create("text",i),a)}}Ct.scope=D.BLOCK_BLOT;function po(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return n.descendants(Et).reduce((e,r)=>r.length()===0?e:e.insert(r.value(),qt(r,{},t)),new B).insert(`
`,qt(n))}function qt(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return n==null||("formats"in n&&typeof n.formats=="function"&&(t={...t,...n.formats()},e&&delete t["code-token"]),n.parent==null||n.parent.statics.blotName==="scroll"||n.parent.statics.scope!==n.statics.scope)?t:qt(n.parent,t,e)}const St=class St extends Lt{static value(){}constructor(t,e,r){super(t,e),this.selection=r,this.textNode=document.createTextNode(St.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let r=this,s=0;for(;r!=null&&r.statics.scope!==D.BLOCK_BLOT;)s+=r.offset(r.parent),r=r.parent;r!=null&&(this.savedLength=St.CONTENTS.length,r.optimize(),r.formatAt(s,St.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof Ut?this.prev:null,r=e?e.length():0,s=this.next instanceof Ut?this.next:null,i=s?s.text:"",{textNode:o}=this,a=o.data.split(St.CONTENTS).join("");o.data=St.CONTENTS;let u;if(e)u=e,(a||s)&&(e.insertAt(e.length(),a+i),s&&s.remove());else if(s)u=s,s.insertAt(0,a);else{const h=document.createTextNode(a);u=this.scroll.create(h),this.parent.insertBefore(u,this)}if(this.remove(),t){const h=(f,m)=>e&&f===e.domNode?m:f===o?r+m-1:s&&f===s.domNode?r+a.length+m:null,p=h(t.start.node,t.start.offset),v=h(t.end.node,t.end.offset);if(p!==null&&v!==null)return{startNode:u.domNode,startOffset:p,endNode:u.domNode,endOffset:v}}return null}update(t,e){if(t.some(r=>r.type==="characterData"&&r.target===this.textNode)){const r=this.restore();r&&(e.range=r)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=St.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}};I(St,"blotName","cursor"),I(St,"className","ql-cursor"),I(St,"tagName","span"),I(St,"CONTENTS","\uFEFF");let sr=St;var Os={exports:{}},fl;function Df(){return fl||(fl=1,function(n){var t=Object.prototype.hasOwnProperty,e="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(e=!1));function s(u,h,p){this.fn=u,this.context=h,this.once=p||!1}function i(u,h,p,v,f){if(typeof p!="function")throw new TypeError("The listener must be a function");var m=new s(p,v||u,f),y=e?e+h:h;return u._events[y]?u._events[y].fn?u._events[y]=[u._events[y],m]:u._events[y].push(m):(u._events[y]=m,u._eventsCount++),u}function o(u,h){--u._eventsCount===0?u._events=new r:delete u._events[h]}function a(){this._events=new r,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],p,v;if(this._eventsCount===0)return h;for(v in p=this._events)t.call(p,v)&&h.push(e?v.slice(1):v);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(p)):h},a.prototype.listeners=function(h){var p=e?e+h:h,v=this._events[p];if(!v)return[];if(v.fn)return[v.fn];for(var f=0,m=v.length,y=new Array(m);f<m;f++)y[f]=v[f].fn;return y},a.prototype.listenerCount=function(h){var p=e?e+h:h,v=this._events[p];return v?v.fn?1:v.length:0},a.prototype.emit=function(h,p,v,f,m,y){var E=e?e+h:h;if(!this._events[E])return!1;var N=this._events[E],T=arguments.length,_,C;if(N.fn){switch(N.once&&this.removeListener(h,N.fn,void 0,!0),T){case 1:return N.fn.call(N.context),!0;case 2:return N.fn.call(N.context,p),!0;case 3:return N.fn.call(N.context,p,v),!0;case 4:return N.fn.call(N.context,p,v,f),!0;case 5:return N.fn.call(N.context,p,v,f,m),!0;case 6:return N.fn.call(N.context,p,v,f,m,y),!0}for(C=1,_=new Array(T-1);C<T;C++)_[C-1]=arguments[C];N.fn.apply(N.context,_)}else{var H=N.length,P;for(C=0;C<H;C++)switch(N[C].once&&this.removeListener(h,N[C].fn,void 0,!0),T){case 1:N[C].fn.call(N[C].context);break;case 2:N[C].fn.call(N[C].context,p);break;case 3:N[C].fn.call(N[C].context,p,v);break;case 4:N[C].fn.call(N[C].context,p,v,f);break;default:if(!_)for(P=1,_=new Array(T-1);P<T;P++)_[P-1]=arguments[P];N[C].fn.apply(N[C].context,_)}}return!0},a.prototype.on=function(h,p,v){return i(this,h,p,v,!1)},a.prototype.once=function(h,p,v){return i(this,h,p,v,!0)},a.prototype.removeListener=function(h,p,v,f){var m=e?e+h:h;if(!this._events[m])return this;if(!p)return o(this,m),this;var y=this._events[m];if(y.fn)y.fn===p&&(!f||y.once)&&(!v||y.context===v)&&o(this,m);else{for(var E=0,N=[],T=y.length;E<T;E++)(y[E].fn!==p||f&&!y[E].once||v&&y[E].context!==v)&&N.push(y[E]);N.length?this._events[m]=N.length===1?N[0]:N:o(this,m)}return this},a.prototype.removeAllListeners=function(h){var p;return h?(p=e?e+h:h,this._events[p]&&o(this,p)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,n.exports=a}(Os)),Os.exports}var jf=Df();const $f=uo(jf),Ys=new WeakMap,Qs=["error","warn","log","info"];let Js="warn";function mo(n){if(Js&&Qs.indexOf(n)<=Qs.indexOf(Js)){for(var t=arguments.length,e=new Array(t>1?t-1:0),r=1;r<t;r++)e[r-1]=arguments[r];console[n](...e)}}function ue(n){return Qs.reduce((t,e)=>(t[e]=mo.bind(console,e,n),t),{})}ue.level=n=>{Js=n};mo.level=ue.level;const Cs=ue("quill:events"),Pf=["selectionchange","mousedown","mouseup","click"];Pf.forEach(n=>{document.addEventListener(n,function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];Array.from(document.querySelectorAll(".ql-container")).forEach(s=>{const i=Ys.get(s);i&&i.emitter&&i.emitter.handleDOM(...e)})})});class R extends $f{constructor(){super(),this.domListeners={},this.on("error",Cs.error)}emit(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return Cs.log.call(Cs,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),s=1;s<e;s++)r[s-1]=arguments[s];(this.domListeners[t.type]||[]).forEach(i=>{let{node:o,handler:a}=i;(t.target===o||o.contains(t.target))&&a(t,...r)})}listenDOM(t,e,r){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:r})}}I(R,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),I(R,"sources",{API:"api",SILENT:"silent",USER:"user"});const Is=ue("quill:selection");class qe{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class Uf{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new qe(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,R.sources.USER),1)}),this.emitter.on(R.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const r=this.getNativeRange();r!=null&&r.start.node!==this.cursor.textNode&&this.emitter.once(R.events.SCROLL_UPDATE,(s,i)=>{try{this.root.contains(r.start.node)&&this.root.contains(r.end.node)&&this.setNativeRange(r.start.node,r.start.offset,r.end.node,r.end.offset);const o=i.some(a=>a.type==="characterData"||a.type==="childList"||a.type==="attributes"&&a.target===this.root);this.update(o?R.sources.SILENT:s)}catch{}})}),this.emitter.on(R.events.SCROLL_OPTIMIZE,(r,s)=>{if(s.range){const{startNode:i,startOffset:o,endNode:a,endOffset:u}=s.range;this.setNativeRange(i,o,a,u),this.update(R.sources.SILENT)}}),this.update(R.sources.SILENT)}handleComposition(){this.emitter.on(R.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(R.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(R.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const r=this.getNativeRange();if(!(r==null||!r.native.collapsed||this.scroll.query(t,D.BLOCK))){if(r.start.node!==this.cursor.textNode){const s=this.scroll.find(r.start.node,!1);if(s==null)return;if(s instanceof Et){const i=s.split(r.start.offset);s.parent.insertBefore(this.cursor,i)}else s.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const r=this.scroll.length();t=Math.min(t,r-1),e=Math.min(t+e,r-1)-t;let s,[i,o]=this.scroll.leaf(t);if(i==null)return null;if(e>0&&o===i.length()){const[p]=this.scroll.leaf(t+1);if(p){const[v]=this.scroll.line(t),[f]=this.scroll.line(t+1);v===f&&(i=p,o=0)}}[s,o]=i.position(o,!0);const a=document.createRange();if(e>0)return a.setStart(s,o),[i,o]=this.scroll.leaf(t+e),i==null?null:([s,o]=i.position(o,!0),a.setEnd(s,o),a.getBoundingClientRect());let u="left",h;if(s instanceof Text){if(!s.data.length)return null;o<s.data.length?(a.setStart(s,o),a.setEnd(s,o+1)):(a.setStart(s,o-1),a.setEnd(s,o),u="right"),h=a.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;h=i.domNode.getBoundingClientRect(),o>0&&(u="right")}return{bottom:h.top+h.height,height:h.height,left:h[u],right:h[u],top:h.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const r=this.normalizeNative(e);return Is.info("getNativeRange",r),r}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&ks(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const r=e.map(o=>{const[a,u]=o,h=this.scroll.find(a,!0),p=h.offset(this.scroll);return u===0?p:h instanceof Et?p+h.index(a,u):p+h.length()}),s=Math.min(Math.max(...r),this.scroll.length()-1),i=Math.min(s,...r);return new qe(i,s-i)}normalizeNative(t){if(!ks(this.root,t.startContainer)||!t.collapsed&&!ks(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(r=>{let{node:s,offset:i}=r;for(;!(s instanceof Text)&&s.childNodes.length>0;)if(s.childNodes.length>i)s=s.childNodes[i],i=0;else if(s.childNodes.length===i)s=s.lastChild,s instanceof Text?i=s.data.length:s.childNodes.length>0?i=s.childNodes.length:i=s.childNodes.length+1;else break;r.node=s,r.offset=i}),e}rangeToNative(t){const e=this.scroll.length(),r=(s,i)=>{s=Math.min(e-1,s);const[o,a]=this.scroll.leaf(s);return o?o.position(a,i):[null,-1]};return[...r(t.index,!1),...r(t.index+t.length,!0)]}setNativeRange(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(Is.info("setNativeRange",t,e,r,s),t!=null&&(this.root.parentNode==null||t.parentNode==null||r.parentNode==null))return;const o=document.getSelection();if(o!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:a}=this.getNativeRange()||{};if(a==null||i||t!==a.startContainer||e!==a.startOffset||r!==a.endContainer||s!==a.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),r instanceof Element&&r.tagName==="BR"&&(s=Array.from(r.parentNode.childNodes).indexOf(r),r=r.parentNode);const u=document.createRange();u.setStart(t,e),u.setEnd(r,s),o.removeAllRanges(),o.addRange(u)}}else o.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:R.sources.API;if(typeof e=="string"&&(r=e,e=!1),Is.info("setRange",t),t!=null){const s=this.rangeToNative(t);this.setNativeRange(...s,e)}else this.setNativeRange(null);this.update(r)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:R.sources.USER;const e=this.lastRange,[r,s]=this.getRange();if(this.lastRange=r,this.lastNative=s,this.lastRange!=null&&(this.savedRange=this.lastRange),!gi(e,this.lastRange)){if(!this.composing&&s!=null&&s.native.collapsed&&s.start.node!==this.cursor.textNode){const o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}const i=[R.events.SELECTION_CHANGE,Je(this.lastRange),Je(e),t];this.emitter.emit(R.events.EDITOR_CHANGE,...i),t!==R.sources.SILENT&&this.emitter.emit(...i)}}}function ks(n,t){try{t.parentNode}catch{return!1}return n.contains(t)}const Hf=/^[ -~]*$/;class Ff{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const r=dl(t),s=new B;return Kf(r.ops.slice()).reduce((o,a)=>{const u=It.Op.length(a);let h=a.attributes||{},p=!1,v=!1;if(a.insert!=null){if(s.retain(u),typeof a.insert=="string"){const y=a.insert;v=!y.endsWith(`
`)&&(e<=o||!!this.scroll.descendant(Ct,o)[0]),this.scroll.insertAt(o,y);const[E,N]=this.scroll.line(o);let T=me({},qt(E));if(E instanceof pt){const[_]=E.descendant(Et,N);_&&(T=me(T,qt(_)))}h=It.AttributeMap.diff(T,h)||{}}else if(typeof a.insert=="object"){const y=Object.keys(a.insert)[0];if(y==null)return o;const E=this.scroll.query(y,D.INLINE)!=null;if(E)(e<=o||this.scroll.descendant(Ct,o)[0])&&(v=!0);else if(o>0){const[N,T]=this.scroll.descendant(Et,o-1);N instanceof Ut?N.value()[T]!==`
`&&(p=!0):N instanceof Lt&&N.statics.scope===D.INLINE_BLOT&&(p=!0)}if(this.scroll.insertAt(o,y,a.insert[y]),E){const[N]=this.scroll.descendant(Et,o);if(N){const T=me({},qt(N));h=It.AttributeMap.diff(T,h)||{}}}}e+=u}else if(s.push(a),a.retain!==null&&typeof a.retain=="object"){const y=Object.keys(a.retain)[0];if(y==null)return o;this.scroll.updateEmbedAt(o,y,a.retain[y])}Object.keys(h).forEach(y=>{this.scroll.formatAt(o,u,y,h[y])});const f=p?1:0,m=v?1:0;return e+=f+m,s.retain(f),s.delete(m),o+u+f+m},0),s.reduce((o,a)=>typeof a.delete=="number"?(this.scroll.deleteAt(o,a.delete),o):o+It.Op.length(a),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(r)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new B().retain(t).delete(e))}formatLine(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(r).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(o=>{o.format(i,r[i])})}),this.scroll.optimize();const s=new B().retain(t).retain(e,Je(r));return this.update(s)}formatText(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(r).forEach(i=>{this.scroll.formatAt(t,e,i,r[i])});const s=new B().retain(t).retain(e,Je(r));return this.update(s)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new B)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=[],s=[];e===0?this.scroll.path(t).forEach(a=>{const[u]=a;u instanceof pt?r.push(u):u instanceof Et&&s.push(u)}):(r=this.scroll.lines(t,e),s=this.scroll.descendants(Et,t,e));const[i,o]=[r,s].map(a=>{const u=a.shift();if(u==null)return{};let h=qt(u);for(;Object.keys(h).length>0;){const p=a.shift();if(p==null)return h;h=zf(qt(p),h)}return h});return{...i,...o}}getHTML(t,e){const[r,s]=this.scroll.line(t);if(r){const i=r.length();return r.length()>=s+e&&!(s===0&&e===i)?jr(r,s,e,!0):jr(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(r=>typeof r.insert=="string").map(r=>r.insert).join("")}insertContents(t,e){const r=dl(e),s=new B().retain(t).concat(r);return this.scroll.insertContents(t,r),this.update(s)}insertEmbed(t,e,r){return this.scroll.insertAt(t,e,r),this.update(new B().retain(t).insert({[e]:r}))}insertText(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(r).forEach(s=>{this.scroll.formatAt(t,e.length,s,r[s])}),this.update(new B().retain(t).insert(e,Je(r)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==pt.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof Ft}removeFormat(t,e){const r=this.getText(t,e),[s,i]=this.scroll.line(t+e);let o=0,a=new B;s!=null&&(o=s.length()-i,a=s.delta().slice(i,i+o-1).insert(`
`));const h=this.getContents(t,e+o).diff(new B().insert(r).concat(a)),p=new B().retain(t).concat(h);return this.applyDelta(p)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const s=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(Hf)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),o=qt(i),a=i.offset(this.scroll),u=e[0].oldValue.replace(sr.CONTENTS,""),h=new B().insert(u),p=new B().insert(i.value()),v=r&&{oldRange:gl(r.oldRange,-a),newRange:gl(r.newRange,-a)};t=new B().retain(a).concat(h.diff(p,v)).reduce((m,y)=>y.insert?m.insert(y.insert,o):m.push(y),new B),this.delta=s.compose(t)}else this.delta=this.getDelta(),(!t||!gi(s.compose(t),this.delta))&&(t=s.diff(this.delta,r));return t}}function Ye(n,t,e){if(n.length===0){const[m]=Rs(e.pop());return t<=0?`</li></${m}>`:`</li></${m}>${Ye([],t-1,e)}`}const[{child:r,offset:s,length:i,indent:o,type:a},...u]=n,[h,p]=Rs(a);if(o>t)return e.push(a),o===t+1?`<${h}><li${p}>${jr(r,s,i)}${Ye(u,o,e)}`:`<${h}><li>${Ye(n,t+1,e)}`;const v=e[e.length-1];if(o===t&&a===v)return`</li><li${p}>${jr(r,s,i)}${Ye(u,o,e)}`;const[f]=Rs(e.pop());return`</li></${f}>${Ye(n,t-1,e)}`}function jr(n,t,e){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in n&&typeof n.html=="function")return n.html(t,e);if(n instanceof Ut)return Mn(n.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(n instanceof Pt){if(n.statics.blotName==="list-container"){const h=[];return n.children.forEachAt(t,e,(p,v,f)=>{const m="formats"in p&&typeof p.formats=="function"?p.formats():{};h.push({child:p,offset:v,length:f,indent:m.indent||0,type:m.list})}),Ye(h,-1,[])}const s=[];if(n.children.forEachAt(t,e,(h,p,v)=>{s.push(jr(h,p,v))}),r||n.statics.blotName==="list")return s.join("");const{outerHTML:i,innerHTML:o}=n.domNode,[a,u]=i.split(`>${o}<`);return a==="<table"?`<table style="border: 1px solid #000;">${s.join("")}<${u}`:`${a}>${s.join("")}<${u}`}return n.domNode instanceof Element?n.domNode.outerHTML:""}function zf(n,t){return Object.keys(t).reduce((e,r)=>{if(n[r]==null)return e;const s=t[r];return s===n[r]?e[r]=s:Array.isArray(s)?s.indexOf(n[r])<0?e[r]=s.concat([n[r]]):e[r]=s:e[r]=[s,n[r]],e},{})}function Rs(n){const t=n==="ordered"?"ol":"ul";switch(n){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function dl(n){return n.reduce((t,e)=>{if(typeof e.insert=="string"){const r=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(r,e.attributes)}return t.push(e)},new B)}function gl(n,t){let{index:e,length:r}=n;return new qe(e+t,r)}function Kf(n){const t=[];return n.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((s,i)=>{i&&t.push({insert:`
`,attributes:e.attributes}),s&&t.push({insert:s,attributes:e.attributes})}):t.push(e)}),t}class zt{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}I(zt,"DEFAULTS",{});const yn="\uFEFF";class bi extends Lt{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(r=>{this.contentNode.appendChild(r)}),this.leftGuard=document.createTextNode(yn),this.rightGuard=document.createTextNode(yn),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,r;const s=t.data.split(yn).join("");if(t===this.leftGuard)if(this.prev instanceof Ut){const i=this.prev.length();this.prev.insertAt(i,s),e={startNode:this.prev.domNode,startOffset:i+s.length}}else r=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(r),this),e={startNode:r,startOffset:s.length};else t===this.rightGuard&&(this.next instanceof Ut?(this.next.insertAt(0,s),e={startNode:this.next.domNode,startOffset:s.length}):(r=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(r),this.next),e={startNode:r,startOffset:s.length}));return t.data=yn,e}update(t,e){t.forEach(r=>{if(r.type==="characterData"&&(r.target===this.leftGuard||r.target===this.rightGuard)){const s=this.restore(r.target);s&&(e.range=s)}})}}class Vf{constructor(t,e){I(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof bi)&&(this.emitter.emit(R.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(R.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(R.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(R.events.COMPOSITION_END,t),this.isComposing=!1}}const Ir=class Ir{constructor(t,e){I(this,"modules",{});this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}};I(Ir,"DEFAULTS",{modules:{}}),I(Ir,"themes",{default:Ir});let ir=Ir;const Gf=n=>n.parentElement||n.getRootNode().host||null,Zf=n=>{const t=n.getBoundingClientRect(),e="offsetWidth"in n&&Math.abs(t.width)/n.offsetWidth||1,r="offsetHeight"in n&&Math.abs(t.height)/n.offsetHeight||1;return{top:t.top,right:t.left+n.clientWidth*e,bottom:t.top+n.clientHeight*r,left:t.left}},vn=n=>{const t=parseInt(n,10);return Number.isNaN(t)?0:t},pl=(n,t,e,r,s,i)=>n<e&&t>r?0:n<e?-(e-n+s):t>r?t-n>r-e?n+s-e:t-r+i:0,Wf=(n,t)=>{var i,o,a;const e=n.ownerDocument;let r=t,s=n;for(;s;){const u=s===e.body,h=u?{top:0,right:((i=window.visualViewport)==null?void 0:i.width)??e.documentElement.clientWidth,bottom:((o=window.visualViewport)==null?void 0:o.height)??e.documentElement.clientHeight,left:0}:Zf(s),p=getComputedStyle(s),v=pl(r.left,r.right,h.left,h.right,vn(p.scrollPaddingLeft),vn(p.scrollPaddingRight)),f=pl(r.top,r.bottom,h.top,h.bottom,vn(p.scrollPaddingTop),vn(p.scrollPaddingBottom));if(v||f)if(u)(a=e.defaultView)==null||a.scrollBy(v,f);else{const{scrollLeft:m,scrollTop:y}=s;f&&(s.scrollTop+=f),v&&(s.scrollLeft+=v);const E=s.scrollLeft-m,N=s.scrollTop-y;r={left:r.left-E,top:r.top-N,right:r.right-E,bottom:r.bottom-N}}s=u||p.position==="fixed"?null:Gf(s)}},Xf=100,Yf=["block","break","cursor","inline","scroll","text"],Qf=(n,t,e)=>{const r=new nr;return Yf.forEach(s=>{const i=t.query(s);i&&r.register(i)}),n.forEach(s=>{let i=t.query(s);i||e.error(`Cannot register "${s}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;i;)if(r.register(i),i="blotName"in i?i.requiredContainer??null:null,o+=1,o>Xf){e.error(`Cycle detected in registering blot requiredContainer: "${s}"`);break}}),r},er=ue("quill"),En=new nr;Pt.uiClass="ql-ui";const Dt=class Dt{static debug(t){t===!0&&(t="log"),ue.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return Ys.get(t)||En.find(t,e)}static import(t){return this.imports[t]==null&&er.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),r="attrName"in t?t.attrName:t.blotName;typeof r=="string"?this.register(`formats/${r}`,t,e):Object.keys(t).forEach(s=>{this.register(s,t[s],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],r=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!r&&er.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&En.register(e),typeof e.register=="function"&&e.register(En)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=Jf(t,e),this.container=this.options.container,this.container==null){er.error("Invalid Quill container",t);return}this.options.debug&&Dt.debug(this.options.debug);const r=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",Ys.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new R;const s=mi.blotName,i=this.options.registry.query(s);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${s}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new Ff(this.scroll),this.selection=new Uf(this.scroll,this.emitter),this.composition=new Vf(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(R.events.EDITOR_CHANGE,o=>{o===R.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(R.events.SCROLL_UPDATE,(o,a)=>{const u=this.selection.lastRange,[h]=this.selection.getRange(),p=u&&h?{oldRange:u,newRange:h}:void 0;Bt.call(this,()=>this.editor.update(null,a,p),o)}),this.emitter.on(R.events.SCROLL_EMBED_UPDATE,(o,a)=>{const u=this.selection.lastRange,[h]=this.selection.getRange(),p=u&&h?{oldRange:u,newRange:h}:void 0;Bt.call(this,()=>{const v=new B().retain(o.offset(this)).retain({[o.statics.blotName]:a});return this.editor.update(v,[],p)},Dt.sources.USER)}),r){const o=this.clipboard.convert({html:`${r}<p><br></p>`,text:`
`});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const r=t;t=document.createElement("div"),t.classList.add(r)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,r){return[t,e,,r]=ie(t,e,r),Bt.call(this,()=>this.editor.deleteText(t,e),r,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:R.sources.API;return Bt.call(this,()=>{const s=this.getSelection(!0);let i=new B;if(s==null)return i;if(this.scroll.query(t,D.BLOCK))i=this.editor.formatLine(s.index,s.length,{[t]:e});else{if(s.length===0)return this.selection.format(t,e),i;i=this.editor.formatText(s.index,s.length,{[t]:e})}return this.setSelection(s,R.sources.SILENT),i},r)}formatLine(t,e,r,s,i){let o;return[t,e,o,i]=ie(t,e,r,s,i),Bt.call(this,()=>this.editor.formatLine(t,e,o),i,t,0)}formatText(t,e,r,s,i){let o;return[t,e,o,i]=ie(t,e,r,s,i),Bt.call(this,()=>this.editor.formatText(t,e,o),i,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=null;if(typeof t=="number"?r=this.selection.getBounds(t,e):r=this.selection.getBounds(t.index,t.length),!r)return null;const s=this.container.getBoundingClientRect();return{bottom:r.bottom-s.top,height:r.height,left:r.left-s.left,right:r.right-s.left,top:r.top-s.top,width:r.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=ie(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=ie(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=ie(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Dt.sources.API;return Bt.call(this,()=>this.editor.insertEmbed(t,e,r),s,t)}insertText(t,e,r,s,i){let o;return[t,,o,i]=ie(t,0,r,s,i),Bt.call(this,()=>this.editor.insertText(t,e,o),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,r){return[t,e,,r]=ie(t,e,r),Bt.call(this,()=>this.editor.removeFormat(t,e),r,t)}scrollRectIntoView(t){Wf(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:R.sources.API;return Bt.call(this,()=>{t=new B(t);const r=this.getLength(),s=this.editor.deleteText(0,r),i=this.editor.insertContents(0,t),o=this.editor.deleteText(this.getLength()-1,1);return s.compose(i).compose(o)},e)}setSelection(t,e,r){t==null?this.selection.setRange(null,e||Dt.sources.API):([t,e,,r]=ie(t,e,r),this.selection.setRange(new qe(Math.max(0,t),e),r),r!==R.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:R.sources.API;const r=new B().insert(t);return this.setContents(r,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:R.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:R.sources.API;return Bt.call(this,()=>(t=new B(t),this.editor.applyDelta(t)),e,!0)}};I(Dt,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:En,theme:"default"}),I(Dt,"events",R.events),I(Dt,"sources",R.sources),I(Dt,"version","2.0.3"),I(Dt,"imports",{delta:B,parchment:Cf,"core/module":zt,"core/theme":ir});let w=Dt;function ml(n){return typeof n=="string"?document.querySelector(n):n}function Ms(n){return Object.entries(n??{}).reduce((t,e)=>{let[r,s]=e;return{...t,[r]:s===!0?{}:s}},{})}function bl(n){return Object.fromEntries(Object.entries(n).filter(t=>t[1]!==void 0))}function Jf(n,t){const e=ml(n);if(!e)throw new Error("Invalid Quill container");const s=!t.theme||t.theme===w.DEFAULTS.theme?ir:w.import(`themes/${t.theme}`);if(!s)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:i,...o}=w.DEFAULTS,{modules:a,...u}=s.DEFAULTS;let h=Ms(t.modules);h!=null&&h.toolbar&&h.toolbar.constructor!==Object&&(h={...h,toolbar:{container:h.toolbar}});const p=me({},Ms(i),Ms(a),h),v={...o,...bl(u),...bl(t)};let f=t.registry;return f?t.formats&&er.warn('Ignoring "formats" option because "registry" is specified'):f=t.formats?Qf(t.formats,v.registry,er):v.registry,{...v,registry:f,container:e,theme:s,modules:Object.entries(p).reduce((m,y)=>{let[E,N]=y;if(!N)return m;const T=w.import(`modules/${E}`);return T==null?(er.error(`Cannot load ${E} module. Are you sure you registered it?`),m):{...m,[E]:me({},T.DEFAULTS||{},N)}},{}),bounds:ml(v.bounds)}}function Bt(n,t,e,r){if(!this.isEnabled()&&t===R.sources.USER&&!this.allowReadOnlyEdits)return new B;let s=e==null?null:this.getSelection();const i=this.editor.delta,o=n();if(s!=null&&(e===!0&&(e=s.index),r==null?s=yl(s,o,t):r!==0&&(s=yl(s,e,r,t)),this.setSelection(s,R.sources.SILENT)),o.length()>0){const a=[R.events.TEXT_CHANGE,o,i,t];this.emitter.emit(R.events.EDITOR_CHANGE,...a),t!==R.sources.SILENT&&this.emitter.emit(...a)}return o}function ie(n,t,e,r,s){let i={};return typeof n.index=="number"&&typeof n.length=="number"?typeof t!="number"?(s=r,r=e,e=t,t=n.length,n=n.index):(t=n.length,n=n.index):typeof t!="number"&&(s=r,r=e,e=t,t=0),typeof e=="object"?(i=e,s=r):typeof e=="string"&&(r!=null?i[e]=r:s=e),s=s||R.sources.API,[n,t,i,s]}function yl(n,t,e,r){const s=typeof e=="number"?e:0;if(n==null)return null;let i,o;return t&&typeof t.transformPosition=="function"?[i,o]=[n.index,n.index+n.length].map(a=>t.transformPosition(a,r!==R.sources.USER)):[i,o]=[n.index,n.index+n.length].map(a=>a<t||a===t&&r===R.sources.USER?a:s>=0?a+s:Math.max(t,a+s)),new qe(i,o-i)}class ke extends Rn{}function vl(n){return n instanceof pt||n instanceof Ct}function El(n){return typeof n.updateContent=="function"}class Qe extends mi{constructor(t,e,r){let{emitter:s}=r;super(t,e),this.emitter=s,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",i=>this.handleDragStart(i))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(R.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(R.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(R.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[r,s]=this.line(t),[i]=this.line(t+e);if(super.deleteAt(t,e),i!=null&&r!==i&&s>0){if(r instanceof Ct||i instanceof Ct){this.optimize();return}const o=i.children.head instanceof Ft?null:i.children.head;r.moveChildren(i,o),r.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,r,s){super.formatAt(t,e,r,s),this.optimize()}insertAt(t,e,r){if(t>=this.length())if(r==null||this.scroll.query(e,D.BLOCK)==null){const s=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(s),r==null&&e.endsWith(`
`)?s.insertAt(0,e.slice(0,-1),r):s.insertAt(0,e,r)}else{const s=this.scroll.create(e,r);this.appendChild(s)}else super.insertAt(t,e,r);this.optimize()}insertBefore(t,e){if(t.statics.scope===D.INLINE_BLOT){const r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(t),super.insertBefore(r,e)}else super.insertBefore(t,e)}insertContents(t,e){const r=this.deltaToRenderBlocks(e.concat(new B().insert(`
`))),s=r.pop();if(s==null)return;this.batchStart();const i=r.shift();if(i){const u=i.type==="block"&&(i.delta.length()===0||!this.descendant(Ct,t)[0]&&t<this.length()),h=i.type==="block"?i.delta:new B().insert({[i.key]:i.value});Bs(this,t,h);const p=i.type==="block"?1:0,v=t+h.length()+p;u&&this.insertAt(v-1,`
`);const f=qt(this.line(t)[0]),m=It.AttributeMap.diff(f,i.attributes)||{};Object.keys(m).forEach(y=>{this.formatAt(v-1,1,y,m[y])}),t=v}let[o,a]=this.children.find(t);if(r.length&&(o&&(o=o.split(a),a=0),r.forEach(u=>{if(u.type==="block"){const h=this.createBlock(u.attributes,o||void 0);Bs(h,0,u.delta)}else{const h=this.create(u.key,u.value);this.insertBefore(h,o||void 0),Object.keys(u.attributes).forEach(p=>{h.format(p,u.attributes[p])})}})),s.type==="block"&&s.delta.length()){const u=o?o.offset(o.scroll)+a:this.length();Bs(this,u,s.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[r,s]=e;return r instanceof Et?[r,s]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(vl,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const r=(s,i,o)=>{let a=[],u=o;return s.children.forEachAt(i,o,(h,p,v)=>{vl(h)?a.push(h):h instanceof Rn&&(a=a.concat(r(h,p,u))),u-=v}),a};return r(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(R.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=R.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(r=>{let{target:s}=r;const i=this.find(s,!0);return i&&!El(i)}),t.length>0&&this.emitter.emit(R.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(R.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,r){const[s]=this.descendant(i=>i instanceof Ct,t);s&&s.statics.blotName===e&&El(s)&&s.updateContent(r)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let r=new B;return t.forEach(s=>{const i=s==null?void 0:s.insert;if(i)if(typeof i=="string"){const o=i.split(`
`);o.slice(0,-1).forEach(u=>{r.insert(u,s.attributes),e.push({type:"block",delta:r,attributes:s.attributes??{}}),r=new B});const a=o[o.length-1];a&&r.insert(a,s.attributes)}else{const o=Object.keys(i)[0];if(!o)return;this.query(o,D.INLINE)?r.push(s):(r.length()&&e.push({type:"block",delta:r,attributes:{}}),r=new B,e.push({type:"blockEmbed",key:o,value:i[o],attributes:s.attributes??{}}))}}),r.length()&&e.push({type:"block",delta:r,attributes:{}}),e}createBlock(t,e){let r;const s={};Object.entries(t).forEach(a=>{let[u,h]=a;this.query(u,D.BLOCK&D.BLOT)!=null?r=u:s[u]=h});const i=this.create(r||this.statics.defaultChild.blotName,r?t[r]:void 0);this.insertBefore(i,e||void 0);const o=i.length();return Object.entries(s).forEach(a=>{let[u,h]=a;i.formatAt(0,o,u,h)}),i}}I(Qe,"blotName","scroll"),I(Qe,"className","ql-editor"),I(Qe,"tagName","DIV"),I(Qe,"defaultChild",pt),I(Qe,"allowedChildren",[pt,Ct,ke]);function Bs(n,t,e){e.reduce((r,s)=>{const i=It.Op.length(s);let o=s.attributes||{};if(s.insert!=null){if(typeof s.insert=="string"){const a=s.insert;n.insertAt(r,a);const[u]=n.descendant(Et,r),h=qt(u);o=It.AttributeMap.diff(h,o)||{}}else if(typeof s.insert=="object"){const a=Object.keys(s.insert)[0];if(a==null)return r;if(n.insertAt(r,a,s.insert[a]),n.scroll.query(a,D.INLINE)!=null){const[h]=n.descendant(Et,r),p=qt(h);o=It.AttributeMap.diff(p,o)||{}}}}return Object.keys(o).forEach(a=>{n.formatAt(r,i,a,o[a])}),r+i},t)}const yi={scope:D.BLOCK,whitelist:["right","center","justify"]},td=new Qt("align","align",yi),bo=new Ht("align","ql-align",yi),yo=new ve("align","text-align",yi);class vo extends ve{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(s=>`00${parseInt(s,10).toString(16)}`.slice(-2)).join("")}`):e}}const ed=new Ht("color","ql-color",{scope:D.INLINE}),vi=new vo("color","color",{scope:D.INLINE}),rd=new Ht("background","ql-bg",{scope:D.INLINE}),Ei=new vo("background","background-color",{scope:D.INLINE});class Re extends ke{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(r=>r.length()<=1?"":r.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${Mn(this.code(t,e))}
</pre>`}}class At extends pt{static register(){w.register(Re)}}I(At,"TAB","  ");class Ai extends Jt{}Ai.blotName="code";Ai.tagName="CODE";At.blotName="code-block";At.className="ql-code-block";At.tagName="DIV";Re.blotName="code-block-container";Re.className="ql-code-block-container";Re.tagName="DIV";Re.allowedChildren=[At];At.allowedChildren=[Ut,Ft,sr];At.requiredContainer=Re;const wi={scope:D.BLOCK,whitelist:["rtl"]},Eo=new Qt("direction","dir",wi),Ao=new Ht("direction","ql-direction",wi),wo=new ve("direction","direction",wi),No={scope:D.INLINE,whitelist:["serif","monospace"]},To=new Ht("font","ql-font",No);class nd extends ve{value(t){return super.value(t).replace(/["']/g,"")}}const xo=new nd("font","font-family",No),Lo=new Ht("size","ql-size",{scope:D.INLINE,whitelist:["small","large","huge"]}),_o=new ve("size","font-size",{scope:D.INLINE,whitelist:["10px","18px","32px"]}),sd=ue("quill:keyboard"),id=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class Bn extends zt{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(r=>!!e[r]!==t[r]&&e[r]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(r=>{this.options.bindings[r]&&this.addBinding(this.options.bindings[r])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=od(t);if(s==null){sd.warn("Attempted to add invalid keyboard binding",s);return}typeof e=="function"&&(e={handler:e}),typeof r=="function"&&(r={handler:r}),(Array.isArray(s.key)?s.key:[s.key]).forEach(o=>{const a={...s,key:o,...e,...r};this.bindings[a.key]=this.bindings[a.key]||[],this.bindings[a.key].push(a)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const s=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(T=>Bn.match(t,T));if(s.length===0)return;const i=w.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;const o=this.quill.getSelection();if(o==null||!this.quill.hasFocus())return;const[a,u]=this.quill.getLine(o.index),[h,p]=this.quill.getLeaf(o.index),[v,f]=o.length===0?[h,p]:this.quill.getLeaf(o.index+o.length),m=h instanceof qn?h.value().slice(0,p):"",y=v instanceof qn?v.value().slice(f):"",E={collapsed:o.length===0,empty:o.length===0&&a.length()<=1,format:this.quill.getFormat(o),line:a,offset:u,prefix:m,suffix:y,event:t};s.some(T=>{if(T.collapsed!=null&&T.collapsed!==E.collapsed||T.empty!=null&&T.empty!==E.empty||T.offset!=null&&T.offset!==E.offset)return!1;if(Array.isArray(T.format)){if(T.format.every(_=>E.format[_]==null))return!1}else if(typeof T.format=="object"&&!Object.keys(T.format).every(_=>T.format[_]===!0?E.format[_]!=null:T.format[_]===!1?E.format[_]==null:gi(T.format[_],E.format[_])))return!1;return T.prefix!=null&&!T.prefix.test(E.prefix)||T.suffix!=null&&!T.suffix.test(E.suffix)?!1:T.handler.call(this,o,E,T)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const r=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let s={};const[i]=this.quill.getLine(t.index);let o=new B().retain(t.index-r).delete(r);if(e.offset===0){const[a]=this.quill.getLine(t.index-1);if(a&&!(a.statics.blotName==="block"&&a.length()<=1)){const h=i.formats(),p=this.quill.getFormat(t.index-1,1);if(s=It.AttributeMap.diff(h,p)||{},Object.keys(s).length>0){const v=new B().retain(t.index+i.length()-2).retain(1,s);o=o.compose(v)}}}this.quill.updateContents(o,w.sources.USER),this.quill.focus()}handleDelete(t,e){const r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-r)return;let s={};const[i]=this.quill.getLine(t.index);let o=new B().retain(t.index).delete(r);if(e.offset>=i.length()-1){const[a]=this.quill.getLine(t.index+1);if(a){const u=i.formats(),h=this.quill.getFormat(t.index,1);s=It.AttributeMap.diff(u,h)||{},Object.keys(s).length>0&&(o=o.retain(a.length()-1).retain(1,s))}}this.quill.updateContents(o,w.sources.USER),this.quill.focus()}handleDeleteRange(t){Ni({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const r=Object.keys(e.format).reduce((i,o)=>(this.quill.scroll.query(o,D.BLOCK)&&!Array.isArray(e.format[o])&&(i[o]=e.format[o]),i),{}),s=new B().retain(t.index).delete(t.length).insert(`
`,r);this.quill.updateContents(s,w.sources.USER),this.quill.setSelection(t.index+1,w.sources.SILENT),this.quill.focus()}}const ld={bindings:{bold:Ds("bold"),italic:Ds("italic"),underline:Ds("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",w.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",w.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(n,t){t.format.indent!=null?this.quill.format("indent","-1",w.sources.USER):t.format.list!=null&&this.quill.format("list",!1,w.sources.USER)}},"indent code-block":Al(!0),"outdent code-block":Al(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(n){this.quill.deleteText(n.index-1,1,w.sources.USER)}},tab:{key:"Tab",handler(n,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new B().retain(n.index).delete(n.length).insert("	");return this.quill.updateContents(e,w.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index+1,w.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,w.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(n,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(n.index,n.length,e,w.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(n){const[t,e]=this.quill.getLine(n.index),r={...t.formats(),list:"checked"},s=new B().retain(n.index).insert(`
`,r).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(s,w.sources.USER),this.quill.setSelection(n.index+1,w.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(n,t){const[e,r]=this.quill.getLine(n.index),s=new B().retain(n.index).insert(`
`,t.format).retain(e.length()-r-1).retain(1,{header:null});this.quill.updateContents(s,w.sources.USER),this.quill.setSelection(n.index+1,w.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(n){const t=this.quill.getModule("table");if(t){const[e,r,s,i]=t.getTable(n),o=ad(e,r,s,i);if(o==null)return;let a=e.offset();if(o<0){const u=new B().retain(a).insert(`
`);this.quill.updateContents(u,w.sources.USER),this.quill.setSelection(n.index+1,n.length,w.sources.SILENT)}else if(o>0){a+=e.length();const u=new B().retain(a).insert(`
`);this.quill.updateContents(u,w.sources.USER),this.quill.setSelection(a,w.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(n,t){const{event:e,line:r}=t,s=r.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(s-1,w.sources.USER):this.quill.setSelection(s+r.length(),w.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(n,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[r,s]=this.quill.getLine(n.index);if(s>e)return!0;let i;switch(t.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(n.index," ",w.sources.USER),this.quill.history.cutoff();const o=new B().retain(n.index-s).delete(e+1).retain(r.length()-2-s).retain(1,{list:i});return this.quill.updateContents(o,w.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index-e,w.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(n){const[t,e]=this.quill.getLine(n.index);let r=2,s=t;for(;s!=null&&s.length()<=1&&s.formats()["code-block"];)if(s=s.prev,r-=1,r<=0){const i=new B().retain(n.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,w.sources.USER),this.quill.setSelection(n.index-1,w.sources.SILENT),!1}return!0}},"embed left":An("ArrowLeft",!1),"embed left shift":An("ArrowLeft",!0),"embed right":An("ArrowRight",!1),"embed right shift":An("ArrowRight",!0),"table down":wl(!1),"table up":wl(!0)}};Bn.DEFAULTS=ld;function Al(n){return{key:"Tab",shiftKey:!n,format:{"code-block":!0},handler(t,e){let{event:r}=e;const s=this.quill.scroll.query("code-block"),{TAB:i}=s;if(t.length===0&&!r.shiftKey){this.quill.insertText(t.index,i,w.sources.USER),this.quill.setSelection(t.index+i.length,w.sources.SILENT);return}const o=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:a,length:u}=t;o.forEach((h,p)=>{n?(h.insertAt(0,i),p===0?a+=i.length:u+=i.length):h.domNode.textContent.startsWith(i)&&(h.deleteAt(0,i.length),p===0?a-=i.length:u-=i.length)}),this.quill.update(w.sources.USER),this.quill.setSelection(a,u,w.sources.SILENT)}}}function An(n,t){return{key:n,shiftKey:t,altKey:null,[n==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(r){let{index:s}=r;n==="ArrowRight"&&(s+=r.length+1);const[i]=this.quill.getLeaf(s);return i instanceof Lt?(n==="ArrowLeft"?t?this.quill.setSelection(r.index-1,r.length+1,w.sources.USER):this.quill.setSelection(r.index-1,w.sources.USER):t?this.quill.setSelection(r.index,r.length+1,w.sources.USER):this.quill.setSelection(r.index+r.length+1,w.sources.USER),!1):!0}}}function Ds(n){return{key:n[0],shortKey:!0,handler(t,e){this.quill.format(n,!e.format[n],w.sources.USER)}}}function wl(n){return{key:n?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const r=n?"prev":"next",s=e.line,i=s.parent[r];if(i!=null){if(i.statics.blotName==="table-row"){let o=i.children.head,a=s;for(;a.prev!=null;)a=a.prev,o=o.next;const u=o.offset(this.quill.scroll)+Math.min(e.offset,o.length()-1);this.quill.setSelection(u,0,w.sources.USER)}}else{const o=s.table()[r];o!=null&&(n?this.quill.setSelection(o.offset(this.quill.scroll)+o.length()-1,0,w.sources.USER):this.quill.setSelection(o.offset(this.quill.scroll),0,w.sources.USER))}return!1}}}function od(n){if(typeof n=="string"||typeof n=="number")n={key:n};else if(typeof n=="object")n=Je(n);else return null;return n.shortKey&&(n[id]=n.shortKey,delete n.shortKey),n}function Ni(n){let{quill:t,range:e}=n;const r=t.getLines(e);let s={};if(r.length>1){const i=r[0].formats(),o=r[r.length-1].formats();s=It.AttributeMap.diff(o,i)||{}}t.deleteText(e,w.sources.USER),Object.keys(s).length>0&&t.formatLine(e.index,1,s,w.sources.USER),t.setSelection(e.index,w.sources.SILENT)}function ad(n,t,e,r){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?r===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const cd=/font-weight:\s*normal/,ud=["P","OL","UL"],Nl=n=>n&&ud.includes(n.tagName),hd=n=>{Array.from(n.querySelectorAll("br")).filter(t=>Nl(t.previousElementSibling)&&Nl(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},fd=n=>{Array.from(n.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(cd)}).forEach(t=>{var r;const e=n.createDocumentFragment();e.append(...t.childNodes),(r=t.parentNode)==null||r.replaceChild(e,t)})};function dd(n){n.querySelector('[id^="docs-internal-guid-"]')&&(fd(n),hd(n))}const gd=/\bmso-list:[^;]*ignore/i,pd=/\bmso-list:[^;]*\bl(\d+)/i,md=/\bmso-list:[^;]*\blevel(\d+)/i,bd=(n,t)=>{const e=n.getAttribute("style"),r=e==null?void 0:e.match(pd);if(!r)return null;const s=Number(r[1]),i=e==null?void 0:e.match(md),o=i?Number(i[1]):1,a=new RegExp(`@list l${s}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),u=t.match(a),h=u&&u[1]==="bullet"?"bullet":"ordered";return{id:s,indent:o,type:h,element:n}},yd=n=>{var o,a;const t=Array.from(n.querySelectorAll("[style*=mso-list]")),e=[],r=[];t.forEach(u=>{(u.getAttribute("style")||"").match(gd)?e.push(u):r.push(u)}),e.forEach(u=>{var h;return(h=u.parentNode)==null?void 0:h.removeChild(u)});const s=n.documentElement.innerHTML,i=r.map(u=>bd(u,s)).filter(u=>u);for(;i.length;){const u=[];let h=i.shift();for(;h;)u.push(h),h=i.length&&((o=i[0])==null?void 0:o.element)===h.element.nextElementSibling&&i[0].id===h.id?i.shift():null;const p=document.createElement("ul");u.forEach(m=>{const y=document.createElement("li");y.setAttribute("data-list",m.type),m.indent>1&&y.setAttribute("class",`ql-indent-${m.indent-1}`),y.innerHTML=m.element.innerHTML,p.appendChild(y)});const v=(a=u[0])==null?void 0:a.element,{parentNode:f}=v??{};v&&(f==null||f.replaceChild(p,v)),u.slice(1).forEach(m=>{let{element:y}=m;f==null||f.removeChild(y)})}};function vd(n){n.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&yd(n)}const Ed=[vd,dd],Ad=n=>{n.documentElement&&Ed.forEach(t=>{t(n)})},wd=ue("quill:clipboard"),Nd=[[Node.TEXT_NODE,Md],[Node.TEXT_NODE,xl],["br",Sd],[Node.ELEMENT_NODE,xl],[Node.ELEMENT_NODE,_d],[Node.ELEMENT_NODE,Ld],[Node.ELEMENT_NODE,kd],["li",Cd],["ol, ul",Id],["pre",qd],["tr",Rd],["b",js("bold")],["i",js("italic")],["strike",js("strike")],["style",Od]],Td=[td,Eo].reduce((n,t)=>(n[t.keyName]=t,n),{}),Tl=[yo,Ei,vi,wo,xo,_o].reduce((n,t)=>(n[t.keyName]=t,n),{});class So extends zt{constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",r=>this.onCaptureCopy(r,!1)),this.quill.root.addEventListener("cut",r=>this.onCaptureCopy(r,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Nd.concat(this.options.matchers??[]).forEach(r=>{let[s,i]=r;this.addMatcher(s,i)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:r}=t,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(s[At.blotName])return new B().insert(r||"",{[At.blotName]:s[At.blotName]});if(!e)return new B().insert(r||"",s);const i=this.convertHTML(e);return Hr(i,`
`)&&(i.ops[i.ops.length-1].attributes==null||s.table)?i.compose(new B().retain(i.length()-1).delete(1)):i}normalizeHTML(t){Ad(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const r=e.body,s=new WeakMap,[i,o]=this.prepareMatching(r,s);return Ti(this.quill.scroll,r,i,o,s)}dangerouslyPasteHTML(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:w.sources.API;if(typeof t=="string"){const s=this.convert({html:t,text:""});this.quill.setContents(s,e),this.quill.setSelection(0,w.sources.SILENT)}else{const s=this.convert({html:e,text:""});this.quill.updateContents(new B().retain(t).concat(s),r),this.quill.setSelection(t+s.length(),w.sources.SILENT)}}onCaptureCopy(t){var o,a;let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[r]=this.quill.selection.getRange();if(r==null)return;const{html:s,text:i}=this.onCopy(r,e);(o=t.clipboardData)==null||o.setData("text/plain",i),(a=t.clipboardData)==null||a.setData("text/html",s),e&&Ni({range:r,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){var o,a,u,h,p;if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const r=(o=t.clipboardData)==null?void 0:o.getData("text/html");let s=(a=t.clipboardData)==null?void 0:a.getData("text/plain");if(!r&&!s){const v=(u=t.clipboardData)==null?void 0:u.getData("text/uri-list");v&&(s=this.normalizeURIList(v))}const i=Array.from(((h=t.clipboardData)==null?void 0:h.files)||[]);if(!r&&i.length>0){this.quill.uploader.upload(e,i);return}if(r&&i.length>0){const v=new DOMParser().parseFromString(r,"text/html");if(v.body.childElementCount===1&&((p=v.body.firstElementChild)==null?void 0:p.tagName)==="IMG"){this.quill.uploader.upload(e,i);return}}this.onPaste(e,{html:r,text:s})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:r,html:s}=e;const i=this.quill.getFormat(t.index),o=this.convert({text:r,html:s},i);wd.log("onPaste",o,{text:r,html:s});const a=new B().retain(t.index).delete(t.length).concat(o);this.quill.updateContents(a,w.sources.USER),this.quill.setSelection(a.length()-t.length,w.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const r=[],s=[];return this.matchers.forEach(i=>{const[o,a]=i;switch(o){case Node.TEXT_NODE:s.push(a);break;case Node.ELEMENT_NODE:r.push(a);break;default:Array.from(t.querySelectorAll(o)).forEach(u=>{if(e.has(u)){const h=e.get(u);h==null||h.push(a)}else e.set(u,[a])});break}}),[r,s]}}I(So,"DEFAULTS",{matchers:[]});function Me(n,t,e,r){return r.query(t)?n.reduce((s,i)=>{if(!i.insert)return s;if(i.attributes&&i.attributes[t])return s.push(i);const o=e?{[t]:e}:{};return s.insert(i.insert,{...o,...i.attributes})},new B):n}function Hr(n,t){let e="";for(let r=n.ops.length-1;r>=0&&e.length<t.length;--r){const s=n.ops[r];if(typeof s.insert!="string")break;e=s.insert+e}return e.slice(-1*t.length)===t}function ge(n,t){if(!(n instanceof Element))return!1;const e=t.query(n);return e&&e.prototype instanceof Lt?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(n.tagName.toLowerCase())}function xd(n,t){return n.previousElementSibling&&n.nextElementSibling&&!ge(n.previousElementSibling,t)&&!ge(n.nextElementSibling,t)}const wn=new WeakMap;function qo(n){return n==null?!1:(wn.has(n)||(n.tagName==="PRE"?wn.set(n,!0):wn.set(n,qo(n.parentNode))),wn.get(n))}function Ti(n,t,e,r,s){return t.nodeType===t.TEXT_NODE?r.reduce((i,o)=>o(t,i,n),new B):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((i,o)=>{let a=Ti(n,o,e,r,s);return o.nodeType===t.ELEMENT_NODE&&(a=e.reduce((u,h)=>h(o,u,n),a),a=(s.get(o)||[]).reduce((u,h)=>h(o,u,n),a)),i.concat(a)},new B):new B}function js(n){return(t,e,r)=>Me(e,n,!0,r)}function Ld(n,t,e){const r=Qt.keys(n),s=Ht.keys(n),i=ve.keys(n),o={};return r.concat(s).concat(i).forEach(a=>{let u=e.query(a,D.ATTRIBUTE);u!=null&&(o[u.attrName]=u.value(n),o[u.attrName])||(u=Td[a],u!=null&&(u.attrName===a||u.keyName===a)&&(o[u.attrName]=u.value(n)||void 0),u=Tl[a],u!=null&&(u.attrName===a||u.keyName===a)&&(u=Tl[a],o[u.attrName]=u.value(n)||void 0))}),Object.entries(o).reduce((a,u)=>{let[h,p]=u;return Me(a,h,p,e)},t)}function _d(n,t,e){const r=e.query(n);if(r==null)return t;if(r.prototype instanceof Lt){const s={},i=r.value(n);if(i!=null)return s[r.blotName]=i,new B().insert(s,r.formats(n,e))}else if(r.prototype instanceof Dr&&!Hr(t,`
`)&&t.insert(`
`),"blotName"in r&&"formats"in r&&typeof r.formats=="function")return Me(t,r.blotName,r.formats(n,e),e);return t}function Sd(n,t){return Hr(t,`
`)||t.insert(`
`),t}function qd(n,t,e){const r=e.query("code-block"),s=r&&"formats"in r&&typeof r.formats=="function"?r.formats(n,e):!0;return Me(t,"code-block",s,e)}function Od(){return new B}function Cd(n,t,e){const r=e.query(n);if(r==null||r.blotName!=="list"||!Hr(t,`
`))return t;let s=-1,i=n.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(s+=1),i=i.parentNode;return s<=0?t:t.reduce((o,a)=>a.insert?a.attributes&&typeof a.attributes.indent=="number"?o.push(a):o.insert(a.insert,{indent:s,...a.attributes||{}}):o,new B)}function Id(n,t,e){const r=n;let s=r.tagName==="OL"?"ordered":"bullet";const i=r.getAttribute("data-checked");return i&&(s=i==="true"?"checked":"unchecked"),Me(t,"list",s,e)}function xl(n,t,e){if(!Hr(t,`
`)){if(ge(n,e)&&(n.childNodes.length>0||n instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&n.nextSibling){let r=n.nextSibling;for(;r!=null;){if(ge(r,e))return t.insert(`
`);const s=e.query(r);if(s&&s.prototype instanceof Ct)return t.insert(`
`);r=r.firstChild}}}return t}function kd(n,t,e){var i;const r={},s=n.style||{};return s.fontStyle==="italic"&&(r.italic=!0),s.textDecoration==="underline"&&(r.underline=!0),s.textDecoration==="line-through"&&(r.strike=!0),((i=s.fontWeight)!=null&&i.startsWith("bold")||parseInt(s.fontWeight,10)>=700)&&(r.bold=!0),t=Object.entries(r).reduce((o,a)=>{let[u,h]=a;return Me(o,u,h,e)},t),parseFloat(s.textIndent||0)>0?new B().insert("	").concat(t):t}function Rd(n,t,e){var s,i;const r=((s=n.parentElement)==null?void 0:s.tagName)==="TABLE"?n.parentElement:(i=n.parentElement)==null?void 0:i.parentElement;if(r!=null){const a=Array.from(r.querySelectorAll("tr")).indexOf(n)+1;return Me(t,"table",a,e)}return t}function Md(n,t,e){var s;let r=n.data;if(((s=n.parentElement)==null?void 0:s.tagName)==="O:P")return t.insert(r.trim());if(!qo(n)){if(r.trim().length===0&&r.includes(`
`)&&!xd(n,e))return t;r=r.replace(/[^\S\u00a0]/g," "),r=r.replace(/ {2,}/g," "),(n.previousSibling==null&&n.parentElement!=null&&ge(n.parentElement,e)||n.previousSibling instanceof Element&&ge(n.previousSibling,e))&&(r=r.replace(/^ /,"")),(n.nextSibling==null&&n.parentElement!=null&&ge(n.parentElement,e)||n.nextSibling instanceof Element&&ge(n.nextSibling,e))&&(r=r.replace(/ $/,"")),r=r.replaceAll(" "," ")}return t.insert(r)}class Oo extends zt{constructor(e,r){super(e,r);I(this,"lastRecorded",0);I(this,"ignoreChange",!1);I(this,"stack",{undo:[],redo:[]});I(this,"currentRange",null);this.quill.on(w.events.EDITOR_CHANGE,(s,i,o,a)=>{s===w.events.SELECTION_CHANGE?i&&a!==w.sources.SILENT&&(this.currentRange=i):s===w.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||a===w.sources.USER?this.record(i,o):this.transform(i)),this.currentRange=ti(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",s=>{s.inputType==="historyUndo"?(this.undo(),s.preventDefault()):s.inputType==="historyRedo"&&(this.redo(),s.preventDefault())})}change(e,r){if(this.stack[e].length===0)return;const s=this.stack[e].pop();if(!s)return;const i=this.quill.getContents(),o=s.delta.invert(i);this.stack[r].push({delta:o,range:ti(s.range,o)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(s.delta,w.sources.USER),this.ignoreChange=!1,this.restoreSelection(s)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(e,r){if(e.ops.length===0)return;this.stack.redo=[];let s=e.invert(r),i=this.currentRange;const o=Date.now();if(this.lastRecorded+this.options.delay>o&&this.stack.undo.length>0){const a=this.stack.undo.pop();a&&(s=s.compose(a.delta),i=a.range)}else this.lastRecorded=o;s.length()!==0&&(this.stack.undo.push({delta:s,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(e){Ll(this.stack.undo,e),Ll(this.stack.redo,e)}undo(){this.change("undo","redo")}restoreSelection(e){if(e.range)this.quill.setSelection(e.range,w.sources.USER);else{const r=Dd(this.quill.scroll,e.delta);this.quill.setSelection(r,w.sources.USER)}}}I(Oo,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1});function Ll(n,t){let e=t;for(let r=n.length-1;r>=0;r-=1){const s=n[r];n[r]={delta:e.transform(s.delta,!0),range:s.range&&ti(s.range,e)},e=s.delta.transform(e),n[r].delta.length()===0&&n.splice(r,1)}}function Bd(n,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(r=>n.query(r,D.BLOCK)!=null):!1}function Dd(n,t){const e=t.reduce((s,i)=>s+(i.delete||0),0);let r=t.length()-e;return Bd(n,t)&&(r-=1),r}function ti(n,t){if(!n)return n;const e=t.transformPosition(n.index),r=t.transformPosition(n.index+n.length);return{index:e,length:r-e}}class Co extends zt{constructor(t,e){super(t,e),t.root.addEventListener("drop",r=>{var o;r.preventDefault();let s=null;if(document.caretRangeFromPoint)s=document.caretRangeFromPoint(r.clientX,r.clientY);else if(document.caretPositionFromPoint){const a=document.caretPositionFromPoint(r.clientX,r.clientY);s=document.createRange(),s.setStart(a.offsetNode,a.offset),s.setEnd(a.offsetNode,a.offset)}const i=s&&t.selection.normalizeNative(s);if(i){const a=t.selection.normalizedToRange(i);(o=r.dataTransfer)!=null&&o.files&&this.upload(a,r.dataTransfer.files)}})}upload(t,e){const r=[];Array.from(e).forEach(s=>{var i;s&&((i=this.options.mimetypes)!=null&&i.includes(s.type))&&r.push(s)}),r.length>0&&this.options.handler.call(this,t,r)}}Co.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(n,t){if(!this.quill.scroll.query("image"))return;const e=t.map(r=>new Promise(s=>{const i=new FileReader;i.onload=()=>{s(i.result)},i.readAsDataURL(r)}));Promise.all(e).then(r=>{const s=r.reduce((i,o)=>i.insert({image:o}),new B().retain(n.index).delete(n.length));this.quill.updateContents(s,R.sources.USER),this.quill.setSelection(n.index+r.length,R.sources.SILENT)})}};const jd=["insertText","insertReplacementText"];class $d extends zt{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",r=>{this.handleBeforeInput(r)}),/Android/i.test(navigator.userAgent)||t.on(w.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){Ni({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const r=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new B().retain(t.index).insert(e,r),w.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,w.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!jd.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const r=Pd(t);if(r==null)return;const s=this.quill.selection.normalizeNative(e),i=s?this.quill.selection.normalizedToRange(s):null;i&&this.replaceText(i,r)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function Pd(n){var t;return typeof n.data=="string"?n.data:(t=n.dataTransfer)!=null&&t.types.includes("text/plain")?n.dataTransfer.getData("text/plain"):null}const Ud=/Mac/i.test(navigator.platform),Hd=100,Fd=n=>!!(n.key==="ArrowLeft"||n.key==="ArrowRight"||n.key==="ArrowUp"||n.key==="ArrowDown"||n.key==="Home"||Ud&&n.key==="a"&&n.ctrlKey===!0);class zd extends zt{constructor(e,r){super(e,r);I(this,"isListening",!1);I(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,r){let{line:s,event:i}=r;if(!(s instanceof Pt)||!s.uiNode)return!0;const o=getComputedStyle(s.domNode).direction==="rtl";return o&&i.key!=="ArrowRight"||!o&&i.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(i.shiftKey?1:0),w.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&Fd(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+Hd,this.isListening)return;this.isListening=!0;const e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){const e=document.getSelection();if(!e)return;const r=e.getRangeAt(0);if(r.collapsed!==!0||r.startOffset!==0)return;const s=this.quill.scroll.find(r.startContainer);if(!(s instanceof Pt)||!s.uiNode)return;const i=document.createRange();i.setStartAfter(s.uiNode),i.setEndAfter(s.uiNode),e.removeAllRanges(),e.addRange(i)}}w.register({"blots/block":pt,"blots/block/embed":Ct,"blots/break":Ft,"blots/container":ke,"blots/cursor":sr,"blots/embed":bi,"blots/inline":Jt,"blots/scroll":Qe,"blots/text":Ut,"modules/clipboard":So,"modules/history":Oo,"modules/keyboard":Bn,"modules/uploader":Co,"modules/input":$d,"modules/uiNode":zd});class Kd extends Ht{add(t,e){let r=0;if(e==="+1"||e==="-1"){const s=this.value(t)||0;r=e==="+1"?s+1:s-1}else typeof e=="number"&&(r=e);return r===0?(this.remove(t),!0):super.add(t,r.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const Vd=new Kd("indent","ql-indent",{scope:D.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class ei extends pt{}I(ei,"blotName","blockquote"),I(ei,"tagName","blockquote");class ri extends pt{static formats(t){return this.tagName.indexOf(t.tagName)+1}}I(ri,"blotName","header"),I(ri,"tagName",["H1","H2","H3","H4","H5","H6"]);class Fr extends ke{}Fr.blotName="list-container";Fr.tagName="OL";class zr extends pt{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){w.register(Fr)}constructor(t,e){super(t,e);const r=e.ownerDocument.createElement("span"),s=i=>{if(!t.isEnabled())return;const o=this.statics.formats(e,t);o==="checked"?(this.format("list","unchecked"),i.preventDefault()):o==="unchecked"&&(this.format("list","checked"),i.preventDefault())};r.addEventListener("mousedown",s),r.addEventListener("touchstart",s),this.attachUI(r)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}zr.blotName="list";zr.tagName="LI";Fr.allowedChildren=[zr];zr.requiredContainer=Fr;class $r extends Jt{static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}I($r,"blotName","bold"),I($r,"tagName",["STRONG","B"]);class ni extends $r{}I(ni,"blotName","italic"),I(ni,"tagName",["EM","I"]);class pe extends Jt{static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return Io(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}I(pe,"blotName","link"),I(pe,"tagName","A"),I(pe,"SANITIZED_URL","about:blank"),I(pe,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]);function Io(n,t){const e=document.createElement("a");e.href=n;const r=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(r)>-1}class si extends Jt{static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}I(si,"blotName","script"),I(si,"tagName",["SUB","SUP"]);class ii extends $r{}I(ii,"blotName","strike"),I(ii,"tagName",["S","STRIKE"]);class li extends Jt{}I(li,"blotName","underline"),I(li,"tagName","U");class Tn extends bi{static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}I(Tn,"blotName","formula"),I(Tn,"className","ql-formula"),I(Tn,"tagName","SPAN");const _l=["alt","height","width"];class oi extends Lt{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return _l.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return Io(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){_l.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}I(oi,"blotName","image"),I(oi,"tagName","IMG");const Sl=["height","width"];class xn extends Ct{static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Sl.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static sanitize(t){return pe.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Sl.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}I(xn,"blotName","video"),I(xn,"className","ql-video"),I(xn,"tagName","IFRAME");const qr=new Ht("code-token","hljs",{scope:D.INLINE});class oe extends Jt{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(At.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,r){super(t,e,r),qr.add(this.domNode,r)}format(t,e){t!==oe.blotName?super.format(t,e):e?qr.add(this.domNode,e):(qr.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),qr.value(this.domNode)||this.unwrap()}}oe.blotName="code-token";oe.className="ql-token";class Ot extends At{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),oe.blotName,!1),super.replaceWith(t,e)}}class Cr extends Re{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===Ot.blotName&&(this.forceNext=!0,this.children.forEach(r=>{r.format(t,e)}))}formatAt(t,e,r,s){r===Ot.blotName&&(this.forceNext=!0),super.formatAt(t,e,r,s)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const s=`${Array.from(this.domNode.childNodes).filter(o=>o!==this.uiNode).map(o=>o.textContent).join(`
`)}
`,i=Ot.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==s){if(s.trim().length>0||this.cachedText==null){const o=this.children.reduce((u,h)=>u.concat(po(h,!1)),new B),a=t(s,i);o.diff(a).reduce((u,h)=>{let{retain:p,attributes:v}=h;return p?(v&&Object.keys(v).forEach(f=>{[Ot.blotName,oe.blotName].includes(f)&&this.formatAt(u,p,f,v[f])}),u+p):u},0)}this.cachedText=s,this.forceNext=!1}}html(t,e){const[r]=this.children.find(t);return`<pre data-language="${r?Ot.formats(r.domNode):"plain"}">
${Mn(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=Ot.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}Cr.allowedChildren=[Ot];Ot.requiredContainer=Cr;Ot.allowedChildren=[oe,sr,Ut,Ft];const Gd=(n,t,e)=>{if(typeof n.versionString=="string"){const r=n.versionString.split(".")[0];if(parseInt(r,10)>=11)return n.highlight(e,{language:t}).value}return n.highlight(t,e).value};class ko extends zt{static register(){w.register(oe,!0),w.register(Ot,!0),w.register(Cr,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((r,s)=>{let{key:i}=s;return r[i]=!0,r},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(w.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof Cr))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(r=>{let{key:s,label:i}=r;const o=e.ownerDocument.createElement("option");o.textContent=i,o.setAttribute("value",s),e.appendChild(o)}),e.addEventListener("change",()=>{t.format(Ot.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=Ot.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(w.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(w.sources.USER);const r=this.quill.getSelection();(t==null?this.quill.scroll.descendants(Cr):[t]).forEach(i=>{i.highlight(this.highlightBlot,e)}),this.quill.update(w.sources.SILENT),r!=null&&this.quill.setSelection(r,w.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return Mn(t).split(`
`).reduce((s,i,o)=>(o!==0&&s.insert(`
`,{[At.blotName]:e}),s.insert(i)),new B);const r=this.quill.root.ownerDocument.createElement("div");return r.classList.add(At.className),r.innerHTML=Gd(this.options.hljs,e,t),Ti(this.quill.scroll,r,[(s,i)=>{const o=qr.value(s);return o?i.compose(new B().retain(i.length(),{[oe.blotName]:o})):i}],[(s,i)=>s.data.split(`
`).reduce((o,a,u)=>(u!==0&&o.insert(`
`,{[At.blotName]:e}),o.insert(a)),i)],new WeakMap)}}ko.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};const kr=class kr extends pt{static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",xi()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===kr.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};I(kr,"blotName","table"),I(kr,"tagName","TD");let $t=kr;class ae extends ke{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),r=this.next.children.head.formats(),s=this.next.children.tail.formats();return t.table===e.table&&t.table===r.table&&t.table===s.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const r=e.formats(),s=e.next.formats();if(r.table!==s.table){const i=this.splitAfter(e);i&&i.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}I(ae,"blotName","table-row"),I(ae,"tagName","TR");class Yt extends ke{}I(Yt,"blotName","table-body"),I(Yt,"tagName","TBODY");class lr extends ke{balanceCells(){const t=this.descendants(ae),e=t.reduce((r,s)=>Math.max(s.children.length,r),0);t.forEach(r=>{new Array(e-r.children.length).fill(0).forEach(()=>{let s;r.children.head!=null&&(s=$t.formats(r.children.head.domNode));const i=this.scroll.create($t.blotName,s);r.appendChild(i),i.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(Yt);e==null||e.children.head==null||e.children.forEach(r=>{const s=r.children.at(t);s!=null&&s.remove()})}insertColumn(t){const[e]=this.descendant(Yt);e==null||e.children.head==null||e.children.forEach(r=>{const s=r.children.at(t),i=$t.formats(r.children.head.domNode),o=this.scroll.create($t.blotName,i);r.insertBefore(o,s)})}insertRow(t){const[e]=this.descendant(Yt);if(e==null||e.children.head==null)return;const r=xi(),s=this.scroll.create(ae.blotName);e.children.head.children.forEach(()=>{const o=this.scroll.create($t.blotName,r);s.appendChild(o)});const i=e.children.at(t);e.insertBefore(s,i)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}I(lr,"blotName","table-container"),I(lr,"tagName","TABLE");lr.allowedChildren=[Yt];Yt.requiredContainer=lr;Yt.allowedChildren=[ae];ae.requiredContainer=Yt;ae.allowedChildren=[$t];$t.requiredContainer=ae;function xi(){return`row-${Math.random().toString(36).slice(2,6)}`}class Zd extends zt{static register(){w.register($t),w.register(ae),w.register(Yt),w.register(lr)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(lr).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(w.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(w.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(w.sources.USER),this.quill.setSelection(e,w.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,r]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==$t.blotName)return[null,null,null,-1];const s=e.parent;return[s.parent.parent,s,e,r]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[r,s,i]=this.getTable(e);if(i==null)return;const o=i.cellOffset();r.insertColumn(o+t),this.quill.update(w.sources.USER);let a=s.rowOffset();t===0&&(a+=1),this.quill.setSelection(e.index+a,e.length,w.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[r,s,i]=this.getTable(e);if(i==null)return;const o=s.rowOffset();r.insertRow(o+t),this.quill.update(w.sources.USER),t>0?this.quill.setSelection(e,w.sources.SILENT):this.quill.setSelection(e.index+s.children.length,e.length,w.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const r=this.quill.getSelection();if(r==null)return;const s=new Array(t).fill(0).reduce(i=>{const o=new Array(e).fill(`
`).join("");return i.insert(o,{table:xi()})},new B().retain(r.index));this.quill.updateContents(s,w.sources.USER),this.quill.setSelection(r.index,w.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(w.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(w.events.TEXT_CHANGE,(r,s,i)=>{i===w.sources.USER&&this.balanceTables()}),!0):!1)})}}const ql=ue("quill:toolbar");class Li extends zt{constructor(t,e){var r,s;if(super(t,e),Array.isArray(this.options.container)){const i=document.createElement("div");i.setAttribute("role","toolbar"),Wd(i,this.options.container),(s=(r=t.container)==null?void 0:r.parentNode)==null||s.insertBefore(i,t.container),this.container=i}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){ql.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(i=>{var a;const o=(a=this.options.handlers)==null?void 0:a[i];o&&this.addHandler(i,o)}),Array.from(this.container.querySelectorAll("button, select")).forEach(i=>{this.attach(i)}),this.quill.on(w.events.EDITOR_CHANGE,()=>{const[i]=this.quill.selection.getRange();this.update(i)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(s=>s.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){ql.warn("ignoring attaching to nonexistent format",e,t);return}const r=t.tagName==="SELECT"?"change":"click";t.addEventListener(r,s=>{let i;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const a=t.options[t.selectedIndex];a.hasAttribute("selected")?i=!1:i=a.value||!1}else t.classList.contains("ql-active")?i=!1:i=t.value||!t.hasAttribute("value"),s.preventDefault();this.quill.focus();const[o]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,i);else if(this.quill.scroll.query(e).prototype instanceof Lt){if(i=prompt(`Enter ${e}`),!i)return;this.quill.updateContents(new B().retain(o.index).delete(o.length).insert({[e]:i}),w.sources.USER)}else this.quill.format(e,i,w.sources.USER);this.update(o)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(r=>{const[s,i]=r;if(i.tagName==="SELECT"){let o=null;if(t==null)o=null;else if(e[s]==null)o=i.querySelector("option[selected]");else if(!Array.isArray(e[s])){let a=e[s];typeof a=="string"&&(a=a.replace(/"/g,'\\"')),o=i.querySelector(`option[value="${a}"]`)}o==null?(i.value="",i.selectedIndex=-1):o.selected=!0}else if(t==null)i.classList.remove("ql-active"),i.setAttribute("aria-pressed","false");else if(i.hasAttribute("value")){const o=e[s],a=o===i.getAttribute("value")||o!=null&&o.toString()===i.getAttribute("value")||o==null&&!i.getAttribute("value");i.classList.toggle("ql-active",a),i.setAttribute("aria-pressed",a.toString())}else{const o=e[s]!=null;i.classList.toggle("ql-active",o),i.setAttribute("aria-pressed",o.toString())}})}}Li.DEFAULTS={};function Ol(n,t,e){const r=document.createElement("button");r.setAttribute("type","button"),r.classList.add(`ql-${t}`),r.setAttribute("aria-pressed","false"),e!=null?(r.value=e,r.setAttribute("aria-label",`${t}: ${e}`)):r.setAttribute("aria-label",t),n.appendChild(r)}function Wd(n,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const r=document.createElement("span");r.classList.add("ql-formats"),e.forEach(s=>{if(typeof s=="string")Ol(r,s);else{const i=Object.keys(s)[0],o=s[i];Array.isArray(o)?Xd(r,i,o):Ol(r,i,o)}}),n.appendChild(r)})}function Xd(n,t,e){const r=document.createElement("select");r.classList.add(`ql-${t}`),e.forEach(s=>{const i=document.createElement("option");s!==!1?i.setAttribute("value",String(s)):i.setAttribute("selected","selected"),r.appendChild(i)}),n.appendChild(r)}Li.DEFAULTS={container:null,handlers:{clean(){const n=this.quill.getSelection();if(n!=null)if(n.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,D.INLINE)!=null&&this.quill.format(e,!1,w.sources.USER)})}else this.quill.removeFormat(n.index,n.length,w.sources.USER)},direction(n){const{align:t}=this.quill.getFormat();n==="rtl"&&t==null?this.quill.format("align","right",w.sources.USER):!n&&t==="right"&&this.quill.format("align",!1,w.sources.USER),this.quill.format("direction",n,w.sources.USER)},indent(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t),r=parseInt(e.indent||0,10);if(n==="+1"||n==="-1"){let s=n==="+1"?1:-1;e.direction==="rtl"&&(s*=-1),this.quill.format("indent",r+s,w.sources.USER)}},link(n){n===!0&&(n=prompt("Enter link URL:")),this.quill.format("link",n,w.sources.USER)},list(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t);n==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,w.sources.USER):this.quill.format("list","unchecked",w.sources.USER):this.quill.format("list",n,w.sources.USER)}}};const Yd='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',Qd='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',Jd='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',tg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',eg='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',rg='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',ng='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',sg='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',Cl='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',ig='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',lg='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',og='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',ag='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',cg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',ug='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',hg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',fg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',dg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',gg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',pg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',mg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',bg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',yg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',vg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',Eg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',Ag='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',wg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',Ng='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Tg='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',xg='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',Lg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',_g='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Sg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',Pr={align:{"":Yd,center:Qd,right:Jd,justify:tg},background:eg,blockquote:rg,bold:ng,clean:sg,code:Cl,"code-block":Cl,color:ig,direction:{"":lg,rtl:og},formula:ag,header:{1:cg,2:ug,3:hg,4:fg,5:dg,6:gg},italic:pg,image:mg,indent:{"+1":bg,"-1":yg},link:vg,list:{bullet:Eg,check:Ag,ordered:wg},script:{sub:Ng,super:Tg},strike:xg,table:Lg,underline:_g,video:Sg},qg='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let Il=0;function kl(n,t){n.setAttribute(t,`${n.getAttribute(t)!=="true"}`)}class Dn{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),kl(this.label,"aria-expanded"),kl(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const r=t.getAttribute("value");return r&&e.setAttribute("data-value",r),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",s=>{switch(s.key){case"Enter":this.selectItem(e,!0),s.preventDefault();break;case"Escape":this.escape(),s.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=qg,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${Il}`,Il+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const r=this.buildItem(e);t.appendChild(r),e.selected===!0&&this.selectItem(r)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const r=this.container.querySelector(".ql-selected");t!==r&&(r!=null&&r.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const r=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(r)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class Ro extends Dn{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(r=>{r.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const r=this.label.querySelector(".ql-color-label"),s=t&&t.getAttribute("data-value")||"";r&&(r.tagName==="line"?r.style.stroke=s:r.style.fill=s)}}class Mo extends Dn{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(r=>{r.innerHTML=e[r.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const r=t||this.defaultItem;if(r!=null){if(this.label.innerHTML===r.innerHTML)return;this.label.innerHTML=r.innerHTML}}}const Og=n=>{const{overflowY:t}=getComputedStyle(n,null);return t!=="visible"&&t!=="clip"};class Bo{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,Og(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,r=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${r}px`,this.root.classList.remove("ql-flip");const s=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let o=0;if(i.right>s.right&&(o=s.right-i.right,this.root.style.left=`${e+o}px`),i.left<s.left&&(o=s.left-i.left,this.root.style.left=`${e+o}px`),i.bottom>s.bottom){const a=i.bottom-i.top,u=t.bottom-t.top+a;this.root.style.top=`${r-u}px`,this.root.classList.add("ql-flip")}return o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const Cg=[!1,"center","right","justify"],Ig=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],kg=[!1,"serif","monospace"],Rg=["1","2","3",!1],Mg=["small",!1,"large","huge"];class Kr extends ir{constructor(t,e){super(t,e);const r=s=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",r);return}this.tooltip!=null&&!this.tooltip.root.contains(s.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(s.target)||i.close()})};t.emitter.listenDOM("click",document.body,r)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(r=>{(r.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),e[i]!=null))if(i==="direction")r.innerHTML=e[i][""]+e[i].rtl;else if(typeof e[i]=="string")r.innerHTML=e[i];else{const o=r.value||"";o!=null&&e[i][o]&&(r.innerHTML=e[i][o])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(s=>{if(s.classList.contains("ql-align")&&(s.querySelector("option")==null&&Lr(s,Cg),typeof e.align=="object"))return new Mo(s,e.align);if(s.classList.contains("ql-background")||s.classList.contains("ql-color")){const i=s.classList.contains("ql-background")?"background":"color";return s.querySelector("option")==null&&Lr(s,Ig,i==="background"?"#ffffff":"#000000"),new Ro(s,e[i])}return s.querySelector("option")==null&&(s.classList.contains("ql-font")?Lr(s,kg):s.classList.contains("ql-header")?Lr(s,Rg):s.classList.contains("ql-size")&&Lr(s,Mg)),new Dn(s)});const r=()=>{this.pickers.forEach(s=>{s.update()})};this.quill.on(R.events.EDITOR_CHANGE,r)}}Kr.DEFAULTS=me({},ir.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let n=this.container.querySelector("input.ql-image[type=file]");n==null&&(n=document.createElement("input"),n.setAttribute("type","file"),n.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),n.classList.add("ql-image"),n.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,n.files),n.value=""}),this.container.appendChild(n)),n.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class Do extends Bo{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const r=this.quill.getBounds(this.quill.selection.savedRange);r!=null&&this.position(r),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,R.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,R.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=Bg(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const r=e.index+e.length;this.quill.insertEmbed(r,this.root.getAttribute("data-mode"),t,R.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(r+1," ",R.sources.USER),this.quill.setSelection(r+2,R.sources.USER)}break}}this.textbox.value="",this.hide()}}function Bg(n){let t=n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=n.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:n}function Lr(n,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(r=>{const s=document.createElement("option");r===e?s.setAttribute("selected","selected"):s.setAttribute("value",String(r)),n.appendChild(s)})}const Dg=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class jo extends Do{constructor(t,e){super(t,e),this.quill.on(R.events.EDITOR_CHANGE,(r,s,i,o)=>{if(r===R.events.SELECTION_CHANGE)if(s!=null&&s.length>0&&o===R.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const a=this.quill.getLines(s.index,s.length);if(a.length===1){const u=this.quill.getBounds(s);u!=null&&this.position(u)}else{const u=a[a.length-1],h=this.quill.getIndex(u),p=Math.min(u.length()-1,s.index+s.length-h),v=this.quill.getBounds(new qe(h,p));v!=null&&this.position(v)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(R.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),r=this.root.querySelector(".ql-tooltip-arrow");return r.style.marginLeft="",e!==0&&(r.style.marginLeft=`${-1*e-r.offsetWidth/2}px`),e}}I(jo,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));class $o extends Kr{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Dg),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new jo(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),Pr),this.buildPickers(t.container.querySelectorAll("select"),Pr))}}$o.DEFAULTS=me({},Kr.DEFAULTS,{modules:{toolbar:{handlers:{link(n){n?this.quill.theme.tooltip.edit():this.quill.format("link",!1,w.sources.USER)}}}}});const jg=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class Po extends Do{constructor(){super(...arguments);I(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){const r=this.linkRange;this.restoreFocus(),this.quill.formatText(r,"link",!1,R.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(R.events.SELECTION_CHANGE,(e,r,s)=>{if(e!=null){if(e.length===0&&s===R.sources.USER){const[i,o]=this.quill.scroll.descendant(pe,e.index);if(i!=null){this.linkRange=new qe(e.index-o,i.length());const a=pe.formats(i.domNode);this.preview.textContent=a,this.preview.setAttribute("href",a),this.show();const u=this.quill.getBounds(this.linkRange);u!=null&&this.position(u);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}I(Po,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));class Uo extends Kr{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=jg),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),Pr),this.buildPickers(t.container.querySelectorAll("select"),Pr),this.tooltip=new Po(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,r)=>{t.handlers.link.call(t,!r.format.link)}))}}Uo.DEFAULTS=me({},Kr.DEFAULTS,{modules:{toolbar:{handlers:{link(n){if(n){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:r}=this.quill.theme;r.edit("link",e)}else this.quill.format("link",!1,w.sources.USER)}}}}});w.register({"attributors/attribute/direction":Eo,"attributors/class/align":bo,"attributors/class/background":rd,"attributors/class/color":ed,"attributors/class/direction":Ao,"attributors/class/font":To,"attributors/class/size":Lo,"attributors/style/align":yo,"attributors/style/background":Ei,"attributors/style/color":vi,"attributors/style/direction":wo,"attributors/style/font":xo,"attributors/style/size":_o},!0);w.register({"formats/align":bo,"formats/direction":Ao,"formats/indent":Vd,"formats/background":Ei,"formats/color":vi,"formats/font":To,"formats/size":Lo,"formats/blockquote":ei,"formats/code-block":At,"formats/header":ri,"formats/list":zr,"formats/bold":$r,"formats/code":Ai,"formats/italic":ni,"formats/link":pe,"formats/script":si,"formats/strike":ii,"formats/underline":li,"formats/formula":Tn,"formats/image":oi,"formats/video":xn,"modules/syntax":ko,"modules/table":Zd,"modules/toolbar":Li,"themes/bubble":$o,"themes/snow":Uo,"ui/icons":Pr,"ui/picker":Dn,"ui/icon-picker":Mo,"ui/color-picker":Ro,"ui/tooltip":Bo},!0);const $g={class:"quill-editor-container"},Pg={key:0,class:"html-editor-container"},Ug={__name:"QuillEditor",props:{content:{type:String,default:""},placeholder:{type:String,default:"请输入内容..."},readOnly:{type:Boolean,default:!1}},emits:["update:content","editor-change","add-pronunciation"],setup(n,{expose:t,emit:e}){const r=n,s=e,i=fn(null),o=fn(null);let a=null;const u=fn(!1),h=fn("");function p(){u.value=!u.value,u.value&&(h.value=a.root.innerHTML)}function v(){}function f(){a&&(a.root.innerHTML=h.value,s("update:content",a.root.innerHTML),s("editor-change",{html:a.root.innerHTML,text:a.getText(),quill:a}))}zo(async()=>{const E=w.import("ui/icons");E.pronunciation=`<svg viewBox="0 0 18 18">
    <path fill="currentColor" d="M9 11.9l4-4H7.5c-.3 0-.5.2-.5.4v1.1c0 .*******.5H9v2zm1-8.9c-3.9 0-7 3.1-7 7s3.1 7 7 7 7-3.1 7-7-3.1-7-7-7z"/>
  </svg>`,E.html=`<svg viewBox="0 0 18 18">
    <path fill="currentColor" d="M5,3C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H14V17H7V15Z"/>
  </svg>`,E.table=`<svg viewBox="0 0 18 18">
    <rect class="ql-stroke" height="12" width="12" x="3" y="3"></rect>
    <line class="ql-stroke" x1="3" x2="15" y1="7" y2="7"></line>
    <line class="ql-stroke" x1="3" x2="15" y1="11" y2="11"></line>
    <line class="ql-stroke" x1="7" x2="7" y1="3" y2="15"></line>
    <line class="ql-stroke" x1="11" x2="11" y1="3" y2="15"></line>
  </svg>`;const N=w.import("blots/inline");class T extends N{static create(C){let H=super.create();return H.setAttribute("data-pronunciation",C),H.classList.add("has-pronunciation"),H}static formats(C){return C.getAttribute("data-pronunciation")}}T.blotName="pronunciation",T.tagName="span",w.register(T),a=new w(i.value,{theme:"snow",modules:{toolbar:{container:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image"],["pronunciation"],["html"],["table"]],handlers:{pronunciation:function(){const _=a.getSelection();if(_&&_.length>0){const C=a.getText(_.index,_.length);s("add-pronunciation",{text:C,range:_,quill:a})}else console.warn("请先选择要添加音标的文本")},image:function(){const _=document.createElement("input");_.setAttribute("type","file"),_.setAttribute("accept","image/*"),_.click(),_.onchange=async()=>{var H;const C=(H=_.files)==null?void 0:H[0];if(C)try{const P=await y(C),et=a.getSelection(!0);a.insertEmbed(et.index,"image",P),a.setSelection(et.index+1)}catch(P){console.error("图片上传失败:",P),alert("图片上传失败: "+(P instanceof Error?P.message:"未知错误"))}}},html:function(){p()},table:function(){m()}}}},placeholder:r.placeholder,readOnly:r.readOnly}),r.content&&(a.root.innerHTML=r.content),a.on("text-change",()=>{s("update:content",a.root.innerHTML),s("editor-change",{html:a.root.innerHTML,text:a.getText(),quill:a})})});function m(){if(!a)return;const E=a.getSelection(!0);a.clipboard.dangerouslyPasteHTML(E.index,`
    <table style="width: 100%; border-collapse: collapse; margin-bottom: 10px;">
      <thead>
        <tr>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f3f4f6;">标题1</th>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f3f4f6;">标题2</th>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f3f4f6;">标题3</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="border: 1px solid #ccc; padding: 8px;">内容1</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容2</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容3</td>
        </tr>
        <tr>
          <td style="border: 1px solid #ccc; padding: 8px;">内容4</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容5</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容6</td>
        </tr>
        <tr>
          <td style="border: 1px solid #ccc; padding: 8px;">内容7</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容8</td>
          <td style="border: 1px solid #ccc; padding: 8px;">内容9</td>
        </tr>
      </tbody>
    </table>
  `),a.setSelection(E.index+1)}async function y(E){const N=new FormData;N.append("file",E);try{const T=await fetch("/api/admin/upload",{method:"POST",body:N,headers:{Authorization:localStorage.getItem("adminToken")||""}});if(!T.ok){const C=await T.json();throw new Error(C.message||"图片上传失败")}return(await T.json()).url}catch(T){throw console.error("图片上传失败:",T),T}}return Si(()=>r.content,E=>{a&&E!==a.root.innerHTML&&(a.root.innerHTML=E)}),Si(()=>r.readOnly,E=>{a&&a.enable(!E)}),Ko(()=>{a&&(a=null)}),t({getQuill:()=>a,getHTML:()=>a?a.root.innerHTML:"",getText:()=>a?a.getText():"",setContent:E=>{a&&(a.root.innerHTML=E)}}),(E,N)=>(Oi(),qi("div",$g,[Tr("div",{ref_key:"quillEditor",ref:i,class:"quill-editor"},null,512),u.value?(Oi(),qi("div",Pg,[Go(Tr("textarea",{ref_key:"htmlTextarea",ref:o,class:"html-textarea","onUpdate:modelValue":N[0]||(N[0]=T=>h.value=T),onInput:v},null,544),[[Zo,h.value]]),Tr("div",{class:"html-editor-buttons"},[Tr("button",{class:"html-editor-button",onClick:f},"应用HTML更改"),Tr("button",{class:"html-editor-button",onClick:p},"关闭HTML编辑器")])])):Vo("",!0)]))}},Xg=Wo(Ug,[["__scopeId","data-v-8a8c5be6"]]);export{Xg as Q};
