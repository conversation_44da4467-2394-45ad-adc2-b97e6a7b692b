import{e as ee,p as te,B as oe,f as ne,r as f,C as se,z as ie,g as re,c as m,o as g,a as n,h as $,b as le,D as I,t as h,F as j,j as ae,i as ce,n as de,l as ue,d as O}from"./qi-a-hqW.js";import{a as pe}from"./CxzfvvNm.js";import{A as me}from"./oUsj6fiR.js";import{s as ge}from"./x_rD_Ya3.js";import{_ as fe}from"./DlAUqK2U.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";const he={class:"flex"},we={class:"flex-1 p-6"},ve={class:"flex justify-between items-center mb-6"},ye={class:"flex space-x-3"},xe={key:0,class:"text-center py-10"},be={key:1,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"},Se={key:2},ke={class:"bg-white shadow-md rounded p-6 mb-6"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ae={class:"mt-1 text-lg"},_e={class:"mt-1"},Me={class:"mt-1 flex flex-wrap gap-2"},Te={key:0,class:"text-gray-500"},$e={class:"mt-1"},Ee={class:"bg-white shadow-md rounded p-6 mb-6"},ze={class:"flex items-center space-x-4 mb-4"},Ie={key:0,class:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800"},Ve={class:"bg-white shadow-md rounded p-6"},Le=["innerHTML"],Ue={key:0,class:"text-gray-500 mt-2"},Ne={key:0,class:"fixed inset-0 flex items-center justify-center z-50"},Pe={class:"bg-white rounded-lg p-6 shadow-xl z-10 max-w-md w-full"},De={class:"mb-6"},Be={class:"text-center mb-4"},je={class:"text-2xl font-bold"},Oe={class:"text-gray-600 mt-1"},Re=ee({__name:"[id]",setup(We){const C=te(),R=oe(),d=ne(),E=R.params.id,z=f(!0),w=f(""),x=f(!1),y=f("1"),V=f(!1),v=f(!1),L=f(!1),b=f(""),S=f("");let W=!1;const F=()=>{if(!window.speechSynthesis)return console.error("浏览器不支持语音合成功能"),!1;try{const t=navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1&&navigator.userAgent.indexOf("Edg")===-1;v.value=t,console.log("当前浏览器是Chrome:",v.value);const e=()=>{const o=window.speechSynthesis.getVoices();if(o.length>0){console.log("已加载语音列表，共",o.length,"个语音");const i=o.filter(s=>s.name.includes("Microsoft"));if(i.length>0){console.log("找到Microsoft语音包:",i.map(c=>c.name).join(", "));const s=i.filter(c=>c.lang.includes("zh"));s.length>0&&console.log("找到中文Microsoft语音包:",s.map(c=>c.name).join(", "));const r=i.filter(c=>c.lang.includes("en"));r.length>0&&console.log("找到英文Microsoft语音包:",r.map(c=>c.name).join(", "))}else console.warn("未找到Microsoft语音包，可能需要安装Windows语音包"),setTimeout(()=>{d.warning("未检测到Microsoft语音包，请安装Windows语音包以获得更好的语音效果",{timeout:8e3,closeButton:!0}),localStorage.getItem("voicePackTipDismissed")||D()},2e3);return V.value=!0,!0}return!1};if(!e())if(t&&"onvoiceschanged"in window.speechSynthesis)window.speechSynthesis.onvoiceschanged=()=>{e()};else{let o=0;const i=10,s=()=>{o++,!e()&&(o<i?setTimeout(s,200):(console.warn("无法加载语音列表，将使用默认语音"),V.value=!0))};s()}if(!t){const o=new SpeechSynthesisUtterance("");o.volume=0,o.onend=()=>{},o.onerror=i=>{console.error("语音合成引擎初始化失败:",i)},window.speechSynthesis.speak(o)}return!0}catch(t){return console.error("初始化语音合成引擎失败:",t),!1}},l=se({id:"",title:"",type:"",body:"",categories:[],annotations:null,createdAt:"",updatedAt:""}),H=ie(()=>{if(!l.body)return"";if(l.annotations&&Array.isArray(l.annotations)&&l.annotations.length>0){const t=document.createElement("div");t.innerHTML=l.body;let e=l.body;return[...l.annotations].sort((i,s)=>s.word.length-i.word.length).forEach(i=>{try{const{word:s,pronunciation:r}=i;if(!r)return;const c=s.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),a=r.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),p=`<span class="pronunciation-word" data-pronunciation="${a}" data-word="${c}">${c} 
            <button class="pronunciation-btn" data-word="${c}" data-pronunciation="${a}" title="点击播放发音">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.414l-2.829-2.828a4 4 0 015.657-5.657l2.828 2.829m7.072 7.072l2.828 2.829a4 4 0 11-5.657 5.657l-2.828-2.829" />
              </svg>
            </button>
          </span>`,u=new RegExp(`\\b${s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"g");e=e.replace(u,p)}catch(s){console.error(`处理单词 ${i.word} 时出错:`,s)}}),e}return l.body}),U=t=>{if(t)try{if(console.log("Chrome浏览器发音单词:",t),!("speechSynthesis"in window)){console.error("浏览器不支持语音合成API"),d.error("您的浏览器不支持语音合成功能");return}return _(),window.speechSynthesis.cancel(),q(t),!0}catch(e){return console.error("Chrome发音失败:",e),d.error(`发音失败: ${e instanceof Error?e.message:"未知错误"}`),G(t),!1}},q=t=>{const e=new SpeechSynthesisUtterance(t);e.lang=/[\u4e00-\u9fa5]/.test(t)?"zh-CN":"en-US",e.rate=parseFloat(y.value),e.volume=1,e.pitch=1;const o=window.speechSynthesis.getVoices();if(/[\u4e00-\u9fa5]/.test(t)){const i=o.find(s=>s.name.includes("Microsoft")&&s.lang.includes("zh"))||o.find(s=>s.lang.includes("zh"));i&&(e.voice=i,console.log("使用中文语音:",i.name))}else{const i=o.find(s=>s.name.includes("Microsoft")&&s.lang.includes("en"))||o.find(s=>s.lang.includes("en-US"))||o.find(s=>s.lang.includes("en"));i&&(e.voice=i,console.log("使用英文语音:",i.name))}A(e,{chunkLength:120},()=>{console.log("语音播放完成")})},A=(t,e={},o)=>{window._currentUtterance=t;const i=e.offset!==void 0?t.text.substring(e.offset):t.text;if(!i||i.trim()===""){o&&o();return}const s=e.chunkLength||120,r=new RegExp("^[\\s\\S]{"+Math.floor(s/2)+","+s+"}[.!?,]{1}|^[\\s\\S]{1,"+s+"}$|^[\\s\\S]{1,"+s+"} "),c=i.match(r);if(!c||c[0]===void 0||c[0].length<=2){o&&o();return}const a=c[0],p=new SpeechSynthesisUtterance(a);for(const u in t)t.hasOwnProperty(u)&&u!=="text"&&(p[u]=t[u]);p.onend=()=>{e.offset=e.offset||0,e.offset+=a.length,A(t,e,o)},p.onerror=u=>{u.error!=="canceled"&&console.error("语音块播放错误:",u),u.error==="canceled"?setTimeout(()=>{A(t,e,o)},100):(e.offset=e.offset||0,e.offset+=a.length,setTimeout(()=>{A(t,e,o)},100))},window._lastUtterance=p,K(),setTimeout(()=>{window.speechSynthesis.pause(),window.speechSynthesis.resume(),window.speechSynthesis.speak(p)},10)},K=()=>{window._utteranceKeepAliveTimer&&clearInterval(window._utteranceKeepAliveTimer),window._utteranceKeepAliveTimer=ge(()=>{window.speechSynthesis.speaking?(window.speechSynthesis.pause(),window.speechSynthesis.resume()):(clearInterval(window._utteranceKeepAliveTimer),window._utteranceKeepAliveTimer=null)},1e3)},G=t=>{var e;try{console.log("使用本地音频合成播放:",t),d.info("正在播放: "+t,{timeout:2e3});const o=window.AudioContext||window.webkitAudioContext;if(!o){console.error("浏览器不支持AudioContext");return}const i=new o,s=i.createGain();s.gain.value=.3,s.connect(i.destination);const r=(u,M,B)=>{const k=i.createOscillator(),T=i.createGain();k.type="sine",k.frequency.value=u,T.gain.setValueAtTime(.3,M),T.gain.exponentialRampToValueAtTime(.001,M+B),k.connect(T),T.connect(s),k.start(M),k.stop(M+B)},c=i.currentTime;r(440,c,.1),r(523.25,c+.15,.2);const a=document.createElement("div");a.style.position="fixed",a.style.top="50%",a.style.left="50%",a.style.transform="translate(-50%, -50%)",a.style.backgroundColor="rgba(0, 0, 0, 0.8)",a.style.color="white",a.style.padding="20px",a.style.borderRadius="10px",a.style.fontSize="24px",a.style.zIndex="9999",a.style.textAlign="center";const p=(e=document.querySelector(`.pronunciation-word[data-word="${t}"]`))==null?void 0:e.getAttribute("data-pronunciation");p?a.innerHTML=`
        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">${t}</div>
        <div style="font-size: 18px; color: #aaa;">[${p}]</div>
      `:a.textContent=t,document.body.appendChild(a),setTimeout(()=>{document.body.removeChild(a)},3e3);try{"Notification"in window&&Notification.permission==="granted"&&new Notification("单词发音",{body:`${t} ${p?"["+p+"]":""}`,icon:"/favicon.ico"})}catch(u){console.warn("通知API不可用:",u)}}catch(o){console.error("本地音频合成失败:",o),d.error("无法播放音频",{timeout:3e3})}},X=t=>{const e=t.target.closest(".pronunciation-btn");if(e){t.preventDefault(),t.stopPropagation();const o=e.getAttribute("data-word"),i=e.getAttribute("data-pronunciation");if(o&&i){b.value=o,S.value=i;const s=e.closest(".pronunciation-word")||e.closest(".has-pronunciation");s&&(s.style.backgroundColor="#bae6fd",setTimeout(()=>{s.style.backgroundColor=""},1e3)),d.info(`正在朗读: ${o} [${i}]`,{timeout:2e3}),_(),setTimeout(()=>{if(v.value)U(o);else if("speechSynthesis"in window){window.speechSynthesis.cancel();const r=new SpeechSynthesisUtterance(o);/[\u4e00-\u9fa5]/.test(o)?r.lang="zh-CN":r.lang="en-US",r.rate=parseFloat(y.value),window.speechSynthesis.speak(r)}},300)}return}if(!v.value){const o=t.target.closest(".pronunciation-word");if(o){const i=o.getAttribute("data-word"),s=o.getAttribute("data-pronunciation");if(i&&s){if(o.style.backgroundColor="#bae6fd",setTimeout(()=>{o.style.backgroundColor=""},1e3),d.info(`正在朗读: ${i} [${s}]`,{timeout:2e3}),"speechSynthesis"in window){window.speechSynthesis.cancel();const r=new SpeechSynthesisUtterance(i);/[\u4e00-\u9fa5]/.test(i)?r.lang="zh-CN":r.lang="en-US",r.rate=parseFloat(y.value),window.speechSynthesis.speak(r)}b.value=i,S.value=s}}}},J=()=>{if(!(!b.value||!S.value))try{const t=b.value,e=S.value;if(d.info(`正在朗读: ${t} [${e}]`,{timeout:2e3}),v.value)_(),setTimeout(()=>{U(t)},300);else if("speechSynthesis"in window){window.speechSynthesis.cancel();const o=new SpeechSynthesisUtterance(t),i=window.speechSynthesis.getVoices();if(/[\u4e00-\u9fa5]/.test(t)){const s=i.find(r=>r.name.includes("Microsoft")&&r.lang.includes("zh"))||i.find(r=>r.lang.includes("zh"));s&&(o.voice=s),o.lang="zh-CN"}else{const s=i.find(r=>r.name.includes("Microsoft")&&r.lang.includes("en"))||i.find(r=>r.lang.includes("en"));s&&(o.voice=s),o.lang="en-US"}o.rate=parseFloat(y.value),window.speechSynthesis.speak(o)}else d.error("您的浏览器不支持语音合成功能")}catch(t){console.error("发音失败:",t),d.error(`发音失败: ${t instanceof Error?t.message:"未知错误"}`)}},Q=()=>{x.value=!x.value,d.info(`自动发音已${x.value?"开启":"关闭"}`)},N=async()=>{z.value=!0,w.value="";try{console.log(`尝试获取内容详情，ID: ${E}`);const t=localStorage.getItem("adminToken");if(console.log("当前Token:",t?`${t.substring(0,15)}...`:"未找到"),!t){w.value="未找到认证令牌，请重新登录",d.error(w.value),C.push("/admin/login");return}const e=await pe.get(`/api/admin/content/${E}`);console.log("API返回内容:",e),l.id=e.id,l.title=e.title||"",l.type=e.type||"",l.body=e.body||"",l.categories=Array.isArray(e.categories)?[...e.categories]:[],l.annotations=e.annotations||[],l.createdAt=e.createdAt,l.updatedAt=e.updatedAt}catch(t){console.error("获取内容详情失败，完整错误:",t),w.value=t.message||"获取内容详情失败",d.error(`获取内容失败: ${w.value}`),t.message&&(t.message.includes("认证失败")||t.message.includes("未提供认证令牌")||t.message.includes("无效的认证令牌"))&&(console.log("检测到认证错误，准备重定向到登录页"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminLoggedIn"),C.push("/admin/login"))}finally{z.value=!1}},Y=t=>({ARTICLE:"文章",LESSON:"课程",EXERCISE:"练习",NOTE:"笔记"})[t]||t,Z=t=>t?new Date(t).toLocaleString():"未知";re(()=>{N(),F(),navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1&&navigator.userAgent.indexOf("Edg")===-1&&(console.log("检测到Chrome浏览器，添加特殊处理"),v.value=!0,setTimeout(()=>{d.info("Chrome浏览器可能需要额外激活语音功能，请点击页面",{timeout:5e3,closeButton:!0})},1e3),document.addEventListener("click",function t(){_(),document.removeEventListener("click",t)},{once:!0}),"onvoiceschanged"in window.speechSynthesis&&(window.speechSynthesis.onvoiceschanged=()=>{const t=window.speechSynthesis.getVoices();console.log("语音列表已加载，共",t.length,"个语音");const e=t.filter(o=>o.name.includes("Microsoft"));e.length>0?console.log("找到Microsoft语音包:",e.map(o=>o.name).join(", ")):(console.warn("未找到Microsoft语音包，可能需要安装Windows语音包"),localStorage.getItem("voicePackTipDismissed")||setTimeout(()=>{D()},3e3))}))});const P=()=>{L.value=!1},_=()=>{try{console.log("尝试激活Chrome音频功能"),W=!0;const t=window.AudioContext||window.webkitAudioContext;if(t){const e=new t;e.resume().then(()=>{console.log("AudioContext已激活");const o=e.createOscillator(),i=e.createGain();o.type="sine",o.frequency.value=440,i.gain.value=.1,o.connect(i),i.connect(e.destination),o.start(),setTimeout(()=>{o.stop(),console.log("提示音播放完成");try{const s=new SpeechSynthesisUtterance(" ");s.volume=.01,s.onend=()=>{console.log("语音合成引擎已激活"),d.success("语音功能已激活",{timeout:3e3})},s.onerror=r=>{r.error!=="canceled"?console.error("激活语音合成引擎失败:",r):console.log("语音合成引擎已激活")},window.speechSynthesis.cancel(),window.speechSynthesis.pause(),window.speechSynthesis.resume(),window.speechSynthesis.speak(s)}catch(s){console.error("激活语音合成引擎失败:",s)}},100),console.log("语音合成引擎已激活")}).catch(o=>{console.error("激活AudioContext失败:",o),d.error("激活音频功能失败，请点击页面再试",{timeout:3e3})})}else console.error("浏览器不支持AudioContext"),d.error("您的浏览器不支持音频功能",{timeout:3e3})}catch(t){console.error("激活音频功能失败:",t),d.error("激活音频功能失败",{timeout:3e3})}},D=()=>{const t=document.createElement("div");t.className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4",t.innerHTML=`
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-yellow-700">
          <strong>未检测到Microsoft语音包。</strong> 为获得更好的语音效果，请安装Windows语音包：
        </p>
        <ol class="mt-2 text-sm text-yellow-700 list-decimal list-inside">
          <li>打开Windows设置 → 时间和语言 → 语言</li>
          <li>添加您需要的语言（如中文或英文）</li>
          <li>在已添加的语言中点击"选项"，下载"语音"包</li>
          <li>完成后刷新此页面</li>
        </ol>
        <div class="mt-2">
          <a href="https://support.microsoft.com/zh-cn/windows/下载语言和语音以供沉浸式阅读器-阅读模式和大声朗读使用-4c83a8d8-7486-42f7-8e46-2b0fdf753130" 
             target="_blank" 
             class="text-sm font-medium text-yellow-700 hover:text-yellow-600 underline">
            查看详细安装指南
          </a>
          <button id="close-voice-tip" class="ml-4 text-sm font-medium text-yellow-700 hover:text-yellow-600">
            关闭提示
          </button>
        </div>
      </div>
    </div>
  `;const e=document.querySelector(".flex-1.p-6");e&&(e.insertBefore(t,e.firstChild),document.getElementById("close-voice-tip").addEventListener("click",()=>{t.remove(),localStorage.setItem("voicePackTipDismissed","true")}))};return(t,e)=>(g(),m(j,null,[n("div",he,[le(me),n("div",we,[n("div",ve,[e[3]||(e[3]=n("h1",{class:"text-2xl font-bold"},"内容预览",-1)),n("div",ye,[n("button",{onClick:e[0]||(e[0]=o=>I(C).push("/admin/content/edit/"+I(E))),class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"},"编辑"),n("button",{onClick:e[1]||(e[1]=o=>I(C).push("/admin/content")),class:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"}," 返回列表 ")])]),z.value?(g(),m("div",xe,e[4]||(e[4]=[n("p",null,"加载中...",-1)]))):w.value?(g(),m("div",be,[e[5]||(e[5]=n("p",{class:"font-bold"},"加载失败:",-1)),n("p",null,h(w.value),1),n("button",{onClick:N,class:"mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"}," 重试 ")])):(g(),m("div",Se,[n("div",ke,[n("div",Ce,[n("div",null,[e[6]||(e[6]=n("h3",{class:"text-sm font-medium text-gray-500"},"标题",-1)),n("p",Ae,h(l.title||"无标题"),1)]),n("div",null,[e[7]||(e[7]=n("h3",{class:"text-sm font-medium text-gray-500"},"类型",-1)),n("p",_e,h(Y(l.type)),1)]),n("div",null,[e[8]||(e[8]=n("h3",{class:"text-sm font-medium text-gray-500"},"栏目",-1)),n("div",Me,[(g(!0),m(j,null,ae(l.categories,(o,i)=>(g(),m("span",{key:i,class:"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"},h(o),1))),128)),!l.categories||l.categories.length===0?(g(),m("span",Te," 无栏目 ")):$("",!0)])]),n("div",null,[e[9]||(e[9]=n("h3",{class:"text-sm font-medium text-gray-500"},"创建时间",-1)),n("p",$e,h(Z(l.createdAt)),1)])])]),n("div",Ee,[e[12]||(e[12]=n("h2",{class:"text-lg font-medium text-gray-700 mb-4"},"发音控制",-1)),n("div",ze,[n("button",{onClick:Q,class:de(["px-4 py-2 rounded",x.value?"bg-green-500 hover:bg-green-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"])},h(x.value?"自动发音已开启":"自动发音已关闭"),3),ce(n("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>y.value=o),class:"border rounded px-3 py-2"},e[10]||(e[10]=[n("option",{value:"0.8"},"慢速 (0.8x)",-1),n("option",{value:"1"},"正常 (1.0x)",-1),n("option",{value:"1.2"},"快速 (1.2x)",-1)]),512),[[ue,y.value]])]),v.value?(g(),m("div",Ie,e[11]||(e[11]=[n("p",{class:"flex items-center"},[n("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[n("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z","clip-rule":"evenodd"})]),O(" 提示：如果Chrome浏览器无法正常发音，请尝试使用Microsoft Edge浏览器访问本页面。 ")],-1)]))):$("",!0)]),n("div",Ve,[e[13]||(e[13]=n("h2",{class:"text-lg font-medium text-gray-700 mb-4"},"内容正文",-1)),n("div",{class:"prose max-w-none quill-content",innerHTML:H.value,onClick:X},null,8,Le),!l.body||l.body.trim()===""?(g(),m("pre",Ue,`            无内容
          `)):$("",!0)])]))])]),L.value?(g(),m("div",Ne,[n("div",{class:"absolute inset-0 bg-black bg-opacity-50",onClick:P}),n("div",Pe,[n("div",{class:"flex justify-between items-center mb-4"},[e[15]||(e[15]=n("h3",{class:"text-xl font-bold"},"单词发音",-1)),n("button",{onClick:P,class:"text-gray-500 hover:text-gray-700"},e[14]||(e[14]=[n("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n("div",De,[n("div",Be,[n("span",je,h(b.value),1),n("div",Oe,h(S.value),1)]),n("div",{class:"flex justify-center flex-col items-center"},[n("button",{onClick:J,class:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full flex items-center mb-3"},e[16]||(e[16]=[n("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.414l-2.829-2.828a4 4 0 015.657-5.657l2.828 2.829m7.072 7.072l2.828 2.829a4 4 0 11-5.657 5.657l-2.828-2.829"})],-1),O(" 点击播放发音 ",-1)]))]),e[17]||(e[17]=n("div",{class:"mt-6 text-sm text-gray-500"},[n("p",{class:"font-bold text-orange-500"},"注意：Chrome浏览器可能无法正常发音"),n("ul",{class:"list-disc pl-5 mt-2"},[n("li",null,"Chrome浏览器对Web Speech API有严格限制，可能导致发音功能无法正常工作"),n("li",{class:"font-bold"},"推荐使用Microsoft Edge浏览器访问本页面获得最佳体验"),n("li",null,'您也可以多次点击"播放发音"按钮尝试激活发音功能'),n("li",null,"确保您已授予网站音频权限（点击地址栏左侧的锁图标检查）")])],-1))])])])):$("",!0)],64))}}),Qe=fe(Re,[["__scopeId","data-v-874cd033"]]);export{Qe as default};
