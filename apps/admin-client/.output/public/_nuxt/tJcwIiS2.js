import{G as g}from"./qi-a-hqW.js";import{a as s}from"./CxzfvvNm.js";const u=g("content",{state:()=>({contents:[],currentContent:null,loading:!1,error:null}),actions:{async fetchContents(e){this.loading=!0,this.error=null;try{const t=new URLSearchParams;e!=null&&e.page&&t.append("page",e.page.toString()),e!=null&&e.pageSize&&t.append("pageSize",e.pageSize.toString()),e!=null&&e.category&&t.append("category",e.category),e!=null&&e.type&&t.append("type",e.type);const n=t.toString(),c=`/api/admin/content${n?`?${n}`:""}`,o=await s.get(c);return o&&o.contents&&o.pagination?(this.contents=o.contents,{contents:o.contents,pagination:o.pagination}):(this.contents=o,{contents:o,pagination:null})}catch(t){throw this.error=t instanceof Error?t.message:"获取内容列表失败",console.error("获取内容列表失败",t),t}finally{this.loading=!1}},async createContent(e){this.loading=!0,this.error=null;try{const t=await s.post("/api/admin/content",e);return this.contents.push(t),this.currentContent=t,t}catch(t){throw this.error=t instanceof Error?t.message:"创建内容失败",console.error("创建内容失败",t),t}finally{this.loading=!1}},async deleteContent(e){this.loading=!0,this.error=null;try{await s.delete(`/api/admin/content/${e}`),this.contents=this.contents.filter(t=>t.id!==e)}catch(t){throw this.error=t instanceof Error?t.message:"删除内容失败",console.error("删除内容失败",t),t}finally{this.loading=!1}},async fetchContentByCategory(e,t){this.loading=!0,this.error=null;try{const n=`/api/admin/content/category?category=${encodeURIComponent(e)}&type=${encodeURIComponent(t)}`;this.contents=await s.get(n)}catch(n){throw this.error=n instanceof Error?n.message:"获取内容失败",console.error("获取内容失败",n),n}finally{this.loading=!1}},async updateContent(e,t){var n,c,o;this.loading=!0,this.error=null;try{console.log(`准备更新内容 ${e}`,{type:t.type,title:((n=t.title)==null?void 0:n.substring(0,20))+"...",categoriesCount:((c=t.categories)==null?void 0:c.length)||0,bodyPreview:((o=t.body)==null?void 0:o.substring(0,30))+"..."});const r=`/api/admin/content/${e}`;console.log(`发送PUT请求到: ${r}`);const i=await s.put(r,t),l=this.contents.findIndex(a=>a.id===e);return l!==-1&&(this.contents[l]=i),this.currentContent=i,console.log("内容更新成功:",i.id),i}catch(r){console.error("更新内容失败:",r);let i="更新内容失败";throw r instanceof Error?(i=r.message,console.error("错误详情:",{name:r.name,message:r.message,stack:r.stack})):console.error("未知错误类型:",r),this.error=i,r}finally{this.loading=!1}}}});export{u};
