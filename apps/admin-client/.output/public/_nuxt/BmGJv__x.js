import{p as F,z as S,r as c,y as U,g as X,c as a,o as n,b as q,a as e,h as z,i as L,v as G,l as H,N as M,D as E,F as $,j as T,t as r,n as p,d as w}from"./qi-a-hqW.js";import{u as J}from"./tJcwIiS2.js";import{A as K}from"./oUsj6fiR.js";import"./CxzfvvNm.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const Q={class:"flex"},W={class:"flex-1 p-6"},Y={class:"flex justify-between mb-6"},Z={class:"flex space-x-2"},ee={class:"relative"},te={key:0,class:"text-center"},se={key:1,class:"text-center text-gray-500"},le={key:2,class:"bg-white shadow-md rounded"},oe={class:"w-full"},ae={class:"p-3"},ne={class:"p-3"},re={class:"p-3"},ie={class:"p-3"},de={class:"p-3"},ue={class:"p-3"},ve={class:"flex space-x-2"},ce=["onClick"],pe=["onClick"],ge=["onClick"],me={key:0,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200"},xe={class:"flex-1 flex justify-between sm:hidden"},be=["disabled"],he=["disabled"],ye={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},fe={class:"text-sm text-gray-700"},we={class:"font-medium"},_e={class:"font-medium"},Ce={class:"font-medium"},ke={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},Se=["disabled"],Me=["disabled"],Ee=["onClick"],$e={key:1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},ze=["disabled"],Le=["disabled"],Te={key:1,class:"bg-white px-4 py-3 text-center text-sm text-gray-500 border-t border-gray-200"},Ne={key:3,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4"},Oe={__name:"index",setup(Be){const _=F(),g=J(),C=S(()=>g.contents),m=c(""),b=c(""),k=c(null),o=c(1),h=c(10),d=c(0),x=c(null),u=async()=>{try{const s=await g.fetchContents({page:o.value,pageSize:h.value,category:m.value||void 0,type:b.value||void 0});console.log("获取内容列表结果:",s),s!=null&&s.pagination?(x.value=s.pagination,d.value=s.pagination.total,console.log("分页信息:",x.value),console.log("总条数:",d.value),console.log("总页数:",Math.ceil(d.value/h.value))):console.warn("未获取到分页信息")}catch(s){console.error("获取内容列表失败",s)}},N=()=>{k.value&&clearTimeout(k.value),k.value=setTimeout(()=>{o.value=1,u()},300)},B=()=>{m.value="",o.value=1,u()},j=()=>{o.value=1,u()},v=s=>{o.value=s,u()},i=S(()=>x.value?x.value.pageCount:1),V=S(()=>{const s=i.value,t=o.value;if(s<=7)return Array.from({length:s},(y,f)=>f+1);const l=[];return l.push(1),t<=3?l.push(2,3,4,"...",s-1,s):t>=s-2?l.push("...",s-4,s-3,s-2,s-1,s):l.push("...",t-1,t,t+1,"...",s),l}),A=s=>({ARTICLE:"文章",LESSON:"课程",EXERCISE:"练习",NOTE:"笔记"})[s]||s,I=s=>new Date(s).toLocaleString(),D=()=>{_.push("/admin/content/new")},P=s=>{_.push(`/admin/content/edit/${s.id}`)},R=async s=>{if(confirm("确定要删除这个内容吗？"))try{await g.deleteContent(s),u()}catch(t){console.error("删除内容失败",t)}},O=s=>{_.push(`/admin/content/preview/${s.id}`)};return U([o,b,m],()=>{u()},{deep:!0}),X(()=>{u()}),(s,t)=>(n(),a("div",Q,[q(K),e("div",W,[e("div",{class:"flex justify-between items-center mb-6"},[t[8]||(t[8]=e("h1",{class:"text-2xl font-bold"},"内容管理",-1)),e("button",{onClick:D,class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"}," 创建内容 ")]),e("div",Y,[e("div",Z,[e("div",ee,[L(e("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>m.value=l),type:"text",placeholder:"搜索栏目...",class:"border rounded px-3 py-2 pr-8",onInput:N},null,544),[[G,m.value]]),m.value?(n(),a("span",{key:0,onClick:B,class:"absolute right-2 top-2 cursor-pointer text-gray-500"}," × ")):z("",!0)]),L(e("select",{"onUpdate:modelValue":t[1]||(t[1]=l=>b.value=l),class:"border rounded px-3 py-2",onChange:j},t[9]||(t[9]=[M('<option value="">全部类型</option><option value="ARTICLE">文章</option><option value="LESSON">课程</option><option value="EXERCISE">练习</option><option value="NOTE">笔记</option>',5)]),544),[[H,b.value]])])]),E(g).loading?(n(),a("div",te," 加载中... ")):C.value.length===0?(n(),a("div",se," 暂无内容 ")):(n(),a("div",le,[e("table",oe,[t[10]||(t[10]=e("thead",null,[e("tr",{class:"bg-gray-100 border-b"},[e("th",{class:"p-3 text-left"},"标题"),e("th",{class:"p-3 text-left"},"类型"),e("th",{class:"p-3 text-left"},"栏目"),e("th",{class:"p-3 text-left"},"作者"),e("th",{class:"p-3 text-left"},"创建时间"),e("th",{class:"p-3 text-left"},"操作")])],-1)),e("tbody",null,[(n(!0),a($,null,T(C.value,l=>{var y;return n(),a("tr",{key:l.id,class:"border-b hover:bg-gray-50"},[e("td",ae,r(l.title||"无标题"),1),e("td",ne,r(A(l.type)),1),e("td",re,r(l.categories?l.categories.join(", "):"-"),1),e("td",ie,r(((y=l.user)==null?void 0:y.username)||"未知"),1),e("td",de,r(I(l.createdAt)),1),e("td",ue,[e("div",ve,[e("button",{onClick:f=>O(l),class:"text-green-500 hover:text-green-700"}," 预览 ",8,ce),e("button",{onClick:f=>P(l),class:"text-blue-500 hover:text-blue-700"}," 编辑 ",8,pe),e("button",{onClick:f=>R(l.id),class:"text-red-500 hover:text-red-700"}," 删除 ",8,ge)])])])}),128))])]),x.value&&d.value>0?(n(),a("div",me,[e("div",xe,[e("button",{onClick:t[2]||(t[2]=l=>o.value>1&&v(o.value-1)),disabled:o.value===1,class:p(["relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])}," 上一页 ",10,be),e("button",{onClick:t[3]||(t[3]=l=>o.value<i.value&&v(o.value+1)),disabled:o.value===i.value,class:p(["ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===i.value}])}," 下一页 ",10,he)]),e("div",ye,[e("div",null,[e("p",fe,[t[11]||(t[11]=w(" 显示第 ",-1)),e("span",we,r(C.value.length>0?(o.value-1)*h.value+1:0),1),t[12]||(t[12]=w(" 至 ",-1)),e("span",_e,r(Math.min(o.value*h.value,d.value)),1),t[13]||(t[13]=w(" 条，共 ",-1)),e("span",Ce,r(d.value),1),t[14]||(t[14]=w(" 条 ",-1))])]),e("div",null,[e("nav",ke,[e("button",{onClick:t[4]||(t[4]=l=>v(1)),disabled:o.value===1,class:p(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])},t[15]||(t[15]=[M('<span class="sr-only">首页</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',3)]),10,Se),e("button",{onClick:t[5]||(t[5]=l=>o.value>1&&v(o.value-1)),disabled:o.value===1,class:p(["relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===1}])},t[16]||(t[16]=[e("span",{class:"sr-only"},"上一页",-1),e("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,Me),(n(!0),a($,null,T(V.value,l=>(n(),a($,{key:l},[l!=="..."?(n(),a("button",{key:0,onClick:y=>v(l),class:p([o.value===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},r(l),11,Ee)):(n(),a("span",$e," ... "))],64))),128)),e("button",{onClick:t[6]||(t[6]=l=>o.value<i.value&&v(o.value+1)),disabled:o.value===i.value,class:p(["relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===i.value}])},t[17]||(t[17]=[e("span",{class:"sr-only"},"下一页",-1),e("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,ze),e("button",{onClick:t[7]||(t[7]=l=>v(i.value)),disabled:o.value===i.value,class:p(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",{"opacity-50 cursor-not-allowed":o.value===i.value}])},t[18]||(t[18]=[M('<span class="sr-only">尾页</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>',3)]),10,Le)])])])])):(n(),a("div",Te,r(x.value?`只有一页数据，共${d.value}条`:"未获取到分页信息"),1))])),E(g).error?(n(),a("div",Ne,r(E(g).error),1)):z("",!0)])]))}};export{Oe as default};
