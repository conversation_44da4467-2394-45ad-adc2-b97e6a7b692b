import{e as te,f as se,r as i,z as j,g as oe,c,o as p,b as le,a as t,h,t as u,d as m,i as v,v as b,l as z,F as $,j as O,n as k,m as A}from"./qi-a-hqW.js";import{A as ae}from"./oUsj6fiR.js";import{a as _}from"./CxzfvvNm.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const ne={class:"flex min-h-screen bg-gray-100 overflow-x-hidden"},re={class:"flex-1"},ie={class:"p-8"},de={class:"w-full max-w-6xl mx-auto"},ue={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"},ce={class:"mb-6 text-right"},pe={class:"bg-white p-4 rounded-lg shadow mb-6"},ve={class:"flex flex-wrap gap-4"},ge={class:"flex-1 min-w-[200px]"},fe={class:"w-auto"},xe={class:"w-auto"},me={key:1,class:"w-full flex justify-center py-12"},be={key:2,class:"bg-white rounded-lg shadow overflow-hidden"},ye={class:"min-w-full divide-y divide-gray-200"},he={class:"bg-white divide-y divide-gray-200"},we={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ke={class:"px-6 py-4 whitespace-nowrap"},_e=["src"],Pe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Le={class:"px-6 py-4 whitespace-nowrap"},Ue=["onClick"],Fe={class:"px-6 py-4 whitespace-nowrap"},Ve={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ne={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ie={class:"flex space-x-2"},De=["onClick"],Ee=["onClick"],ze={class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},Re={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Se={class:"text-sm text-gray-700"},Te={class:"font-medium"},je={class:"font-medium"},$e={class:"font-medium"},Oe={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"分页"},Ae=["disabled"],Me=["onClick"],Be=["disabled"],Ye={key:3,class:"bg-white p-8 rounded-lg shadow text-center"},Je={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},qe={class:"bg-white rounded-lg p-6 w-full max-w-md"},He={class:"mb-4"},Qe={class:"mb-4"},We={class:"mb-4"},Ge={key:0,class:"mt-2"},Ke=["src"],Xe={class:"mb-4"},Ze={class:"mb-4 flex items-center"},et={class:"flex items-center justify-between"},tt=["disabled"],st={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ot={class:"bg-white rounded-lg p-6 w-full max-w-md"},lt={class:"mb-4"},at={class:"mb-4"},nt={class:"mb-4"},rt={key:0,class:"mt-2"},it=["src"],dt={class:"mb-4"},ut={class:"mb-4 flex items-center"},ct={class:"flex items-center justify-between"},pt=["disabled"],vt={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},gt={class:"bg-white rounded-lg p-6 w-full max-w-md"},ft={class:"mb-6"},xt={class:"flex items-center justify-between"},mt=["disabled"],Ct=te({__name:"course-packages",setup(bt){const d=se();function M(o){return o?{PRIVATE:"私有",PUBLIC:"公开",FOUNDER_ONLY:"仅创始人"}[o]||o:"私有"}const g=i([]),R=i(!0),P=i(null),C=i(""),L=i("all"),U=i("all"),a=i({page:1,pageSize:10,total:0,totalPages:1}),F=i(!1),V=i(!1),r=i({title:"",description:"",cover:"",isFree:!1,order:1,shareLevel:"PRIVATE"}),N=i(!1),I=i(!1),n=i({}),D=i(!1),E=i(!1),y=i(null),B=j(()=>{const o=a.value.totalPages||1,e=a.value.page||1;if(o<=5)return Array.from({length:o},(f,x)=>x+1);let l=[e],s=1;for(;l.length<5&&e-s>0;)l.unshift(e-s),s++;for(s=1;l.length<5&&e+s<=o;)l.push(e+s),s++;for(;l.length<5;)if(l[0]>1)l.unshift(l[0]-1);else if(l[l.length-1]<o)l.push(l[l.length-1]+1);else break;return l}),Y=j(()=>{let o=g.value;if(C.value){const e=C.value.toLowerCase();o=o.filter(l=>{var s;return l.title.toLowerCase().includes(e)||((s=l.description)==null?void 0:s.toLowerCase().includes(e))})}if(L.value!=="all"){const e=L.value==="free";o=o.filter(l=>l.isFree===e)}return U.value!=="all"&&(o=o.filter(e=>e.shareLevel===U.value)),o});async function S(o){o<1||o>a.value.totalPages||(a.value.page=o,await w())}async function w(){R.value=!0,P.value=null;try{console.log("正在获取课程包...");const o=await _.get("/api/admin/course-packages",{page:a.value.page,pageSize:a.value.pageSize}),e=o.packages||[];console.log("课程包数据获取成功:",e?e.length:0,"个课程包"),g.value=e,o.pagination&&(a.value={page:Number(o.pagination.page)||1,pageSize:Number(o.pagination.pageSize)||10,total:Number(o.pagination.total)||0,totalPages:Number(o.pagination.totalPages)||1}),(!e||e.length===0)&&console.log("没有课程包数据")}catch(o){console.error("获取课程包失败:",o),P.value=o.message||"获取课程包失败，请刷新页面重试",d.error("获取课程包失败: "+(o.message||"未知错误"))}finally{R.value=!1}}async function J(){if(!r.value.title){d.error("请填写课程包标题");return}V.value=!0;try{console.log("正在创建课程包...");let o="";const e=localStorage.getItem("adminUser");if(e)try{o=JSON.parse(e).id,console.log("从adminUser获取到用户ID:",o)}catch(s){console.error("解析用户信息失败:",s)}if(!o)throw new Error("未获取到用户ID，请重新登录");const l=await _.post("/api/admin/course-packages",{...r.value,order:1,creatorId:o,shareLevel:r.value.shareLevel});F.value=!1,d.success("课程包创建成功"),r.value={title:"",description:"",cover:"",isFree:!1,order:1,shareLevel:"PRIVATE"},a.value.page=1,await w()}catch(o){console.error("创建课程包错误:",o),d.error(o.message||"创建课程包时发生错误")}finally{V.value=!1}}function q(o){n.value={...o},N.value=!0}async function H(){if(!n.value.title){d.error("请填写课程包标题");return}I.value=!0;try{const o={title:n.value.title,description:n.value.description,cover:n.value.cover,isFree:n.value.isFree,shareLevel:n.value.shareLevel};console.log("正在更新课程包:",n.value.id),console.log("更新数据:",JSON.stringify(o,null,2));const e=await _.put(`/api/admin/course-packages/${n.value.id}`,o);console.log("更新响应:",e),N.value=!1,d.success("课程包更新成功"),await w()}catch(o){console.error("更新课程包失败:",o),d.error(o.message||"更新课程包时发生错误")}finally{I.value=!1}}function Q(o){y.value=o,D.value=!0}async function W(){if(y.value){E.value=!0;try{console.log("正在删除课程包:",y.value.id),await _.delete(`/api/admin/course-packages/${y.value.id}`),D.value=!1,d.success("课程包删除成功"),g.value.length===1&&a.value.page>1&&(a.value.page=a.value.page-1),await w()}catch(o){console.error("删除课程包失败:",o),d.error(o.message||"删除课程包时发生错误")}finally{E.value=!1,y.value=null}}}function G(o){if(!o)return"未知";const e=new Date(o);return isNaN(e.getTime())?o:new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).format(e)}async function K(o){try{console.log("正在切换课程包状态:",o.id),await _.post(`/api/admin/course-packages/${o.id}/toggle-status`);const e=g.value.findIndex(l=>l.id===o.id);e!==-1&&(g.value[e].isFree=!g.value[e].isFree),d.success("课程包状态已更新")}catch(e){console.error("切换课程包状态错误:",e),d.error(e.message||"切换课程包状态时发生错误")}}async function T(o){const e=new FormData;e.append("file",o);try{console.log("正在上传图片...");const l=localStorage.getItem("adminToken"),s=l?l.startsWith("Bearer ")?l:`Bearer ${l}`:"",f=await fetch("/api/admin/upload",{method:"POST",headers:{Authorization:s},body:e});if(!f.ok){const ee=await f.text();throw console.error("上传失败详细信息:",ee),new Error("图片上传失败")}const x=await f.json();return console.log("图片上传成功:",x),x.url}catch(l){throw console.error("图片上传失败:",l),l}}async function X(o){var l;const e=(l=o.target.files)==null?void 0:l[0];if(e)try{const s=await T(e);r.value.cover=s,d.success("图片上传成功")}catch{d.error("图片上传失败")}}async function Z(o){var l;const e=(l=o.target.files)==null?void 0:l[0];if(e)try{const s=await T(e);n.value.cover=s,d.success("图片上传成功")}catch{d.error("图片上传失败")}}return oe(()=>{w()}),(o,e)=>{var l;return p(),c("div",ne,[le(ae),t("div",re,[t("div",ie,[t("div",de,[e[31]||(e[31]=t("h1",{class:"text-2xl font-bold mb-8 text-center"},"课程包管理",-1)),P.value?(p(),c("div",ue,[t("p",null,u(P.value),1)])):h("",!0),t("div",ce,[t("button",{onClick:e[0]||(e[0]=s=>F.value=!0),class:"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 ml-auto"},e[19]||(e[19]=[t("span",{class:"mr-2"},"+",-1),m(" 创建新课程包 ",-1)]))]),t("div",pe,[t("div",ve,[t("div",ge,[v(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>C.value=s),type:"text",placeholder:"搜索课程包...",class:"w-full px-4 py-2 border rounded-lg"},null,512),[[b,C.value]])]),t("div",fe,[v(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>L.value=s),class:"w-full px-4 py-2 border rounded-lg"},e[20]||(e[20]=[t("option",{value:"all"},"所有价格",-1),t("option",{value:"free"},"免费",-1),t("option",{value:"paid"},"付费",-1)]),512),[[z,L.value]])]),t("div",xe,[v(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>U.value=s),class:"w-full px-4 py-2 border rounded-lg"},e[21]||(e[21]=[t("option",{value:"all"},"所有分享级别",-1),t("option",{value:"PRIVATE"},"私有",-1),t("option",{value:"PUBLIC"},"公开",-1),t("option",{value:"FOUNDER_ONLY"},"仅创始人",-1)]),512),[[z,U.value]])])])]),R.value?(p(),c("div",me,e[22]||(e[22]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"},null,-1)]))):g.value&&g.value.length>0?(p(),c("div",be,[t("table",ye,[e[23]||(e[23]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," ID "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 封面 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 标题 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 课程数量 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 价格 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 分享级别 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 创建时间 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 操作 ")])],-1)),t("tbody",he,[(p(!0),c($,null,O(Y.value,s=>{var f;return p(),c("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",we,u(s.id.slice(0,8))+"... ",1),t("td",ke,[t("img",{src:s.cover||"https://via.placeholder.com/50",alt:"Cover",class:"h-10 w-16 object-cover rounded"},null,8,_e)]),t("td",Pe,u(s.title),1),t("td",Ce,u(((f=s.courses)==null?void 0:f.length)||0),1),t("td",Le,[t("button",{onClick:x=>K(s),class:k(["px-3 py-1 rounded-full text-xs font-semibold",s.isFree?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"])},u(s.isFree?"免费":"付费"),11,Ue)]),t("td",Fe,[t("span",{class:k(["px-3 py-1 rounded-full text-xs font-semibold",{"bg-red-100 text-red-800":s.shareLevel==="PRIVATE","bg-green-100 text-green-800":s.shareLevel==="PUBLIC","bg-yellow-100 text-yellow-800":s.shareLevel==="FOUNDER_ONLY"}])},u(M(s.shareLevel)),3)]),t("td",Ve,u(G(s.createdAt)),1),t("td",Ne,[t("div",Ie,[t("button",{onClick:x=>q(s),class:"text-indigo-600 hover:text-indigo-900 mr-2"}," 编辑 ",8,De),t("button",{onClick:x=>Q(s),class:"text-red-600 hover:text-red-900"}," 删除 ",8,Ee)])])])}),128))])]),t("div",ze,[t("div",Re,[t("div",null,[t("p",Se,[e[24]||(e[24]=m(" 显示 ",-1)),t("span",Te,u((a.value.page-1)*a.value.pageSize+1),1),e[25]||(e[25]=m(" 至 ",-1)),t("span",je,u(Math.min(a.value.page*a.value.pageSize,a.value.total)),1),e[26]||(e[26]=m(" 条，共 ",-1)),t("span",$e,u(a.value.total),1),e[27]||(e[27]=m(" 条 ",-1))])]),t("div",null,[t("nav",Oe,[t("button",{onClick:e[4]||(e[4]=s=>S(a.value.page-1)),disabled:a.value.page<=1,class:k(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",a.value.page<=1?"opacity-50 cursor-not-allowed":""])},e[28]||(e[28]=[t("span",{class:"sr-only"},"上一页",-1),m(" « ",-1)]),10,Ae),(p(!0),c($,null,O(B.value,s=>(p(),c("span",{key:s,onClick:f=>S(s),class:k(["relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium cursor-pointer",s===a.value.page?"bg-blue-50 text-blue-600 z-10 border-blue-500":"text-gray-500 hover:bg-gray-50"])},u(s),11,Me))),128)),t("button",{onClick:e[5]||(e[5]=s=>S(a.value.page+1)),disabled:a.value.page>=a.value.totalPages,class:k(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",a.value.page>=a.value.totalPages?"opacity-50 cursor-not-allowed":""])},e[29]||(e[29]=[t("span",{class:"sr-only"},"下一页",-1),m(" » ",-1)]),10,Be)])])])])])):(p(),c("div",Ye,e[30]||(e[30]=[t("p",{class:"text-gray-500"},"暂无课程包数据",-1)])))])])]),F.value?(p(),c("div",Je,[t("div",qe,[e[38]||(e[38]=t("h2",{class:"text-xl font-bold mb-4"},"创建新课程包",-1)),t("div",He,[e[32]||(e[32]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"title"}," 标题 ",-1)),v(t("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>r.value.title=s),id:"title",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[b,r.value.title]])]),t("div",Qe,[e[33]||(e[33]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"description"}," 描述 ",-1)),v(t("textarea",{"onUpdate:modelValue":e[7]||(e[7]=s=>r.value.description=s),id:"description",rows:"3",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[b,r.value.description]])]),t("div",We,[e[34]||(e[34]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"cover"}," 封面URL ",-1)),v(t("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>r.value.cover=s),id:"cover",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"可填写网络图片URL"},null,512),[[b,r.value.cover]]),t("input",{type:"file",accept:"image/*",class:"mt-2",onChange:X},null,32),r.value.cover?(p(),c("div",Ge,[t("img",{src:r.value.cover,alt:"Cover Preview",class:"h-20 object-cover rounded"},null,8,Ke)])):h("",!0)]),t("div",Xe,[e[36]||(e[36]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"shareLevel"}," 分享级别 ",-1)),v(t("select",{"onUpdate:modelValue":e[9]||(e[9]=s=>r.value.shareLevel=s),id:"shareLevel",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},e[35]||(e[35]=[t("option",{value:"PRIVATE"},"私有",-1),t("option",{value:"PUBLIC"},"公开",-1),t("option",{value:"FOUNDER_ONLY"},"仅创始人",-1)]),512),[[z,r.value.shareLevel]])]),t("div",Ze,[v(t("input",{"onUpdate:modelValue":e[10]||(e[10]=s=>r.value.isFree=s),id:"isFree",type:"checkbox",class:"mr-2"},null,512),[[A,r.value.isFree]]),e[37]||(e[37]=t("label",{class:"text-gray-700 text-sm font-bold",for:"isFree"}," 免费课程包 ",-1))]),t("div",et,[t("button",{onClick:J,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:V.value},u(V.value?"创建中...":"创建"),9,tt),t("button",{onClick:e[11]||(e[11]=s=>F.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):h("",!0),N.value?(p(),c("div",st,[t("div",ot,[e[45]||(e[45]=t("h2",{class:"text-xl font-bold mb-4"},"编辑课程包",-1)),t("div",lt,[e[39]||(e[39]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-title"}," 标题 ",-1)),v(t("input",{"onUpdate:modelValue":e[12]||(e[12]=s=>n.value.title=s),id:"edit-title",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[b,n.value.title]])]),t("div",at,[e[40]||(e[40]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-description"}," 描述 ",-1)),v(t("textarea",{"onUpdate:modelValue":e[13]||(e[13]=s=>n.value.description=s),id:"edit-description",rows:"3",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[b,n.value.description]])]),t("div",nt,[e[41]||(e[41]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-cover"}," 封面URL ",-1)),v(t("input",{"onUpdate:modelValue":e[14]||(e[14]=s=>n.value.cover=s),id:"edit-cover",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"可填写网络图片URL"},null,512),[[b,n.value.cover]]),t("input",{type:"file",accept:"image/*",class:"mt-2",onChange:Z},null,32),n.value.cover?(p(),c("div",rt,[t("img",{src:n.value.cover,alt:"Cover Preview",class:"h-20 object-cover rounded"},null,8,it)])):h("",!0)]),t("div",dt,[e[43]||(e[43]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-shareLevel"}," 分享级别 ",-1)),v(t("select",{"onUpdate:modelValue":e[15]||(e[15]=s=>n.value.shareLevel=s),id:"edit-shareLevel",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},e[42]||(e[42]=[t("option",{value:"PRIVATE"},"私有",-1),t("option",{value:"PUBLIC"},"公开",-1),t("option",{value:"FOUNDER_ONLY"},"仅创始人",-1)]),512),[[z,n.value.shareLevel]])]),t("div",ut,[v(t("input",{"onUpdate:modelValue":e[16]||(e[16]=s=>n.value.isFree=s),id:"edit-isFree",type:"checkbox",class:"mr-2"},null,512),[[A,n.value.isFree]]),e[44]||(e[44]=t("label",{class:"text-gray-700 text-sm font-bold",for:"edit-isFree"}," 免费课程包 ",-1))]),t("div",ct,[t("button",{onClick:H,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:I.value},u(I.value?"更新中...":"更新"),9,pt),t("button",{onClick:e[17]||(e[17]=s=>N.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):h("",!0),D.value?(p(),c("div",vt,[t("div",gt,[e[46]||(e[46]=t("h2",{class:"text-xl font-bold mb-4"},"确认删除",-1)),t("p",ft,'确定要删除课程包 "'+u((l=y.value)==null?void 0:l.title)+'" 吗？此操作无法撤销。',1),t("div",xt,[t("button",{onClick:W,class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:E.value},u(E.value?"删除中...":"确认删除"),9,mt),t("button",{onClick:e[18]||(e[18]=s=>D.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):h("",!0)])}}});export{Ct as default};
