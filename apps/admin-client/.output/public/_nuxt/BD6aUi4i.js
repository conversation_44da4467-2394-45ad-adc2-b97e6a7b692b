import{e as s,z as a,c as d,o as c,a as r,n as u,t as o}from"./qi-a-hqW.js";const x={class:"bg-white rounded-lg shadow-md p-6 flex items-center border border-gray-200"},g={class:"flex-shrink-0 mr-4"},p={class:"text-lg font-semibold text-gray-800 mb-2"},m={class:"text-3xl font-bold text-gray-900"},y=s({__name:"StatCard",props:{title:{type:String,required:!0},value:{type:Number,required:!0},icon:{type:String,required:!0},color:{type:String,default:"blue",validator:e=>["blue","green","purple","red","yellow","orange","indigo","pink"].includes(e)}},setup(e){const n=e,l={blue:"text-blue-500",green:"text-green-500",purple:"text-purple-500",red:"text-red-500",yellow:"text-yellow-500",orange:"text-orange-500",indigo:"text-indigo-500",pink:"text-pink-500"},i=a(()=>{const t=n.value||0;return t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toString()});return(t,f)=>(c(),d("div",x,[r("div",g,[r("div",{class:u(["text-4xl",`${l[e.color]}`])},o(e.icon),3)]),r("div",null,[r("h3",p,o(e.title),1),r("p",m,o(i.value),1)])]))}});export{y as default};
