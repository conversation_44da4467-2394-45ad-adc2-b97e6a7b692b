import{_ as y}from"./eAbK7LvP.js";import{r as a,q as _,f as M,g as k,c as s,o as l,a as e,b as C,h as g,w as L,t as V,s as z,i as v,v as B,x as A,d as T}from"./qi-a-hqW.js";const N={class:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600"},S={class:"w-full max-w-md"},j={class:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4"},q={key:0,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded text-center"},D={key:0,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-center"},U={class:"mb-4"},Z={class:"mb-6 relative"},E={class:"relative"},H=["type"],P={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},R={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},F={class:"flex items-center justify-center"},G=["disabled"],I={key:0},J={key:1,class:"flex items-center justify-center"},W={__name:"login",setup(K){const d=a(""),u=a(""),o=a(""),r=a(!1),{adminLogin:f,isAdminAuthenticated:b}=_(),i=a(!1),c=M(),m=a(!1),w=()=>{r.value=!r.value},x=async()=>{o.value="",i.value=!0;try{console.log("尝试登录...");const n=await f(d.value,u.value);n.success?(c.success("登录成功，正在跳转..."),setTimeout(()=>{window.location.href="/admin/dashboard"},1e3)):(o.value=n.message||"登录失败，请检查您的凭据",c.error(o.value))}catch(n){console.error("登录失败:",n.message),o.value=n.message||"登录失败，请检查您的凭据",c.error(o.value)}finally{i.value=!1}};return k(()=>{console.log("[Login.onMounted] 开始检查登录状态"),typeof window<"u"&&(b()?(console.log("[Login.onMounted] 已登录，准备跳转到仪表盘"),m.value=!0,setTimeout(()=>{console.log("[Login.onMounted] 执行跳转到仪表盘"),window.location.replace("/admin/dashboard")},500)):console.log("[Login.onMounted] 未登录"))}),(n,t)=>{const h=y;return l(),s("div",N,[e("div",S,[e("div",j,[t[7]||(t[7]=e("div",{class:"mb-6 text-center"},[e("h1",{class:"text-3xl font-bold text-gray-800"},"管理后台"),e("p",{class:"text-gray-600 mt-2"},"ZeroBase 管理系统")],-1)),C(h,null,{default:L(()=>[m.value?(l(),s("div",q," 已检测到登录状态，正在跳转到仪表盘... ")):g("",!0)]),_:1}),o.value?(l(),s("div",D,V(o.value),1)):g("",!0),e("form",{onSubmit:z(x,["prevent"])},[e("div",U,[t[2]||(t[2]=e("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"email"}," 邮箱 ",-1)),v(e("input",{"onUpdate:modelValue":t[0]||(t[0]=p=>d.value=p),type:"email",required:"",class:"shadow-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"请输入管理员邮箱"},null,512),[[B,d.value]])]),e("div",Z,[t[5]||(t[5]=e("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"password"}," 密码 ",-1)),e("div",E,[v(e("input",{type:r.value?"text":"password","onUpdate:modelValue":t[1]||(t[1]=p=>u.value=p),required:"",class:"shadow-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10",placeholder:"请输入密码"},null,8,H),[[A,u.value]]),e("button",{type:"button",onClick:w,class:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none","aria-label":"切换密码可见性"},[r.value?(l(),s("svg",R,t[4]||(t[4]=[e("path",{"fill-rule":"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.781-1.781zm4.261 4.262l1.514 1.514a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z","clip-rule":"evenodd"},null,-1),e("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.742L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.064 7 9.542 7 .847 0 1.67-.105 2.454-.303z"},null,-1)]))):(l(),s("svg",P,t[3]||(t[3]=[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"},null,-1),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"},null,-1)])))])])]),e("div",F,[e("button",{type:"submit",class:"w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed",disabled:i.value},[i.value?(l(),s("span",J,t[6]||(t[6]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),T(" 正在登录... ",-1)]))):(l(),s("span",I,"登录"))],8,G)])],32)]),t[8]||(t[8]=e("div",{class:"text-center text-white"},[e("p",null,"© 2023 ZeroBase 管理系统. 保留所有权利.")],-1))])])}}};export{W as default};
