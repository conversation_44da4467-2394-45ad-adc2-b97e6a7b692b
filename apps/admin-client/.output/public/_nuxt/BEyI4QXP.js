import{X as H,q as L,r as d,z as J,g as U,c,o as i,b as X,a as s,h as w,t as y,D as Q,i as R,l as W,F as O,j,n as Y,A as Z}from"./qi-a-hqW.js";import{A as ee}from"./oUsj6fiR.js";import{_ as oe}from"./DlAUqK2U.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";const te={class:"ml-64 p-6"},se={key:0,class:"mb-4 p-3 bg-gray-100 rounded text-sm"},le={class:"mt-2 flex space-x-2"},ne={key:1,class:"mb-4"},re={class:"mb-6"},ae={class:"flex space-x-2"},ue=["value"],ce={key:2,class:"mb-6"},ie={class:"flex justify-between mb-4"},de={class:"text-xl font-semibold"},ve=["disabled"],pe={key:0},ge={class:"mb-4"},ye=["value","onInput","onFocus","disabled"],me={class:"mb-4"},fe=["value","onInput","onFocus"],he={class:"mb-4"},be={key:0},ke=["value","onInput","onFocus","onBlur"],_e={key:0,class:"text-red-500 text-sm mt-1"},xe={key:1},we=["value","onInput","onFocus","onBlur"],Ae={key:0,class:"text-red-500 text-sm mt-1"},Se={key:2},Ie=["value","onInput","onFocus"],$e={class:"flex justify-end"},Ee=["onClick"],Ce={key:1,class:"text-gray-500 italic"},Te={__name:"frontend-settings",setup(Ne){const{$axios:Fe}=H(),{adminToken:h,adminUser:f}=L(),A=d(!0),E=J(()=>(console.log("检查超级管理员权限:",f.value),f.value?f.value.role==="superadmin":!1)),C=d(!1),v=d(""),T=async()=>{try{const e=await $fetch("/api/admin/frontend-settings/category/general",{method:"GET",headers:{Authorization:h.value}});console.log("API 连接成功:",e),C.value=!0}catch(e){console.error("API 连接失败:",e),C.value=!1,e instanceof TypeError?v.value="网络错误：无法连接到服务器":e instanceof Error&&(v.value=e.message||"未知错误")}};U(async()=>{var e;console.log("页面加载，当前用户:",f.value),console.log("用户角色:",(e=f.value)==null?void 0:e.role),await T();try{p.value="general",await x("general"),console.group("前台设置调试信息"),console.log("已加载设置数量:",n.value.length),console.log("已加载设置详情:",JSON.stringify(n.value,null,2)),console.log("设置值:",JSON.stringify(a.value,null,2)),console.groupEnd()}catch(o){console.error("加载设置时出错:",o),g("加载设置失败: "+(o.message||"未知错误"),"error")}});const B=d(["general","contact","links","copyright","about","home"]),S={general:"通用设置",contact:"联系我们",links:"友情链接",copyright:"版权页面",about:"关于我们",home:"首页结构"},p=d("general"),n=d([]),a=d({}),m=d({}),_=d(!1),b=d(null),I=d([]),k=d(null),x=async(e=p.value)=>{e=e||"general";try{console.group(`加载 ${e} 类别设置 - 详细调试`),console.log("当前管理员Token:",h.value);const o=await $fetch(`/api/admin/frontend-settings/category/${e}`,{method:"GET",headers:{Authorization:h.value}});console.log("原始API响应:",o),console.log("响应类型:",typeof o),console.log("响应是否为数组:",Array.isArray(o));let l=[];Array.isArray(o)?l=o:o&&Array.isArray(o.settings)?l=o.settings:o&&o.data&&Array.isArray(o.data)&&(l=o.data),n.value=l,console.log("处理后的设置数组:",n.value),console.log("设置数组长度:",n.value.length),a.value=n.value.reduce((t,u)=>(t[u.key]=u.value??"",t),{}),console.log("初始化后的设置值:",a.value),n.value.length===0&&g(`${S[e]||e}类别下暂无设置`,"warning"),console.groupEnd()}catch(o){console.groupEnd(),console.error(`加载 ${e} 类别设置失败:`,o),o instanceof TypeError?(v.value="网络错误：无法获取设置",g("网络错误：无法获取设置","error")):o instanceof Error&&(v.value=o.message||"未知错误",g(v.value,"error")),n.value=[],a.value={}}},V=async()=>{if(M.value){g("请先修复JSON格式错误","error");return}console.log("开始保存设置"),_.value=!0;try{const e=n.value.map(l=>({key:l.key,value:a.value[l.key],category:p.value,description:l.description})).filter(l=>l.key);console.log("准备保存的设置:",e);const o=await $fetch(`/api/admin/frontend-settings/bulk/${p.value}`,{method:"POST",headers:{Authorization:h.value,"Content-Type":"application/json"},body:JSON.stringify({settings:e})});console.log("保存设置成功:",o),g("设置已保存","success"),await x()}catch(e){console.error("保存设置失败:",e),e instanceof TypeError?v.value="网络错误：无法保存设置":e instanceof Error&&(v.value=e.message||"未知错误"),g("保存设置失败: "+(v.value||"未知错误"),"error")}finally{_.value=!1}},D=async(e,o)=>{if(confirm("确定要删除此设置吗？")){console.log("开始删除设置:",e);try{const l=await $fetch(`/api/admin/frontend-settings/${e}`,{method:"DELETE",headers:{Authorization:h.value}});console.log("删除设置成功:",l),g("设置已删除","success");const t=[...n.value];t.splice(o,1),n.value=t,delete a.value[e]}catch(l){console.error("删除设置失败:",l),l instanceof TypeError?v.value="网络错误：无法删除设置":l instanceof Error&&(v.value=l.message||"未知错误"),g("删除设置失败: "+(v.value||"未知错误"),"error")}}},z=()=>{if(!E.value){g("仅超级管理员可以添加新设置","error");return}console.log("添加新设置");const e={key:"",value:"",category:p.value,description:""};n.value.push(e),a.value[e.key]="",setTimeout(()=>{const o=document.querySelectorAll(".mb-6.p-4.border");o.length>0&&o[o.length-1].scrollIntoView({behavior:"smooth",block:"center"})},100)},N=e=>{try{if(!a.value[e]){m.value[e]=null;return}JSON.parse(a.value[e]),m.value[e]=null}catch(o){m.value[e]=`JSON格式错误: ${o.message}`}},M=J(()=>Object.values(m.value).some(e=>e!==null)),K=e=>typeof e=="object"&&e!==null&&!Array.isArray(e),P=e=>Array.isArray(e),g=(e,o="success")=>{b.value={text:e,type:o},setTimeout(()=>{b.value=null},5e3)},F=async e=>{const o=typeof e=="string"?e:p.value;console.log("选择的类别:",o);try{await x(o)}catch(l){console.error("加载类别设置失败:",l),g("加载设置失败: "+(l.message||"未知错误"),"error")}},q=(e,o)=>{const l=o.target.value,t=[...n.value],u=t[e].key;t[e]={...t[e],key:l},u!==l&&(a.value[l]=a.value[u]||"",delete a.value[u]),n.value=t,Z(()=>{I.value[e]&&I.value[e].focus()})},G=(e,o)=>{const l=o.target.value,t=[...n.value];t[e]={...t[e],description:l},n.value=t},$=(e,o)=>{const l=o.target.value;a.value[e]=l};return(e,o)=>{var l;return i(),c("div",null,[X(ee),s("div",te,[o[8]||(o[8]=s("h1",{class:"text-2xl font-bold mb-6"},"前台设置管理",-1)),A.value?(i(),c("div",se,[s("p",null,"用户角色: "+y(((l=Q(f))==null?void 0:l.role)||"未知"),1),s("p",null,"是否超级管理员: "+y(E.value?"是":"否"),1),s("p",null,"已加载设置数: "+y(n.value.length),1),s("div",le,[s("button",{onClick:x,class:"px-2 py-1 bg-blue-500 text-white rounded text-xs"}," 手动刷新设置 "),s("button",{onClick:T,class:"px-2 py-1 bg-green-500 text-white rounded text-xs"}," 检查API连接 "),s("button",{onClick:o[0]||(o[0]=t=>A.value=!1),class:"px-2 py-1 bg-gray-500 text-white rounded text-xs"}," 隐藏调试信息 ")])])):(i(),c("div",ne,[s("button",{onClick:o[1]||(o[1]=t=>A.value=!0),class:"text-xs text-gray-500 hover:text-gray-700"}," 显示调试信息 ")])),s("div",re,[o[4]||(o[4]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"设置类别",-1)),s("div",ae,[R(s("select",{"onUpdate:modelValue":o[2]||(o[2]=t=>p.value=t),class:"w-full p-2 border border-gray-300 rounded",onChange:F},[(i(!0),c(O,null,j(B.value,t=>(i(),c("option",{key:t,value:t},y(S[t]||t),9,ue))),128))],544),[[W,p.value]]),s("button",{onClick:F,class:"bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded",title:"刷新当前类别设置"},o[3]||(o[3]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))])]),p.value?(i(),c("div",ce,[s("div",ie,[s("h2",de,y(S[p.value]||p.value),1),s("button",{onClick:V,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",disabled:_.value},y(_.value?"保存中...":"保存所有设置"),9,ve)]),n.value.length>0?(i(),c("div",pe,[(i(!0),c(O,null,j(n.value,(t,u)=>(i(),c("div",{key:t.key||u,class:"mb-6 p-4 border border-gray-200 rounded"},[s("div",ge,[o[5]||(o[5]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"设置键名",-1)),s("input",{ref_for:!0,ref_key:"settingKeyInputs",ref:I,value:t.key,onInput:r=>q(u,r),onFocus:r=>k.value=u,class:"w-full p-2 border border-gray-300 rounded",disabled:t.id,placeholder:"请输入设置键名"},null,40,ye)]),s("div",me,[o[6]||(o[6]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"设置描述",-1)),s("input",{value:t.description,onInput:r=>G(u,r),onFocus:r=>k.value=u,class:"w-full p-2 border border-gray-300 rounded",placeholder:"请输入设置描述"},null,40,fe)]),s("div",he,[o[7]||(o[7]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"设置值",-1)),K(t.value)?(i(),c("div",be,[s("textarea",{value:a.value[t.key]||"",onInput:r=>$(t.key,r),onFocus:r=>k.value=u,class:"w-full p-2 border border-gray-300 rounded font-mono h-40",onBlur:r=>N(t.key),placeholder:"请输入JSON对象"},null,40,ke),m.value[t.key]?(i(),c("p",_e,y(m.value[t.key]),1)):w("",!0)])):P(t.value)?(i(),c("div",xe,[s("textarea",{value:a.value[t.key]||"",onInput:r=>$(t.key,r),onFocus:r=>k.value=u,class:"w-full p-2 border border-gray-300 rounded font-mono h-40",onBlur:r=>N(t.key),placeholder:"请输入JSON数组"},null,40,we),m.value[t.key]?(i(),c("p",Ae,y(m.value[t.key]),1)):w("",!0)])):(i(),c("div",Se,[s("input",{value:a.value[t.key]||"",onInput:r=>$(t.key,r),onFocus:r=>k.value=u,class:"w-full p-2 border border-gray-300 rounded",placeholder:"请输入设置值"},null,40,Ie)]))]),s("div",$e,[s("button",{onClick:r=>D(t.key,u),class:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded"}," 删除 ",8,Ee)])]))),128))])):(i(),c("div",Ce,"该类别下暂无设置")),s("button",{onClick:z,class:"mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"}," 添加新设置 ")])):w("",!0),b.value?(i(),c("div",{key:3,class:Y(["p-4 rounded mb-4",b.value.type==="success"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},y(b.value.text),3)):w("",!0)])])}}},De=oe(Te,[["__scopeId","data-v-e9c0c1b0"]]);export{De as default};
