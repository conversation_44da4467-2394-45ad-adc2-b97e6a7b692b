import{e as ee,r,f as te,y as T,z as I,g as se,c as p,o as c,b as le,a as t,h as P,t as d,d as b,i as g,v as k,l as B,F,j as R,n as _,x as oe,A as ae}from"./qi-a-hqW.js";import{A as ne}from"./oUsj6fiR.js";import{a as C}from"./CxzfvvNm.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const ie={class:"flex min-h-screen bg-gray-100 overflow-x-hidden"},re={class:"flex flex-col items-center flex-1"},ue={class:"w-full max-w-6xl p-2"},de={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"},pe={class:"mb-6 text-right"},ce={class:"bg-white p-4 rounded-lg shadow mb-6"},ve={class:"flex flex-wrap gap-4"},ge={class:"flex-1 min-w-[200px]"},me={class:"w-auto"},fe={key:1,class:"w-full flex justify-center py-12"},xe={key:2,class:"bg-white rounded-lg shadow overflow-hidden"},be={class:"min-w-full divide-y divide-gray-200"},ye={class:"bg-white divide-y divide-gray-200"},we={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},he={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},_e={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ce={class:"px-6 py-4 whitespace-nowrap"},$e=["onClick"],Ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ue={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ze={class:"flex space-x-2"},Ne=["onClick"],Me=["onClick"],Se={class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},Ve={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Pe={class:"text-sm text-gray-700"},De={class:"font-medium"},je={class:"font-medium"},Be={class:"font-medium"},Le={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"分页"},Te=["disabled"],Ie=["onClick"],Fe=["disabled"],Re={key:3,class:"bg-white p-8 rounded-lg shadow text-center"},Ee={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Je={class:"bg-white rounded-lg p-6 w-full max-w-md"},Oe={class:"mb-4"},qe={class:"mb-4"},Qe={class:"mb-4"},Ge={class:"relative"},He=["type"],Ke={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},We={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},Xe={class:"mb-4"},Ye={class:"flex items-center justify-between"},Ze=["disabled"],et={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},tt={class:"bg-white rounded-lg p-6 w-full max-w-md"},st={class:"mb-4"},lt={class:"mb-4"},ot={class:"mb-4"},at={class:"flex items-center justify-between"},nt=["disabled"],it={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},rt={class:"bg-white rounded-lg p-6 w-full max-w-md"},ut={class:"mb-6"},dt={class:"flex items-center justify-between"},pt=["disabled"],yt=ee({__name:"users",setup(ct){const v=r([]),D=r(!0),$=r(null),A=r(""),U=r("all"),L=r(0),y=r(!1),h=r(!1),z=r(!1),i=r({username:"",email:"",password:"",role:"user",isActive:!0}),N=r(!1),M=r(!1),n=r({}),S=r(!1),V=r(!1),f=r(null),o=r({page:1,pageSize:10,total:0,totalPages:1}),u=te();T(v,s=>{console.log("用户列表已更新:",s)},{deep:!0}),T(h,s=>{s&&(i.value={username:"",email:"",password:"",role:"user",isActive:!0},y.value=!1)});const E=I(()=>{const s=o.value.totalPages||1,e=o.value.page||1;if(s<=5)return Array.from({length:s},(w,m)=>m+1);let a=[e],l=1;for(;a.length<5&&e-l>0;)a.unshift(e-l),l++;for(l=1;a.length<5&&e+l<=s;)a.push(e+l),l++;for(;a.length<5;)if(a[0]>1)a.unshift(a[0]-1);else if(a[a.length-1]<s)a.push(a[a.length-1]+1);else break;return a}),J=I(()=>{let s=v.value;if(A.value){const e=A.value.toLowerCase();s=s.filter(a=>a.username.toLowerCase().includes(e)||a.email.toLowerCase().includes(e))}if(U.value!=="all"){const e=U.value==="active";s=s.filter(a=>a.isActive===e)}return s});async function j(s){s<1||s>o.value.totalPages||(o.value.page=s,await x())}async function x(){D.value=!0,$.value=null;try{console.log("开始获取用户数据...");const s=await C.get("/api/admin/users",{page:o.value.page,pageSize:o.value.pageSize});console.log("接口返回原始数据:",s);const e=s.users||[];s.pagination&&(o.value={page:Number(s.pagination.page)||1,pageSize:Number(s.pagination.pageSize)||10,total:Number(s.pagination.total)||0,totalPages:Number(s.pagination.totalPages)||1}),e.length===0&&u.info("暂无用户数据");const a=e.map(l=>({...l,username:l.username||l.email,role:l.role||"user",isActive:!!l.isActive}));v.value=a,console.log("处理后的用户列表:",v.value)}catch(s){console.error("获取用户数据错误:",s),$.value=s.message||"获取用户数据时发生错误",u.error(`获取用户数据失败: ${s.message||"未知错误"}`)}finally{D.value=!1}}async function O(s){try{const e=s.username||s.email||"用户";console.log(`切换用户状态，用户: ${e}, 当前状态: ${s.isActive} (${typeof s.isActive})`);const l=!!!s.isActive,w=v.value.findIndex(m=>m.id===s.id);if(w!==-1){const m={...v.value[w],isActive:l};v.value.splice(w,1,m),L.value++,await ae()}u.info(`正在切换用户 "${e}" 的状态...`),console.log("发送更新请求，状态值:",l),await C.put(`/api/admin/users/${s.id}`,{isActive:l}),u.success(`已${l?"激活":"停用"}用户 "${e}"`),await x()}catch(e){console.error("切换用户状态错误:",e),u.error(`切换用户状态失败: ${e.message||"未知错误"}`),await x()}}async function q(){if(!i.value.username||!i.value.email||!i.value.password){u.error("请填写所有必填字段");return}z.value=!0;try{const s=i.value.username||i.value.email||"新用户";u.info(`正在创建新用户 "${s}"...`),console.log("创建用户，角色值:",i.value.role),await C.post("/api/admin/users",i.value),i.value={username:"",email:"",password:"",role:"user",isActive:!0},h.value=!1,y.value=!1,u.success(`用户 "${s}" 创建成功`),o.value.page=1,await x()}catch(s){console.error("创建用户错误:",s),u.error(`创建用户失败: ${s.message||"未知错误"}`)}finally{z.value=!1}}function Q(s){console.log("打开编辑模态框，用户数据:",s),n.value=JSON.parse(JSON.stringify(s)),console.log("设置编辑用户数据:",n.value),N.value=!0}async function G(){if(!n.value.username||!n.value.email){u.error("请填写所有必填字段");return}M.value=!0;try{console.log("更新前角色:",n.value.role),u.info(`正在更新用户 "${n.value.username}" 的信息...`);const s=n.value.role;await C.put(`/api/admin/users/${n.value.id}`,{username:n.value.username,email:n.value.email,role:s,isActive:n.value.isActive,...n.value.password?{password:n.value.password}:{}}),N.value=!1,u.success(`用户 "${n.value.username}" 更新成功`),await x()}catch(s){console.error("更新用户错误:",s),u.error(`更新用户失败: ${s.message||"未知错误"}`)}finally{M.value=!1}}function H(s){f.value=s,S.value=!0}async function K(){if(f.value){V.value=!0;try{u.info(`正在删除用户 "${f.value.username}"...`),await C.delete(`/api/admin/users/${f.value.id}`),S.value=!1,u.success(`用户 "${f.value.username}" 删除成功`),v.value.length===1&&o.value.page>1&&(o.value.page=o.value.page-1),await x()}catch(s){console.error("删除用户错误:",s),u.error(`删除用户失败: ${s.message||"未知错误"}`)}finally{V.value=!1,f.value=null}}}function W(s){if(!s)return"未知";const e=new Date(s);return isNaN(e.getTime())?s:new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).format(e)}function X(s){if(console.log("格式化角色:",s),!s)return"student";const e=s.toLowerCase();return e==="admin"?"admin":e==="superadmin"?"superadmin":e==="user"?"student":s}function Y(s){if(!s)return"bg-blue-100 text-blue-800";const e=s.toLowerCase();return e==="admin"?"bg-purple-100 text-purple-800":e==="superadmin"?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}function Z(){y.value=!y.value}return se(()=>{i.value={username:"",email:"",password:"",role:"user",isActive:!0},x()}),(s,e)=>{var a;return c(),p("div",ie,[le(ne),t("div",re,[t("div",ue,[e[26]||(e[26]=t("h1",{class:"text-2xl font-bold mb-8 text-center"},"用户管理",-1)),$.value?(c(),p("div",de,[t("p",null,d($.value),1)])):P("",!0),t("div",pe,[t("button",{onClick:e[0]||(e[0]=l=>h.value=!0),class:"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 ml-auto"},e[15]||(e[15]=[t("span",{class:"mr-2"},"+",-1),b(" 创建新用户 ",-1)]))]),t("div",ce,[t("div",ve,[t("div",ge,[g(t("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>A.value=l),type:"text",placeholder:"搜索用户...",class:"w-full px-4 py-2 border rounded-lg"},null,512),[[k,A.value]])]),t("div",me,[g(t("select",{"onUpdate:modelValue":e[2]||(e[2]=l=>U.value=l),class:"w-full px-4 py-2 border rounded-lg"},e[16]||(e[16]=[t("option",{value:"all"},"所有状态",-1),t("option",{value:"active"},"激活",-1),t("option",{value:"inactive"},"未激活",-1)]),512),[[B,U.value]])])])]),D.value?(c(),p("div",fe,e[17]||(e[17]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"},null,-1)]))):v.value&&v.value.length>0?(c(),p("div",xe,[t("table",be,[e[18]||(e[18]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," ID "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 用户名 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 邮箱 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 角色 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 状态 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 创建时间 "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 操作 ")])],-1)),t("tbody",ye,[(c(!0),p(F,null,R(J.value,(l,w)=>(c(),p("tr",{key:`user-${l.id}-${L.value}`,class:"hover:bg-gray-50"},[t("td",we,d(l.id.slice(0,8))+"... ",1),t("td",he,d(l.username),1),t("td",ke,d(l.email),1),t("td",_e,[t("span",{class:_(["px-2 py-1 text-xs rounded-full",Y(l.role)])},d(X(l.role)),3)]),t("td",Ce,[t("button",{onClick:m=>O(l),class:_(["px-3 py-1 rounded-full text-xs font-semibold",l.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},d(l.isActive?"激活":"未激活"),11,$e)]),t("td",Ae,d(W(l.createdAt)),1),t("td",Ue,[t("div",ze,[t("button",{onClick:m=>Q(l),class:"text-indigo-600 hover:text-indigo-900 mr-2"}," 编辑 ",8,Ne),t("button",{onClick:m=>H(l),class:"text-red-600 hover:text-red-900"}," 删除 ",8,Me)])])]))),128))])]),t("div",Se,[t("div",Ve,[t("div",null,[t("p",Pe,[e[19]||(e[19]=b(" 显示 ",-1)),t("span",De,d((o.value.page-1)*o.value.pageSize+1),1),e[20]||(e[20]=b(" 至 ",-1)),t("span",je,d(Math.min(o.value.page*o.value.pageSize,o.value.total)),1),e[21]||(e[21]=b(" 条，共 ",-1)),t("span",Be,d(o.value.total),1),e[22]||(e[22]=b(" 条 ",-1))])]),t("div",null,[t("nav",Le,[t("button",{onClick:e[3]||(e[3]=l=>j(o.value.page-1)),disabled:o.value.page<=1,class:_(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",o.value.page<=1?"opacity-50 cursor-not-allowed":""])},e[23]||(e[23]=[t("span",{class:"sr-only"},"上一页",-1),b(" « ",-1)]),10,Te),(c(!0),p(F,null,R(E.value,l=>(c(),p("span",{key:l,onClick:w=>j(l),class:_(["relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium cursor-pointer",l===o.value.page?"bg-blue-50 text-blue-600 z-10 border-blue-500":"text-gray-500 hover:bg-gray-50"])},d(l),11,Ie))),128)),t("button",{onClick:e[4]||(e[4]=l=>j(o.value.page+1)),disabled:o.value.page>=o.value.totalPages,class:_(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",o.value.page>=o.value.totalPages?"opacity-50 cursor-not-allowed":""])},e[24]||(e[24]=[t("span",{class:"sr-only"},"下一页",-1),b(" » ",-1)]),10,Fe)])])])])])):(c(),p("div",Re,e[25]||(e[25]=[t("p",{class:"text-gray-500"},"暂无用户数据",-1)])))])]),h.value?(c(),p("div",Ee,[t("div",Je,[e[34]||(e[34]=t("h2",{class:"text-xl font-bold mb-4"},"创建新用户",-1)),t("div",Oe,[e[27]||(e[27]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"name"}," 用户名 ",-1)),g(t("input",{"onUpdate:modelValue":e[5]||(e[5]=l=>i.value.username=l),id:"name",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"请输入用户名"},null,512),[[k,i.value.username]])]),t("div",qe,[e[28]||(e[28]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"email"}," 邮箱 ",-1)),g(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>i.value.email=l),id:"email",type:"email",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"请输入邮箱",autocomplete:"off"},null,512),[[k,i.value.email]])]),t("div",Qe,[e[31]||(e[31]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"password"}," 密码 ",-1)),t("div",Ge,[g(t("input",{"onUpdate:modelValue":e[7]||(e[7]=l=>i.value.password=l),id:"password",type:y.value?"text":"password",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline pr-10",placeholder:"请输入密码",autocomplete:"new-password"},null,8,He),[[oe,i.value.password]]),t("button",{type:"button",onClick:Z,class:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none","aria-label":"切换密码可见性"},[y.value?(c(),p("svg",We,e[30]||(e[30]=[t("path",{"fill-rule":"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.781-1.781zm4.261 4.262l1.514 1.514a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z","clip-rule":"evenodd"},null,-1),t("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.742L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.064 7 9.542 7 .847 0 1.67-.105 2.454-.303z"},null,-1)]))):(c(),p("svg",Ke,e[29]||(e[29]=[t("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"},null,-1),t("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"},null,-1)])))])])]),t("div",Xe,[e[33]||(e[33]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"role"}," 角色 ",-1)),g(t("select",{"onUpdate:modelValue":e[8]||(e[8]=l=>i.value.role=l),id:"role",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},e[32]||(e[32]=[t("option",{value:"user"},"学生",-1),t("option",{value:"ADMIN"},"管理员",-1),t("option",{value:"superadmin"},"超级管理员",-1)]),512),[[B,i.value.role]])]),t("div",Ye,[t("button",{onClick:q,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:z.value},d(z.value?"创建中...":"创建"),9,Ze),t("button",{onClick:e[9]||(e[9]=l=>h.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):P("",!0),N.value?(c(),p("div",et,[t("div",tt,[e[39]||(e[39]=t("h2",{class:"text-xl font-bold mb-4"},"编辑用户",-1)),t("div",st,[e[35]||(e[35]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-name"}," 用户名 ",-1)),g(t("input",{"onUpdate:modelValue":e[10]||(e[10]=l=>n.value.username=l),id:"edit-name",type:"text",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[k,n.value.username]])]),t("div",lt,[e[36]||(e[36]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-email"}," 邮箱 ",-1)),g(t("input",{"onUpdate:modelValue":e[11]||(e[11]=l=>n.value.email=l),id:"edit-email",type:"email",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},null,512),[[k,n.value.email]])]),t("div",ot,[e[38]||(e[38]=t("label",{class:"block text-gray-700 text-sm font-bold mb-2",for:"edit-role"}," 角色 ",-1)),g(t("select",{"onUpdate:modelValue":e[12]||(e[12]=l=>n.value.role=l),id:"edit-role",class:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},e[37]||(e[37]=[t("option",{value:"user"},"学生",-1),t("option",{value:"ADMIN"},"管理员",-1),t("option",{value:"superadmin"},"超级管理员",-1)]),512),[[B,n.value.role]])]),t("div",at,[t("button",{onClick:G,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:M.value},d(M.value?"更新中...":"更新"),9,nt),t("button",{onClick:e[13]||(e[13]=l=>N.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):P("",!0),S.value?(c(),p("div",it,[t("div",rt,[e[40]||(e[40]=t("h2",{class:"text-xl font-bold mb-4"},"确认删除",-1)),t("p",ut,'确定要删除用户 "'+d((a=f.value)==null?void 0:a.username)+'" 吗？此操作无法撤销。',1),t("div",dt,[t("button",{onClick:K,class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:V.value},d(V.value?"删除中...":"确认删除"),9,pt),t("button",{onClick:e[14]||(e[14]=l=>S.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"}," 取消 ")])])])):P("",!0)])}}});export{yt as default};
