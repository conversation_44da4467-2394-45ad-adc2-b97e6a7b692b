import{p as N,f as U,r as u,C as j,P as $,c as r,o as a,b as x,a as t,h,D as q,s as B,i as p,v as g,l as D,N as I,F as L,j as M,t as w,d as A}from"./qi-a-hqW.js";import{u as P}from"./tJcwIiS2.js";import{A as R}from"./oUsj6fiR.js";import{Q as F}from"./47Na0wgx.js";import"./CxzfvvNm.js";import"./DqjI2rGC.js";import"./eAbK7LvP.js";import"./DlAUqK2U.js";const O={class:"flex"},Q={class:"flex-1 p-6"},z={class:"flex justify-between items-center mb-6"},H={class:"bg-white shadow-md rounded p-6"},W={class:"mb-4"},X={class:"mb-4"},G={class:"mb-4"},J={class:"flex flex-wrap gap-2"},K=["onUpdate:modelValue"],Y=["onClick"],Z={class:"mb-6"},ee={class:"flex justify-end"},te=["disabled"],oe={key:0},se={key:1},le={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4"},ne={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},re={class:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full"},ae={class:"mb-2"},ie={class:"mb-4"},de={class:"flex justify-end space-x-3"},xe={__name:"new",setup(ue){const v=N(),k=P(),c=U(),b=u(!1),i=u(""),o=j({title:"",type:"",body:"",categories:[],annotations:[]}),f=u(!1),_=u(""),d=u("");let n=null;const C=s=>{n=s.quill,o.body=s.html},S=()=>{if(!d.value.trim()||!n){c.error("请输入音标");return}const s=n.getSelection();if(!s)return;const e=n.getText(s.index,s.length);o.annotations.push({word:e,pronunciation:d.value.trim()}),n.formatText(s.index,s.length,{color:"#ef4444",background:"#f0f9ff"}),f.value=!1,c.success(`已为"${e}"添加音标: ${d.value.trim()}`)},E=()=>{o.categories.push("")},V=s=>{o.categories.splice(s,1)},T=async()=>{o.categories=o.categories.filter(s=>s.trim()!==""),n&&(o.body=n.root.innerHTML),b.value=!0,i.value="";try{const s=await k.createContent(o);c.success("内容创建成功"),v.push("/admin/content")}catch(s){i.value=s.message||"创建内容失败",c.error(i.value)}finally{b.value=!1}};return $(()=>{n&&(n=null)}),(s,e)=>(a(),r("div",O,[x(R),t("div",Q,[t("div",z,[e[6]||(e[6]=t("h1",{class:"text-2xl font-bold"},"创建内容",-1)),t("button",{onClick:e[0]||(e[0]=l=>q(v).push("/admin/content")),class:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"}," 返回列表 ")]),t("div",H,[t("form",{onSubmit:B(T,["prevent"])},[t("div",W,[e[7]||(e[7]=t("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-1"},"标题",-1)),p(t("input",{id:"title","onUpdate:modelValue":e[1]||(e[1]=l=>o.title=l),type:"text",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"输入内容标题",required:""},null,512),[[g,o.title]])]),t("div",X,[e[9]||(e[9]=t("label",{for:"type",class:"block text-sm font-medium text-gray-700 mb-1"},"内容类型",-1)),p(t("select",{id:"type","onUpdate:modelValue":e[2]||(e[2]=l=>o.type=l),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",required:""},e[8]||(e[8]=[I('<option value="">请选择类型</option><option value="ARTICLE">文章</option><option value="LESSON">课程</option><option value="EXERCISE">练习</option><option value="NOTE">笔记</option>',5)]),512),[[D,o.type]])]),t("div",G,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"栏目",-1)),t("div",J,[(a(!0),r(L,null,M(o.categories,(l,m)=>(a(),r("div",{key:m,class:"flex items-center"},[p(t("input",{"onUpdate:modelValue":y=>o.categories[m]=y,type:"text",class:"border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 mr-2",placeholder:"栏目名称"},null,8,K),[[g,o.categories[m]]]),t("button",{type:"button",onClick:y=>V(m),class:"text-red-500 hover:text-red-700"}," 删除 ",8,Y)]))),128)),t("button",{type:"button",onClick:E,class:"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"}," 添加栏目 ")])]),t("div",Z,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"内容正文",-1)),x(F,{content:o.body,"onUpdate:content":e[3]||(e[3]=l=>o.body=l),placeholder:"在此输入内容...",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",onEditorChange:C},null,8,["content"])]),t("div",ee,[t("button",{type:"submit",class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded",disabled:b.value},[b.value?(a(),r("span",oe,"保存中...")):(a(),r("span",se,"保存内容"))],8,te)])],32)]),i.value?(a(),r("div",le,w(i.value),1)):h("",!0),f.value?(a(),r("div",ne,[t("div",re,[e[14]||(e[14]=t("h3",{class:"text-xl font-bold mb-4"},"添加音标标注",-1)),t("p",ae,[e[12]||(e[12]=A("选中单词: ",-1)),t("strong",null,w(_.value),1)]),t("div",ie,[e[13]||(e[13]=t("label",{for:"pronunciation",class:"block text-sm font-medium text-gray-700 mb-1"},"音标",-1)),p(t("input",{id:"pronunciation","onUpdate:modelValue":e[4]||(e[4]=l=>d.value=l),type:"text",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"输入音标，如 /həˈləʊ/"},null,512),[[g,d.value]])]),t("div",de,[t("button",{onClick:e[5]||(e[5]=l=>f.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"}," 取消 "),t("button",{onClick:S,class:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"}," 确认 ")])])])):h("",!0)])]))}};export{xe as default};
