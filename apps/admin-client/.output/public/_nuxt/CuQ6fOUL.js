import{e as U,k as V,r as u,g as F,c as n,o as i,a as s,h as w,n as k,t as l,F as p,j as _,i as S,l as G,m as T}from"./qi-a-hqW.js";import{a as f}from"./CxzfvvNm.js";const z={class:"p-6"},I={class:"card bg-base-100 shadow-xl mb-6"},j={class:"card-body"},D={class:"flex items-center space-x-4"},N={class:"text-sm text-gray-600"},P={key:0,class:"mt-4"},W={class:"grid grid-cols-2 gap-2"},R={class:"badge badge-outline"},q={class:"text-sm"},H={class:"card bg-base-100 shadow-xl mb-6"},J={class:"card-body"},K={class:"form-control mb-4"},O=["value"],Q={class:"form-control mb-4"},X={class:"flex flex-wrap gap-2"},Y=["value"],Z={class:"label-text ml-2"},$={class:"form-control mb-4"},ss={class:"cursor-pointer label"},es={class:"card-actions"},ts=["disabled"],as={key:0,class:"card bg-base-100 shadow-xl mb-6"},ls={class:"card-body"},os={class:"stats stats-vertical lg:stats-horizontal shadow"},ns={class:"stat"},is={class:"stat-value"},ds={class:"stat"},cs={class:"stat-value text-success"},rs={class:"stat"},us={class:"stat-value text-error"},vs={key:0,class:"mt-4"},gs={class:"overflow-x-auto"},bs={class:"table table-zebra"},ms={class:"max-w-xs truncate"},hs={class:"text-sm text-error"},ps={class:"card bg-base-100 shadow-xl"},_s={class:"card-body"},fs={class:"stats stats-vertical lg:stats-horizontal shadow"},xs={class:"stat"},ys={class:"stat-value"},ws={class:"stat"},ks={class:"stat-value text-warning"},Ss={class:"stat"},Ls={class:"stat-value text-error"},Cs={class:"stat"},As={class:"stat-value text-info"},Es=U({__name:"audio",setup(Ts){const o=V(),c=u({available:!1,message:"检查中...",voiceConfigs:{}}),m=u([]),v=u(""),L=["en-US-Male","en-US-Female","en-GB-Male","en-GB-Female"],g=u([...L]),x=u(!1),b=u(!1),d=u(null),r=u({completed:0,generating:0,failed:0,pending:0}),M=t=>({"en-US-Male":"美式男声","en-US-Female":"美式女声","en-GB-Male":"英式男声","en-GB-Female":"英式女声"})[t]||t,C=async()=>{o.showLoading({title:"检查服务状态",message:"正在检查Edge TTS服务状态",key:"check-service"});try{const t=await f.get("/api/admin/audio/status");t&&t.data?c.value=t.data:c.value=t||{available:!1,message:"响应格式异常"}}catch(t){console.error("检查服务状态失败:",t),c.value={available:!1,message:"检查服务状态失败",voiceConfigs:{}}}finally{o.hideLoading("check-service")}},B=async()=>{o.showLoading({title:"加载课程列表",message:"正在获取课程列表，请稍候",key:"fetch-courses"});try{const t=await f.get("/api/admin/courses/tree");t&&Array.isArray(t)?m.value=t.flatMap(e=>e.courses||[]):(console.warn("课程列表响应格式异常:",t),m.value=[])}catch(t){console.error("获取课程列表失败:",t),m.value=[]}finally{o.hideLoading("fetch-courses")}},E=async()=>{if(!(!v.value||g.value.length===0)){b.value=!0,d.value=null,o.showLoading({title:"批量音频生成中",message:"正在批量生成课程音频，请稍候",showProgress:!0,key:"batch-generate"});try{const t=await f.post("/api/admin/audio/course/generate",{courseId:v.value,voiceTypes:g.value,regenerate:x.value});d.value=t,await y()}catch(t){console.error("批量生成音频失败:",t),alert("批量生成音频失败，请重试")}finally{o.hideLoading("batch-generate"),b.value=!1}}},y=async()=>{o.showLoading({title:"刷新统计中",message:"正在获取音频统计信息，请稍候",key:"refresh-stats"});try{const t=await f.get("/api/admin/audio/reuse-stats");if(console.log("🔍 API响应:",t),t&&t.stats){const e=t.stats;console.log("📊 复用统计数据:",e),r.value={completed:e.statementsWithAudio||0,generating:0,failed:0,pending:Math.max(0,(e.totalStatements||0)-(e.statementsWithAudio||0))},console.log("📊 音频统计已更新:",r.value)}else console.warn("音频统计响应格式异常:",t),r.value={completed:0,generating:0,failed:0,pending:0}}catch(t){console.error("获取音频统计失败:",t),r.value={completed:0,generating:0,failed:0,pending:0}}finally{o.hideLoading("refresh-stats")}},A=()=>{console.log("🧹 清理Audio页面的加载状态"),o.clearAllLoading(),setTimeout(()=>{o.isLoading.value&&(console.warn("⚠️ 检测到残留的loading状态，强制清理"),o.clearAllLoading())},100)};return F(async()=>{console.log("🚀 Audio页面开始初始化"),A();try{console.log("1️⃣ 检查Edge TTS服务状态"),await C(),console.log("✅ Edge TTS服务状态检查完成"),console.log("2️⃣ 获取课程列表"),await B(),console.log("✅ 课程列表获取完成"),console.log("3️⃣ 刷新音频统计"),await y(),console.log("✅ 音频统计刷新完成"),console.log("🎉 Audio页面初始化完成")}catch(t){console.error("❌ Audio页面初始化失败:",t),A()}}),(t,e)=>(i(),n("div",z,[s("div",{class:"flex justify-between items-center mb-6"},[e[3]||(e[3]=s("h1",{class:"text-2xl font-bold"},"音频管理",-1)),s("button",{onClick:C,class:"btn btn-outline"},"检查服务状态")]),s("div",I,[s("div",j,[e[5]||(e[5]=s("h2",{class:"card-title"},"Edge TTS 服务状态",-1)),s("div",D,[s("div",{class:k(["badge",c.value.available?"badge-success":"badge-error"])},l(c.value.available?"可用":"不可用"),3),s("span",N,l(c.value.message),1)]),c.value.available?(i(),n("div",P,[e[4]||(e[4]=s("h3",{class:"font-semibold mb-2"},"支持的语音类型：",-1)),s("div",W,[(i(!0),n(p,null,_(c.value.voiceConfigs,(a,h)=>(i(),n("div",{key:h,class:"flex items-center space-x-2"},[s("span",R,l(h),1),s("span",q,l(a.name),1)]))),128))])])):w("",!0)])]),s("div",H,[s("div",J,[e[10]||(e[10]=s("h2",{class:"card-title"},"批量操作",-1)),s("div",K,[e[7]||(e[7]=s("label",{class:"label"},[s("span",{class:"label-text"},"选择课程")],-1)),S(s("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>v.value=a),class:"select select-bordered"},[e[6]||(e[6]=s("option",{value:""},"请选择课程",-1)),(i(!0),n(p,null,_(m.value,a=>(i(),n("option",{key:a.id,value:a.id},l(a.title),9,O))),128))],512),[[G,v.value]])]),s("div",Q,[e[8]||(e[8]=s("label",{class:"label"},[s("span",{class:"label-text"},"语音类型")],-1)),s("div",X,[(i(),n(p,null,_(L,a=>s("label",{key:a,class:"cursor-pointer label"},[S(s("input",{type:"checkbox",value:a,"onUpdate:modelValue":e[1]||(e[1]=h=>g.value=h),class:"checkbox checkbox-primary"},null,8,Y),[[T,g.value]]),s("span",Z,l(M(a)),1)])),64))])]),s("div",$,[s("label",ss,[e[9]||(e[9]=s("span",{class:"label-text"},"重新生成已存在的音频",-1)),S(s("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=a=>x.value=a),class:"checkbox checkbox-primary"},null,512),[[T,x.value]])])]),s("div",es,[s("button",{onClick:E,disabled:!v.value||g.value.length===0||b.value,class:k(["btn btn-primary",{loading:b.value}])},l(b.value?"生成中...":"批量生成音频"),11,ts)])])]),d.value?(i(),n("div",as,[s("div",ls,[e[16]||(e[16]=s("h2",{class:"card-title"},"生成结果",-1)),s("div",os,[s("div",ns,[e[11]||(e[11]=s("div",{class:"stat-title"},"总语句数",-1)),s("div",is,l(d.value.totalStatements),1)]),s("div",ds,[e[12]||(e[12]=s("div",{class:"stat-title"},"成功",-1)),s("div",cs,l(d.value.successCount),1)]),s("div",rs,[e[13]||(e[13]=s("div",{class:"stat-title"},"失败",-1)),s("div",us,l(d.value.failCount),1)])]),d.value.results&&d.value.results.length>0?(i(),n("div",vs,[e[15]||(e[15]=s("h3",{class:"font-semibold mb-2"},"详细结果：",-1)),s("div",gs,[s("table",bs,[e[14]||(e[14]=s("thead",null,[s("tr",null,[s("th",null,"语句"),s("th",null,"状态"),s("th",null,"错误信息")])],-1)),s("tbody",null,[(i(!0),n(p,null,_(d.value.results,a=>(i(),n("tr",{key:a.statementId},[s("td",ms,l(a.english),1),s("td",null,[s("div",{class:k(["badge",a.success?"badge-success":"badge-error"])},l(a.success?"成功":"失败"),3)]),s("td",hs,l(a.error||"-"),1)]))),128))])])])])):w("",!0)])])):w("",!0),s("div",ps,[s("div",_s,[e[21]||(e[21]=s("h2",{class:"card-title"},"音频统计",-1)),s("div",fs,[s("div",xs,[e[17]||(e[17]=s("div",{class:"stat-title"},"已生成音频的语句",-1)),s("div",ys,l(r.value.completed),1)]),s("div",ws,[e[18]||(e[18]=s("div",{class:"stat-title"},"生成中",-1)),s("div",ks,l(r.value.generating),1)]),s("div",Ss,[e[19]||(e[19]=s("div",{class:"stat-title"},"生成失败",-1)),s("div",Ls,l(r.value.failed),1)]),s("div",Cs,[e[20]||(e[20]=s("div",{class:"stat-title"},"未生成",-1)),s("div",As,l(r.value.pending),1)])]),s("div",{class:"card-actions mt-4"},[s("button",{onClick:y,class:"btn btn-outline"},"刷新统计")])])])]))}});export{Es as default};
