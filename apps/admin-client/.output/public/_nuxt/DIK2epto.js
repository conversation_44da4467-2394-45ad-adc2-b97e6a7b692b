import{e as _,p as N,B as T,z as V,C,r as m,g as j,c as l,o as d,a as t,h as f,D as b,d as v,t as y,i as n,v as g,m as k,s as A}from"./qi-a-hqW.js";const $={class:"mb-6 flex items-center"},D={key:0},R={key:1,class:"rounded-md bg-red-50 p-4 dark:bg-red-900/30"},S={class:"flex"},B={class:"ml-3"},L={class:"text-sm font-medium text-red-800 dark:text-red-200"},M={key:2,class:"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800"},E={class:"px-6 py-5"},P={class:"mb-8"},q={class:"grid grid-cols-1 gap-6 md:grid-cols-2"},z={class:"mb-8"},H={class:"flex items-center space-x-6"},O={class:"h-20 w-20 flex-shrink-0 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"},J=["src"],F={class:"mb-8"},G={class:"flex items-center space-x-6"},I={class:"flex items-center"},K={class:"flex items-center"},Q={class:"border-t border-gray-200 px-6 py-4 dark:border-gray-700"},W={class:"flex justify-end space-x-3"},X=["disabled"],Y={key:0,class:"i-ph-spinner animate-spin mr-2 h-4 w-4"},Z={key:3,class:"fixed bottom-4 right-4 z-50 flex items-center rounded-md bg-green-100 p-4 shadow-lg dark:bg-green-900/30"},se=_({__name:"edit",setup(ee){const c=N(),h=T(),x=V(()=>h.params.id),s=C({username:"",email:"",avatar:"",isActive:!0,isNewUser:!1}),u=m(!0),o=m(!1),i=m(""),p=m(!1);async function w(){u.value=!0,i.value="";try{const r=await fetch(`/api/admin/users/${x.value}`);if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const e=await r.json();Object.assign(s,{username:e.username,email:e.email,avatar:e.avatar,isActive:e.isActive,isNewUser:e.isNewUser}),u.value=!1}catch(r){console.error("获取用户信息失败:",r),i.value="获取用户信息失败，请稍后重试",u.value=!1}}async function U(){o.value=!0;try{const r=await fetch(`/api/admin/users/${x.value}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);p.value=!0,setTimeout(()=>{p.value=!1},3e3),o.value=!1}catch(r){console.error("保存用户信息失败:",r),i.value="保存用户信息失败，请稍后重试",o.value=!1}}return j(()=>{w()}),(r,e)=>(d(),l("div",null,[t("div",$,[t("button",{onClick:e[0]||(e[0]=a=>b(c).back()),class:"mr-3 inline-flex items-center text-sm font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"},e[7]||(e[7]=[t("span",{class:"i-ph-arrow-left-bold mr-1 h-5 w-5"},null,-1),v(" 返回 ",-1)])),e[8]||(e[8]=t("h1",{class:"text-2xl font-bold"},"编辑用户",-1))]),u.value?(d(),l("div",D,e[9]||(e[9]=[t("div",{class:"flex h-64 items-center justify-center"},[t("span",{class:"i-ph-spinner animate-spin h-10 w-10 text-purple-600"})],-1)]))):i.value?(d(),l("div",R,[t("div",S,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("span",{class:"i-ph-warning-circle h-5 w-5 text-red-400","aria-hidden":"true"})],-1)),t("div",B,[t("h3",L,y(i.value),1)])])])):(d(),l("div",M,[t("form",{onSubmit:A(U,["prevent"])},[t("div",E,[t("div",P,[e[13]||(e[13]=t("h2",{class:"mb-4 text-lg font-medium text-gray-900 dark:text-white"},"基本信息",-1)),t("div",q,[t("div",null,[e[11]||(e[11]=t("label",{for:"username",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," 用户名 ",-1)),n(t("input",{id:"username","onUpdate:modelValue":e[1]||(e[1]=a=>s.username=a),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm",required:""},null,512),[[g,s.username]])]),t("div",null,[e[12]||(e[12]=t("label",{for:"email",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," 邮箱 ",-1)),n(t("input",{id:"email","onUpdate:modelValue":e[2]||(e[2]=a=>s.email=a),type:"email",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm",required:""},null,512),[[g,s.email]])])])]),t("div",z,[e[16]||(e[16]=t("h2",{class:"mb-4 text-lg font-medium text-gray-900 dark:text-white"},"头像",-1)),t("div",H,[t("div",O,[t("img",{src:s.avatar||"https://api.dicebear.com/7.x/bottts/svg?seed=placeholder",alt:"用户头像",class:"h-full w-full object-cover"},null,8,J)]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," 头像URL ",-1)),n(t("input",{"onUpdate:modelValue":e[3]||(e[3]=a=>s.avatar=a),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"输入头像URL"},null,512),[[g,s.avatar]]),e[15]||(e[15]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," 输入有效的图片URL或使用默认头像 ",-1))])])]),t("div",F,[e[19]||(e[19]=t("h2",{class:"mb-4 text-lg font-medium text-gray-900 dark:text-white"},"账户状态",-1)),t("div",G,[t("div",I,[n(t("input",{id:"active","onUpdate:modelValue":e[4]||(e[4]=a=>s.isActive=a),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700"},null,512),[[k,s.isActive]]),e[17]||(e[17]=t("label",{for:"active",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," 账户激活 ",-1))]),t("div",K,[n(t("input",{id:"newUser","onUpdate:modelValue":e[5]||(e[5]=a=>s.isNewUser=a),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700"},null,512),[[k,s.isNewUser]]),e[18]||(e[18]=t("label",{for:"newUser",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," 新用户 ",-1))])])])]),t("div",Q,[t("div",W,[t("button",{type:"button",onClick:e[6]||(e[6]=a=>b(c).back()),class:"inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"}," 取消 "),t("button",{type:"submit",disabled:o.value,class:"inline-flex items-center rounded-md border border-transparent bg-purple-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 dark:bg-purple-700 dark:hover:bg-purple-800"},[o.value?(d(),l("span",Y)):f("",!0),v(" "+y(o.value?"保存中...":"保存"),1)],8,X)])])],32)])),p.value?(d(),l("div",Z,e[20]||(e[20]=[t("span",{class:"i-ph-check-circle-bold mr-2 h-5 w-5 text-green-600 dark:text-green-400"},null,-1),t("span",{class:"text-sm font-medium text-green-800 dark:text-green-200"},"保存成功",-1)]))):f("",!0)]))}});export{se as default};
