import{p as l,c as n,o as a,a as t}from"./qi-a-hqW.js";const i={class:"flex items-center justify-center min-h-screen"},x={__name:"unauthorized",setup(c){const s=l(),o=()=>{s.push("/admin/login")},r=()=>{s.push("/")};return(d,e)=>(a(),n("div",i,[t("div",{class:"bg-white p-8 rounded-lg shadow-md max-w-md w-full"},[t("div",{class:"text-center"},[e[0]||(e[0]=t("h1",{class:"text-red-600 text-4xl font-bold mb-4"},"未授权访问",-1)),e[1]||(e[1]=t("div",{class:"text-red-500 text-6xl mb-6"},[t("i",{class:"fas fa-exclamation-circle"})],-1)),e[2]||(e[2]=t("p",{class:"text-gray-700 mb-6"}," 您没有权限访问请求的页面。请确保您已登录并具有适当的权限。 ",-1)),t("div",{class:"flex flex-col space-y-3"},[t("button",{onClick:o,class:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"}," 返回登录页 "),t("button",{onClick:r,class:"bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition-colors"}," 返回首页 ")])])])]))}};export{x as default};
