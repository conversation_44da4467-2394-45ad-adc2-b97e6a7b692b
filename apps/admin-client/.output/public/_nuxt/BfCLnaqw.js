import{G as W,r as f,H as N,e as M,g as O,y as R,c as n,o as u,a as r,h as y,i as L,l as _,F as S,j as A,t as $,p as H,b as J}from"./qi-a-hqW.js";const m={success:a=>console.log(`[SUCCESS] ${a}`),error:a=>console.log(`[ERROR] ${a}`),warning:a=>console.log(`[WARNING] ${a}`),info:a=>console.log(`[INFO] ${a}`)},q=W("coursePack",{state:()=>({coursePacks:[],currentCoursePack:null,loading:!1,error:null}),actions:{async fetchCoursePacks(){this.loading=!0;try{this.coursePacks=[],this.error=null}catch(a){this.error=a instanceof Error?a.message:"Unknown error"}finally{this.loading=!1}},async createCoursePack(a){this.loading=!0;try{const t={id:Date.now().toString(),name:a.name||"",description:a.description,coverImage:a.coverImage,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return this.coursePacks.push(t),t}catch(t){throw this.error=t instanceof Error?t.message:"Unknown error",t}finally{this.loading=!1}},async updateCoursePack(a,t){this.loading=!0;try{const l=this.coursePacks.findIndex(i=>i.id===a);l!==-1&&(this.coursePacks[l]={...this.coursePacks[l],...t,updatedAt:new Date().toISOString()})}catch(l){throw this.error=l instanceof Error?l.message:"Unknown error",l}finally{this.loading=!1}},async deleteCoursePack(a){this.loading=!0;try{this.coursePacks=this.coursePacks.filter(t=>t.id!==a)}catch(t){throw this.error=t instanceof Error?t.message:"Unknown error",t}finally{this.loading=!1}},setCurrentCoursePack(a){this.currentCoursePack=a}},getters:{getCoursePackById:a=>t=>a.coursePacks.find(l=>l.id===t)}}),K=()=>{const a=f(!1),t=f(null),l=async d=>{a.value=!0,t.value=null;try{return new FormData().append("image",d),await new Promise(h=>setTimeout(h,1e3)),{success:!0,url:`/uploads/images/${Date.now()}-${d.name}`,filename:d.name}}catch(c){const b=c instanceof Error?c.message:"Upload failed";return t.value=b,{success:!1,error:b}}finally{a.value=!1}},i=async d=>{try{return await new Promise(c=>setTimeout(c,500)),!0}catch(c){return t.value=c instanceof Error?c.message:"Delete failed",!1}},g=d=>d.startsWith("http")?d:`/uploads/images/${d}`,p=d=>["image/jpeg","image/png","image/gif","image/webp"].includes(d.type)?d.size>5242880?{valid:!1,error:"Image size must be less than 5MB"}:{valid:!0}:{valid:!1,error:"Only JPEG, PNG, GIF, and WebP images are allowed"};return{uploading:N(a),error:N(t),uploadImage:l,deleteImage:i,getImageUrl:g,validateImage:p}},Q={class:"w-full"},X={class:"mb-8 rounded-lg border border-gray-200 p-6 dark:border-gray-700"},Y={class:"grid grid-cols-1 gap-6 md:grid-cols-2"},Z={class:"space-y-4"},ee={class:"flex h-32 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600"},re=["src"],oe={key:1,class:"text-center text-gray-500"},se={class:"flex space-x-2"},te={class:"flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"},ae={class:"space-y-4"},le={class:"flex h-32 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600"},ne=["src"],ue={key:1,class:"text-center text-gray-500"},ie={class:"flex space-x-2"},ce={class:"flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"},de={class:"mb-8 rounded-lg border border-gray-200 p-6 dark:border-gray-700"},ge={class:"mb-4 space-y-2"},ve=["value"],fe={key:0,class:"space-y-4"},me={class:"flex h-48 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600"},pe=["src"],be={key:1,class:"text-center text-gray-500"},ye={class:"flex space-x-2"},ke={class:"flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"},he={class:"rounded-lg border border-gray-200 p-6 dark:border-gray-700"},xe={class:"mb-4 grid grid-cols-1 gap-4 md:grid-cols-2"},Ce={class:"space-y-2"},we=["value"],Pe={class:"space-y-2"},Ie=["disabled"],Le=["value"],_e={key:0,class:"mt-4 space-y-4"},Se={class:"flex h-48 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600"},Ae=["src"],$e={key:1,class:"text-center text-gray-500"},De={class:"flex space-x-2"},je={class:"flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"},Ue=M({__name:"ImageManager",setup(a){const t=f(null),l=f(""),i=f(null),g=f(null),p=f(""),d=f(""),c=f(""),b=f([]),h=f([]),v=f({frontLogo:null,backLogo:null,coursePackCover:null,courseCover:null}),C=q(),k=K();O(async()=>{try{await T(),await B()}catch(s){console.error("加载数据时出错:",s),m.error("加载数据失败")}}),R(p,async s=>{s?await D(s):i.value=null}),R(c,async s=>{s?await j(s):g.value=null});async function T(){try{await C.setupCoursePacks(),b.value=C.coursePacks}catch(s){console.error("加载课程包列表失败:",s),m.error("加载课程包列表失败")}}async function B(){try{const s=await k.getImageInfo("logo");s&&s.front?t.value=s.front.url:t.value="/logo.png",l.value=""}catch(s){console.error("加载 Logo 信息失败:",s),t.value="/logo.png",l.value=""}}async function D(s){try{const e=await k.getImageInfo("coursePack",s);if(e&&e.url){i.value=e.url;return}const o=b.value.find(x=>x.id===s);o&&o.cover?i.value=o.cover:i.value=null}catch(e){console.error("加载课程包封面失败:",e),i.value=null}}async function z(){var s;if(!d.value){h.value=[],c.value="";return}try{await C.setupCoursePack(d.value),(s=C.currentCoursePack)!=null&&s.courses&&(h.value=C.currentCoursePack.courses)}catch(e){console.error("加载课程列表失败:",e),m.error("加载课程列表失败")}}async function j(s){try{const e=await k.getImageInfo("course",s);if(e&&e.url){g.value=e.url;return}const o=h.value.find(x=>x.id===s);o&&o.cover?g.value=o.cover:g.value=null}catch(e){console.error("加载课程封面失败:",e),g.value=null}}function P(s,e){const o=s.target;if(o.files&&o.files[0]){const x=o.files[0],G=new FileReader;G.onload=w=>{var F;if(typeof((F=w.target)==null?void 0:F.result)=="string"){switch(e){case"frontLogo":t.value=w.target.result;break;case"backLogo":l.value=w.target.result;break;case"coursePackCover":i.value=w.target.result;break;case"courseCover":g.value=w.target.result;break}v.value[e]=x}},G.readAsDataURL(x)}}async function I(s){switch(s){case"frontLogo":t.value="/logo.png";break;case"backLogo":l.value="";break;case"coursePackCover":await D(p.value);break;case"courseCover":await j(c.value);break}v.value[s]=null}async function U(){if(!p.value||!i.value){m.warning("请选择课程包并上传封面图片");return}try{if(v.value.coursePackCover){const s=await k.uploadImage("coursePack",p.value,v.value.coursePackCover);s&&(i.value=s.url,v.value.coursePackCover=null,m.success("课程包封面保存成功"))}else m.info("没有新的封面图片需要上传")}catch(s){console.error("保存课程包封面失败:",s),m.error("保存课程包封面失败")}}async function E(){if(!c.value||!g.value){m.warning("请选择课程并上传封面图片");return}try{if(v.value.courseCover){const s=await k.uploadImage("course",c.value,v.value.courseCover);s&&(g.value=s.url,v.value.courseCover=null,m.success("课程封面保存成功"))}else m.info("没有新的封面图片需要上传")}catch(s){console.error("保存课程封面失败:",s),m.error("保存课程封面失败")}}async function V(){const s=[];let e=!1;if(v.value.frontLogo&&(s.push(k.uploadImage("logo","front",v.value.frontLogo).then(o=>{o&&(t.value=o.url,v.value.frontLogo=null)})),e=!0),v.value.backLogo&&(s.push(k.uploadImage("logo","back",v.value.backLogo).then(o=>{o&&(l.value=o.url,v.value.backLogo=null)})),e=!0),p.value&&v.value.coursePackCover&&(s.push(U()),e=!0),c.value&&v.value.courseCover&&(s.push(E()),e=!0),!e){m.info("没有发现需要保存的更改");return}try{await Promise.all(s),m.success("所有更改已保存")}catch(o){console.error("保存更改时出错:",o),m.error("保存更改失败")}}return(s,e)=>(u(),n("div",Q,[e[29]||(e[29]=r("h2",{class:"mb-6 text-2xl font-bold"},"图片管理",-1)),r("div",X,[e[17]||(e[17]=r("h3",{class:"mb-4 text-xl font-semibold"},"Logo 管理",-1)),r("div",Y,[r("div",Z,[e[13]||(e[13]=r("h4",{class:"font-medium"},"前端 Logo",-1)),r("div",ee,[t.value?(u(),n("img",{key:0,src:t.value,alt:"前端 Logo",class:"h-full max-h-24 object-contain"},null,8,re)):(u(),n("div",oe,e[11]||(e[11]=[r("p",null,"暂无图片",-1)])))]),r("div",se,[r("label",te,[e[12]||(e[12]=r("span",null,"上传图片",-1)),r("input",{type:"file",class:"hidden",accept:"image/*",onChange:e[0]||(e[0]=o=>P(o,"frontLogo"))},null,32)]),t.value?(u(),n("button",{key:0,onClick:e[1]||(e[1]=o=>I("frontLogo")),class:"rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"}," 重置 ")):y("",!0)])]),r("div",ae,[e[16]||(e[16]=r("h4",{class:"font-medium"},"后端 Logo",-1)),r("div",le,[l.value?(u(),n("img",{key:0,src:l.value,alt:"后端 Logo",class:"h-full max-h-24 object-contain"},null,8,ne)):(u(),n("div",ue,e[14]||(e[14]=[r("p",null,"暂无图片",-1)])))]),r("div",ie,[r("label",ce,[e[15]||(e[15]=r("span",null,"上传图片",-1)),r("input",{type:"file",class:"hidden",accept:"image/*",onChange:e[2]||(e[2]=o=>P(o,"backLogo"))},null,32)]),l.value?(u(),n("button",{key:0,onClick:e[3]||(e[3]=o=>I("backLogo")),class:"rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"}," 重置 ")):y("",!0)])])])]),r("div",de,[e[21]||(e[21]=r("h3",{class:"mb-4 text-xl font-semibold"},"课程包封面管理",-1)),r("div",ge,[L(r("select",{"onUpdate:modelValue":e[4]||(e[4]=o=>p.value=o),class:"w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800"},[e[18]||(e[18]=r("option",{value:""},"请选择课程包",-1)),(u(!0),n(S,null,A(b.value,o=>(u(),n("option",{key:o.id,value:o.id},$(o.title),9,ve))),128))],512),[[_,p.value]])]),p.value?(u(),n("div",fe,[r("div",me,[i.value?(u(),n("img",{key:0,src:i.value,alt:"课程包封面",class:"h-full max-h-40 object-contain"},null,8,pe)):(u(),n("div",be,e[19]||(e[19]=[r("p",null,"暂无封面图片",-1)])))]),r("div",ye,[r("label",ke,[e[20]||(e[20]=r("span",null,"上传封面",-1)),r("input",{type:"file",class:"hidden",accept:"image/*",onChange:e[5]||(e[5]=o=>P(o,"coursePackCover"))},null,32)]),i.value?(u(),n("button",{key:0,onClick:e[6]||(e[6]=o=>I("coursePackCover")),class:"rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"}," 重置 ")):y("",!0),p.value&&i.value?(u(),n("button",{key:1,onClick:U,class:"rounded-md bg-green-500 px-4 py-2 text-white hover:bg-green-600"}," 保存更改 ")):y("",!0)])])):y("",!0)]),r("div",he,[e[28]||(e[28]=r("h3",{class:"mb-4 text-xl font-semibold"},"课程封面管理",-1)),r("div",xe,[r("div",Ce,[e[23]||(e[23]=r("label",{class:"block font-medium"},"选择课程包",-1)),L(r("select",{"onUpdate:modelValue":e[7]||(e[7]=o=>d.value=o),class:"w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800",onChange:z},[e[22]||(e[22]=r("option",{value:""},"请选择课程包",-1)),(u(!0),n(S,null,A(b.value,o=>(u(),n("option",{key:o.id,value:o.id},$(o.title),9,we))),128))],544),[[_,d.value]])]),r("div",Pe,[e[25]||(e[25]=r("label",{class:"block font-medium"},"选择课程",-1)),L(r("select",{"onUpdate:modelValue":e[8]||(e[8]=o=>c.value=o),class:"w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800",disabled:!d.value},[e[24]||(e[24]=r("option",{value:""},"请选择课程",-1)),(u(!0),n(S,null,A(h.value,o=>(u(),n("option",{key:o.id,value:o.id},$(o.title),9,Le))),128))],8,Ie),[[_,c.value]])])]),c.value?(u(),n("div",_e,[r("div",Se,[g.value?(u(),n("img",{key:0,src:g.value,alt:"课程封面",class:"h-full max-h-40 object-contain"},null,8,Ae)):(u(),n("div",$e,e[26]||(e[26]=[r("p",null,"暂无封面图片",-1)])))]),r("div",De,[r("label",je,[e[27]||(e[27]=r("span",null,"上传封面",-1)),r("input",{type:"file",class:"hidden",accept:"image/*",onChange:e[9]||(e[9]=o=>P(o,"courseCover"))},null,32)]),g.value?(u(),n("button",{key:0,onClick:e[10]||(e[10]=o=>I("courseCover")),class:"rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"}," 重置 ")):y("",!0),c.value&&g.value?(u(),n("button",{key:1,onClick:E,class:"rounded-md bg-green-500 px-4 py-2 text-white hover:bg-green-600"}," 保存更改 ")):y("",!0)])])):y("",!0)]),r("div",{class:"mt-8 flex justify-end"},[r("button",{onClick:V,class:"rounded-md bg-purple-600 px-6 py-3 text-white shadow-md hover:bg-purple-700"}," 保存所有更改 ")])]))}});f(!1);f("");function Ee(){const a=H(),t=f(!1),l=f(!0);return O(async()=>{l.value=!0;try{console.log("[AdminGuard] 检查管理员权限");const i=localStorage.getItem("adminLoggedIn")==="true",g=localStorage.getItem("adminToken");i&&g?(console.log("[AdminGuard] 管理员权限验证通过"),t.value=!0):(console.warn("[AdminGuard] 用户不是管理员"),a.push("/"))}catch(i){console.error("[AdminGuard] 检查管理员权限时出错:",i),a.push("/")}finally{l.value=!1}}),{isAdmin:t,isLoading:l}}const Ge={class:"container mx-auto px-4 py-8"},Ne=M({__name:"images",setup(a){return Ee(),(t,l)=>(u(),n("div",Ge,[l[0]||(l[0]=r("h1",{class:"mb-8 text-3xl font-bold"},"网站图片管理",-1)),J(Ue)]))}});export{Ne as default};
