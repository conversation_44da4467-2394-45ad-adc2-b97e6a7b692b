import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, p as parseCookies, $ as $fetch, c as createError } from '../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  var _a;
  try {
    console.log("\u5F00\u59CB\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E...");
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    console.log("\u4EEA\u8868\u76D8API: Token\u72B6\u6001:", token ? "\u5B58\u5728" : "\u4E0D\u5B58\u5728");
    if (token) {
      try {
        const parts = token.split(".");
        if (parts.length === 3) {
          const payload = JSON.parse(Buffer.from(parts[1], "base64").toString());
          console.log("\u4EEA\u8868\u76D8API: Token\u6709\u6548\u8F7D\u8377:", payload);
        }
      } catch (e) {
        console.error("\u4EEA\u8868\u76D8API: Token\u89E3\u6790\u5931\u8D25:", e);
      }
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log(`\u4EEA\u8868\u76D8API: \u5C1D\u8BD5\u4ECE ${apiBaseUrl}/api/dashboard \u83B7\u53D6\u6570\u636E\uFF0C\u4F7F\u7528token:`, !!token);
    const dashboardStats = await $fetch(`${apiBaseUrl}/api/dashboard`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : ""
      },
      timeout: 1e4
    });
    console.log("\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E:", dashboardStats);
    return dashboardStats;
  } catch (error) {
    console.error("\u83B7\u53D6\u7EDF\u8BA1\u4FE1\u606F\u5931\u8D25", error);
    if (error instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    if (error instanceof Error && "cause" in error) {
      console.error("\u7F51\u7EDC\u9519\u8BEF\u8BE6\u60C5:", error.cause);
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u4EEA\u8868\u76D8\u6570\u636E\u5931\u8D25",
      data: { error: error instanceof Error ? error.message : String(error) }
    });
  }
});

export { index as default };
//# sourceMappingURL=index2.mjs.map
