import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, $ as $fetch, c as createError } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const stats_get = defineEventHandler(async (event) => {
  try {
    console.log("\u6B63\u5728\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E...");
    const dashboardStats = await $fetch("http://localhost:5000/api/dashboard", {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json"
      },
      timeout: 1e4
      // 10秒超时
    });
    console.log("\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E\u83B7\u53D6\u6210\u529F:", dashboardStats);
    return dashboardStats;
  } catch (error) {
    console.error("\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u5931\u8D25:", error);
    if (error instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    if (error instanceof Error && "cause" in error) {
      console.error("\u7F51\u7EDC\u9519\u8BEF\u8BE6\u60C5:", error.cause);
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u5931\u8D25",
      data: {
        error: error instanceof Error ? {
          name: error.name,
          message: error.message
        } : error
      }
    });
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
