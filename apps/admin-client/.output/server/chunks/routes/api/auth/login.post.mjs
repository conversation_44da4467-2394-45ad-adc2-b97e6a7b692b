import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, c as createError, u as useRuntimeConfig, $ as $fetch, s as setCookie, F as FetchError } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const login_post = defineEventHandler(async (event) => {
  var _a, _b, _c;
  try {
    console.log("[API:login] \u5F00\u59CB\u5904\u7406\u767B\u5F55\u8BF7\u6C42", {
      method: event.method,
      url: event.path,
      headers: event.headers
    });
    const body = await readBody(event);
    if (!body) {
      console.error("[API:login] \u8BF7\u6C42\u4F53\u4E3A\u7A7A");
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u6C42\u4F53\u4E0D\u80FD\u4E3A\u7A7A",
        data: { message: "\u8BF7\u63D0\u4F9B\u6709\u6548\u7684\u767B\u5F55\u51ED\u636E" }
      });
    }
    const { email, password } = body;
    if (!email || !password) {
      console.error("[API:login] \u90AE\u7BB1\u6216\u5BC6\u7801\u4E3A\u7A7A", { email: !!email, password: !!password });
      throw createError({
        statusCode: 400,
        statusMessage: "\u90AE\u7BB1\u548C\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",
        data: { message: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u548C\u5BC6\u7801" }
      });
    }
    console.log(`[API:login] \u5C1D\u8BD5\u767B\u5F55\uFF1A${email}`);
    const config = useRuntimeConfig();
    const apiBaseUrl = config.public.apiBaseUrl || "http://localhost:5000";
    try {
      const response = await $fetch("/api/auth/login", {
        baseURL: apiBaseUrl,
        method: "POST",
        body: { email, password },
        headers: {
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("[API:login] \u540E\u7AEF\u54CD\u5E94:", response);
      if (response.user && (response.user.role === "ADMIN" || response.user.role === "admin" || response.user.role === "superadmin")) {
        console.log("[API:login] \u767B\u5F55\u6210\u529F\uFF0C\u7528\u6237\u89D2\u8272:", response.user.role);
        const token = response.token;
        setCookie(event, "adminToken", token, {
          httpOnly: true,
          path: "/",
          maxAge: 60 * 60 * 24 * 7,
          // 7天过期
          secure: true,
          sameSite: "lax"
        });
        return {
          success: true,
          token,
          user: response.user
        };
      } else {
        console.warn("[API:login] \u7528\u6237\u4E0D\u662F\u7BA1\u7406\u5458:", (_a = response.user) == null ? void 0 : _a.role);
        throw createError({
          statusCode: 403,
          statusMessage: "\u6743\u9650\u4E0D\u8DB3",
          data: {
            success: false,
            message: "\u60A8\u6CA1\u6709\u7BA1\u7406\u5458\u6743\u9650"
          }
        });
      }
    } catch (apiError) {
      console.error("[API:login] API\u8C03\u7528\u5931\u8D25:", apiError);
      let errorMessage = "\u90AE\u7BB1\u6216\u5BC6\u7801\u4E0D\u6B63\u786E";
      if (apiError instanceof FetchError && apiError.data) {
        console.log("[API:login] FetchError\u6570\u636E:", apiError.data);
        errorMessage = apiError.data.message || errorMessage;
      } else if ((_b = apiError.data) == null ? void 0 : _b.message) {
        console.log("[API:login] API\u9519\u8BEF\u6570\u636E:", apiError.data);
        errorMessage = ((_c = apiError.data) == null ? void 0 : _c.message) || errorMessage;
      } else if (apiError.response && apiError.response._data) {
        console.log("[API:login] \u54CD\u5E94\u6570\u636E:", apiError.response._data);
        errorMessage = apiError.response._data.message || errorMessage;
      }
      throw createError({
        statusCode: apiError.statusCode || 401,
        statusMessage: apiError.statusMessage || "\u8BA4\u8BC1\u5931\u8D25",
        data: {
          success: false,
          message: errorMessage
        }
      });
    }
  } catch (error) {
    console.error("[API:login] \u767B\u5F55\u5904\u7406\u9519\u8BEF:", error);
    if (error instanceof Error) {
      console.error("[API:login] \u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    let errorMessage = "\u767B\u5F55\u8FC7\u7A0B\u4E2D\u53D1\u751F\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5";
    try {
      if (error.cause && error.cause.data && error.cause.data.message) {
        errorMessage = String(error.cause.data.message);
      } else if (error.data && error.data.message) {
        errorMessage = String(error.data.message);
      } else if (error.response && error.response._data && error.response._data.message) {
        errorMessage = String(error.response._data.message);
      }
      console.log("[API:login] \u63D0\u53D6\u7684\u9519\u8BEF\u6D88\u606F:", errorMessage);
    } catch (e) {
      console.error("[API:login] \u63D0\u53D6\u9519\u8BEF\u6D88\u606F\u65F6\u51FA\u9519:", e);
    }
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: errorMessage,
      data: {
        success: false,
        message: errorMessage
      }
    });
  }
});

export { login_post as default };
//# sourceMappingURL=login.post.mjs.map
