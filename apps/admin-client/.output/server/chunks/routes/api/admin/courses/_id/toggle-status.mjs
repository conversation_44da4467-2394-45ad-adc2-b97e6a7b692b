import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch } from '../../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const toggleStatus = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const id = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F3A\u5C11\u8BFE\u7A0BID"
      });
    }
    console.log(`\u5F00\u59CB\u5207\u6362\u8BFE\u7A0BID: ${id} \u7684\u72B6\u6001...`);
    const cookies = parseCookies(event);
    const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    try {
      const dashboardResponse = await $fetch("/api/dashboard", {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u8BA4\u8BC1\u6709\u6548\uFF0C\u4EEA\u8868\u76D8\u6570\u636E:", dashboardResponse);
      try {
        const courseResponse = await $fetch(`/api/courses/${id}`, {
          baseURL: apiBaseUrl,
          method: "GET",
          headers: {
            "Authorization": `Bearer ${token}`,
            "Accept": "application/json",
            "Content-Type": "application/json"
          }
        });
        console.log("\u83B7\u53D6\u5230\u8BFE\u7A0B\u6570\u636E:", courseResponse);
        const currentStatus = courseResponse.isPublished !== void 0 ? courseResponse.isPublished : courseResponse.published !== void 0 ? courseResponse.published : false;
        console.log(`\u5F53\u524D\u8BFE\u7A0B\u72B6\u6001: ${currentStatus ? "\u5DF2\u53D1\u5E03" : "\u672A\u53D1\u5E03"}, \u5C06\u5207\u6362\u4E3A: ${!currentStatus ? "\u5DF2\u53D1\u5E03" : "\u672A\u53D1\u5E03"}`);
        try {
          const updateResponse = await $fetch(`/api/courses/${id}`, {
            baseURL: apiBaseUrl,
            method: "PUT",
            headers: {
              "Authorization": `Bearer ${token}`,
              "Accept": "application/json",
              "Content-Type": "application/json"
            },
            body: {
              isPublished: !currentStatus,
              published: !currentStatus
            }
          });
          console.log("\u6210\u529F\u66F4\u65B0\u8BFE\u7A0B\u72B6\u6001:", updateResponse);
          return {
            ...updateResponse,
            isPublished: !currentStatus,
            success: true,
            message: "\u8BFE\u7A0B\u72B6\u6001\u5207\u6362\u6210\u529F"
          };
        } catch (updateError) {
          console.error("\u66F4\u65B0\u8BFE\u7A0B\u72B6\u6001\u5931\u8D25:", updateError);
          throw createError({
            statusCode: 500,
            statusMessage: "\u5207\u6362\u8BFE\u7A0B\u72B6\u6001\u65F6\u51FA\u9519",
            data: { error: updateError instanceof Error ? updateError.message : String(updateError) }
          });
        }
      } catch (courseError) {
        console.error("\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E\u5931\u8D25:", courseError);
        return createError({
          statusCode: 500,
          message: "\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E\u5931\u8D25"
        });
      }
    } catch (dashboardError) {
      console.error("\u8BA4\u8BC1\u9A8C\u8BC1\u5931\u8D25:", dashboardError);
      return createError({
        statusCode: 401,
        message: "\u8BA4\u8BC1\u9A8C\u8BC1\u5931\u8D25"
      });
    }
  } catch (err) {
    console.error("\u5207\u6362\u8BFE\u7A0B\u72B6\u6001\u65F6\u51FA\u9519:", err);
    return createError({
      statusCode: 500,
      message: "\u5207\u6362\u8BFE\u7A0B\u72B6\u6001\u65F6\u51FA\u9519"
    });
  }
});

export { toggleStatus as default };
//# sourceMappingURL=toggle-status.mjs.map
