import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const verifyAuth = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const token = event.req.headers.authorization || ((_b = (_a = event.req.headers.cookie) == null ? void 0 : _a.match(/adminToken=([^;]+)/)) == null ? void 0 : _b[1]) || "";
    console.log("[API:verify-auth] \u9A8C\u8BC1\u8BA4\u8BC1\u72B6\u6001\uFF0C\u4EE4\u724C:", token ? "\u5B58\u5728" : "\u4E0D\u5B58\u5728");
    if (token) {
      console.log("[API:verify-auth] \u4EE4\u724C\u5B58\u5728\uFF0C\u8FD4\u56DE\u8BA4\u8BC1\u6210\u529F");
      return {
        authenticated: true,
        role: "ADMIN"
      };
    } else {
      console.log("[API:verify-auth] \u65E0\u4EE4\u724C\uFF0C\u8FD4\u56DE\u8BA4\u8BC1\u5931\u8D25");
      return {
        authenticated: false
      };
    }
  } catch (error) {
    console.error("[API:verify-auth] \u9A8C\u8BC1\u8FC7\u7A0B\u51FA\u9519", error);
    return {
      authenticated: true,
      role: "ADMIN"
    };
  }
});

export { verifyAuth as default };
//# sourceMappingURL=verify-auth.mjs.map
