import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch } from '../../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const toggleStatus = defineEventHandler(async (event) => {
  var _a, _b, _c;
  try {
    const id = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!id) {
      console.error("\u5207\u6362\u7528\u6237\u72B6\u6001\u9519\u8BEF: \u7F3A\u5C11\u7528\u6237ID");
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F3A\u5C11\u7528\u6237ID"
      });
    }
    console.log(`\u5F00\u59CB\u5207\u6362\u7528\u6237ID: ${id} \u7684\u72B6\u6001...`);
    const cookies = parseCookies(event);
    const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u5207\u6362\u7528\u6237\u72B6\u6001\u9519\u8BEF: \u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    console.log(`\u5C1D\u8BD5API\u8C03\u7528 - GET ${apiBaseUrl}/api/dashboard`);
    try {
      const dashboardResponse = await $fetch("/api/dashboard", {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u8BA4\u8BC1\u6709\u6548\uFF0C\u4EEA\u8868\u76D8\u6570\u636E:", dashboardResponse);
      console.log(`\u5C1D\u8BD5API\u8C03\u7528 - GET ${apiBaseUrl}/api/users/${id}`);
      try {
        const userResponse = await $fetch(`/api/users/${id}`, {
          baseURL: apiBaseUrl,
          method: "GET",
          headers: {
            "Authorization": `Bearer ${token}`,
            "Accept": "application/json",
            "Content-Type": "application/json"
          }
        });
        console.log("\u83B7\u53D6\u5230\u7528\u6237\u6570\u636E:", userResponse);
        const currentStatus = userResponse.isActive !== void 0 ? userResponse.isActive : userResponse.active !== void 0 ? userResponse.active : true;
        console.log(`\u5F53\u524D\u7528\u6237\u72B6\u6001: ${currentStatus ? "\u6D3B\u8DC3" : "\u975E\u6D3B\u8DC3"}, \u5C06\u5207\u6362\u4E3A: ${!currentStatus ? "\u6D3B\u8DC3" : "\u975E\u6D3B\u8DC3"}`);
        try {
          console.log(`\u5C1D\u8BD5API\u8C03\u7528 - PATCH ${apiBaseUrl}/api/user/${id}/status`);
          const updateResponse = await $fetch(`/api/user/${id}/status`, {
            baseURL: apiBaseUrl,
            method: "PATCH",
            headers: {
              "Authorization": `Bearer ${token}`,
              "Accept": "application/json",
              "Content-Type": "application/json"
            },
            body: {
              isActive: !currentStatus,
              currentUserId: ((_c = event.user) == null ? void 0 : _c.userId) || id
            }
          });
          console.log("\u6210\u529F\u66F4\u65B0\u7528\u6237\u72B6\u6001:", updateResponse);
          return {
            ...updateResponse,
            isActive: !currentStatus,
            success: true,
            message: "\u7528\u6237\u72B6\u6001\u5207\u6362\u6210\u529F"
          };
        } catch (updateError) {
          console.error("\u66F4\u65B0\u7528\u6237\u72B6\u6001\u5931\u8D25:", updateError);
          console.error("\u9519\u8BEF\u8BE6\u60C5:", updateError.statusCode, updateError.message, updateError.data);
          try {
            console.log(`\u5C1D\u8BD5\u5907\u7528API\u8C03\u7528 - PUT ${apiBaseUrl}/api/users/${id}`);
            const backupResponse = await $fetch(`/api/users/${id}`, {
              baseURL: apiBaseUrl,
              method: "PUT",
              headers: {
                "Authorization": `Bearer ${token}`,
                "Accept": "application/json",
                "Content-Type": "application/json"
              },
              body: {
                isActive: !currentStatus
              }
            });
            console.log("\u901A\u8FC7\u5907\u7528\u65B9\u6CD5\u6210\u529F\u66F4\u65B0\u7528\u6237\u72B6\u6001:", backupResponse);
            return {
              ...backupResponse,
              isActive: !currentStatus,
              success: true,
              message: "\u7528\u6237\u72B6\u6001\u5DF2\u66F4\u65B0"
            };
          } catch (backupError) {
            console.error("\u5907\u7528\u65B9\u6CD5\u66F4\u65B0\u7528\u6237\u72B6\u6001\u5931\u8D25:", backupError);
            console.error("\u9519\u8BEF\u8BE6\u60C5:", backupError.statusCode, backupError.message, backupError.data);
            throw createError({
              statusCode: 500,
              statusMessage: "\u5207\u6362\u7528\u6237\u72B6\u6001\u65F6\u51FA\u9519",
              data: { error: backupError.message || String(backupError) }
            });
          }
        }
      } catch (userError) {
        console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u5931\u8D25:", userError);
        console.error("\u9519\u8BEF\u8BE6\u60C5:", userError.statusCode, userError.message, userError.data);
        throw createError({
          statusCode: 500,
          statusMessage: "\u83B7\u53D6\u7528\u6237\u6570\u636E\u5931\u8D25",
          data: { error: userError.message || String(userError) }
        });
      }
    } catch (dashboardError) {
      console.error("\u8BA4\u8BC1\u9A8C\u8BC1\u5931\u8D25:", dashboardError);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", dashboardError.statusCode, dashboardError.message, dashboardError.data);
      throw createError({
        statusCode: 401,
        statusMessage: "\u8BA4\u8BC1\u9A8C\u8BC1\u5931\u8D25",
        data: { error: dashboardError.message || String(dashboardError) }
      });
    }
  } catch (err) {
    console.error("\u5207\u6362\u7528\u6237\u72B6\u6001\u65F6\u51FA\u9519:", err);
    console.error("\u9519\u8BEF\u8BE6\u60C5:", err.statusCode, err.message, err.data);
    throw createError({
      statusCode: 500,
      statusMessage: "\u5207\u6362\u7528\u6237\u72B6\u6001\u65F6\u51FA\u9519",
      data: { error: err.message || String(err) }
    });
  }
});

export { toggleStatus as default };
//# sourceMappingURL=toggle-status.mjs.map
