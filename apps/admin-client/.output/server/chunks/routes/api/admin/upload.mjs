import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, e as readMultipartFormData, c as createError, p as parseCookies, $ as $fetch } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const upload = defineEventHandler(async (event) => {
  var _a;
  console.log("\u5904\u7406\u6587\u4EF6\u4E0A\u4F20\u8BF7\u6C42...");
  try {
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u4E0A\u4F20\u6587\u4EF6",
        data: { message: "\u8BF7\u63D0\u4F9B\u8981\u4E0A\u4F20\u7684\u6587\u4EF6" }
      });
    }
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    const newFormData = new FormData();
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6587\u4EF6\u6570\u636E",
        data: { message: "\u4E0A\u4F20\u7684\u6587\u4EF6\u6570\u636E\u65E0\u6548" }
      });
    }
    const file = new File([fileData.data], fileData.filename || "upload.file", {
      type: fileData.type || "application/octet-stream"
    });
    newFormData.append("file", file);
    console.log("\u53D1\u9001\u6587\u4EF6\u4E0A\u4F20\u8BF7\u6C42\u5230\u540E\u7AEF...");
    const response = await $fetch("/api/admin/upload", {
      baseURL: apiBaseUrl,
      method: "POST",
      body: newFormData,
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });
    console.log("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F:", response);
    return response;
  } catch (error) {
    console.error("\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: "\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25",
      data: {
        message: "\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25",
        error: error instanceof Error ? error.message : String(error)
      }
    });
  }
});

export { upload as default };
//# sourceMappingURL=upload.mjs.map
