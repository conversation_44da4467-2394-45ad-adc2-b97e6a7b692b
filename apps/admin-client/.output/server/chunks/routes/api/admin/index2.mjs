import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch, r as readBody } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  console.log("\u5F00\u59CB\u5904\u7406\u8BFE\u7A0B\u6570\u636E\u8BF7\u6C42...");
  const method = event.node.req.method;
  if (method === "GET") {
    console.log("\u5904\u7406GET\u8BF7\u6C42...");
    return await handleGetCourses(event);
  } else if (method === "POST") {
    console.log("\u5904\u7406POST\u8BF7\u6C42...");
    return await handleCreateCourse(event);
  } else {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
});
async function handleGetCourses(event) {
  var _a;
  try {
    console.log("\u5F00\u59CB\u83B7\u53D6\u8BFE\u7A0B\u5217\u8868...");
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    console.log("\u5C1D\u8BD5\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E...");
    const dashboardResponse = await $fetch("/api/dashboard", {
      baseURL: apiBaseUrl,
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u4EEA\u8868\u76D8API\u9A8C\u8BC1\u6210\u529F:", dashboardResponse);
    console.log("\u5C1D\u8BD5\u76F4\u63A5\u4ECE/api/courses\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E...");
    try {
      const response = await $fetch("/api/courses", {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u6210\u529F\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E:", response);
      if (response.courses) {
        return normalizeCourses(response.courses);
      } else if (response.data) {
        return normalizeCourses(response.data);
      } else if (response.items) {
        return normalizeCourses(response.items);
      } else if (Array.isArray(response)) {
        return normalizeCourses(response);
      }
      throw createError({
        statusCode: 500,
        statusMessage: "\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E\u5931\u8D25",
        data: { error: "\u670D\u52A1\u5668\u8FD4\u56DE\u7684\u6570\u636E\u683C\u5F0F\u4E0D\u7B26\u5408\u9884\u671F" }
      });
    } catch (apiError) {
      console.error("API\u8C03\u7528\u5931\u8D25:", apiError);
      throw createError({
        statusCode: apiError.statusCode || 500,
        statusMessage: "\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E\u5931\u8D25",
        data: {
          error: apiError.message || "\u65E0\u6CD5\u4ECEAPI\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E",
          details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
        }
      });
    }
  } catch (err) {
    console.error("\u83B7\u53D6\u8BFE\u7A0B\u5217\u8868\u51FA\u9519:", err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: "\u83B7\u53D6\u8BFE\u7A0B\u6570\u636E\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleCreateCourse(event) {
  var _a;
  try {
    console.log("\u5F00\u59CB\u5904\u7406\u521B\u5EFA\u8BFE\u7A0B\u8BF7\u6C42...");
    const body = await readBody(event);
    console.log("\u6536\u5230\u521B\u5EFA\u8BFE\u7A0B\u8BF7\u6C42\u6570\u636E:", body);
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("API\u57FA\u7840URL:", apiBaseUrl);
    const requestBody = {
      ...body,
      // 确保字段名称正确
      coursePackId: body.coursePackId,
      title: body.title,
      description: body.description || "",
      order: body.order || 0,
      cover: body.cover || null
    };
    console.log("\u51C6\u5907\u53D1\u9001\u5230\u540E\u7AEF\u7684\u6570\u636E:", requestBody);
    try {
      console.log("\u5C1D\u8BD5\u8C03\u7528\u540E\u7AEFAPI\u521B\u5EFA\u8BFE\u7A0B...");
      const response = await $fetch("/api/admin/courses", {
        baseURL: apiBaseUrl,
        method: "POST",
        body: requestBody,
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u6210\u529F\u521B\u5EFA\u8BFE\u7A0B:", response);
      return response;
    } catch (apiError) {
      console.error("API\u521B\u5EFA\u8BFE\u7A0B\u8C03\u7528\u5931\u8D25:", apiError);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        statusCode: apiError.statusCode,
        message: apiError.message,
        data: apiError.data
      });
      try {
        console.log("\u5C1D\u8BD5\u5907\u7528API\u8DEF\u5F84\u521B\u5EFA\u8BFE\u7A0B...");
        const response = await $fetch("/api/courses", {
          baseURL: apiBaseUrl,
          method: "POST",
          body: requestBody,
          headers: {
            "Authorization": `Bearer ${token}`,
            "Accept": "application/json",
            "Content-Type": "application/json"
          }
        });
        console.log("\u901A\u8FC7\u5907\u7528API\u8DEF\u5F84\u6210\u529F\u521B\u5EFA\u8BFE\u7A0B:", response);
        return response;
      } catch (backupError) {
        console.error("\u5907\u7528API\u8DEF\u5F84\u4E5F\u5931\u8D25:", backupError);
        throw createError({
          statusCode: backupError.statusCode || 500,
          statusMessage: "\u521B\u5EFA\u8BFE\u7A0B\u5931\u8D25",
          data: {
            error: backupError.message || "\u65E0\u6CD5\u901A\u8FC7API\u521B\u5EFA\u8BFE\u7A0B",
            details: "\u6240\u6709API\u8DEF\u5F84\u5C1D\u8BD5\u5747\u5931\u8D25"
          }
        });
      }
    }
  } catch (err) {
    console.error("\u521B\u5EFA\u8BFE\u7A0B\u51FA\u9519:", err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: "\u521B\u5EFA\u8BFE\u7A0B\u5931\u8D25",
      data: {
        error: err instanceof Error ? err.message : String(err),
        details: "\u8BF7\u68C0\u67E5API\u670D\u52A1\u5668\u72B6\u6001\u548C\u8BF7\u6C42\u6570\u636E\u683C\u5F0F"
      }
    });
  }
}
function normalizeCourses(courses) {
  return courses.map((course) => {
    var _a, _b;
    return {
      id: course.id || course._id,
      name: course.name || course.title,
      isPublished: course.isPublished !== void 0 ? course.isPublished : course.published !== void 0 ? course.published : false,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
      studentCount: course.studentCount || course.enrollments || (((_a = course._count) == null ? void 0 : _a.students) || ((_b = course._count) == null ? void 0 : _b.enrollments) || 0)
    };
  });
}

export { index as default };
//# sourceMappingURL=index2.mjs.map
