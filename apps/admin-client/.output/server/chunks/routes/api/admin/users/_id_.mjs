import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch, r as readBody } from '../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const _id_ = defineEventHandler(async (event) => {
  var _a, _b;
  console.log("\u5904\u7406\u7528\u6237\u8BE6\u60C5\u8BF7\u6C42...");
  const id = (_a = event.context.params) == null ? void 0 : _a.id;
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: "\u7F3A\u5C11\u7528\u6237ID"
    });
  }
  const method = event.node.req.method;
  console.log(`\u5904\u7406${method}\u8BF7\u6C42\uFF0C\u7528\u6237ID: ${id}`);
  const cookies = parseCookies(event);
  const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
  if (!token) {
    console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
    throw createError({
      statusCode: 401,
      statusMessage: "\u672A\u6388\u6743",
      data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
    });
  }
  const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
  console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
  try {
    if (method === "GET") {
      return await handleGetUser(event, id, token, apiBaseUrl);
    } else if (method === "PUT") {
      return await handleUpdateUser(event, id, token, apiBaseUrl);
    } else if (method === "DELETE") {
      return await handleDeleteUser(event, id, token, apiBaseUrl);
    } else {
      throw createError({
        statusCode: 405,
        statusMessage: "Method Not Allowed"
      });
    }
  } catch (err) {
    console.error(`\u5904\u7406\u7528\u6237(ID: ${id})\u8BF7\u6C42\u51FA\u9519:`, err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5904\u7406\u7528\u6237\u8BF7\u6C42\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
});
async function handleGetUser(event, id, token, apiBaseUrl) {
  console.log(`\u83B7\u53D6\u7528\u6237\u8BE6\u60C5, ID: ${id}`);
  try {
    const response = await $fetch(`/api/users/${id}`, {
      baseURL: apiBaseUrl,
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u83B7\u53D6\u7528\u6237\u8BE6\u60C5:", response);
    return normalizeUser(response);
  } catch (err) {
    console.error("\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25:", err);
    throw createError({
      statusCode: 404,
      statusMessage: "\u7528\u6237\u6570\u636E\u83B7\u53D6\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleUpdateUser(event, id, token, apiBaseUrl) {
  console.log(`\u66F4\u65B0\u7528\u6237, ID: ${id}`);
  const body = await readBody(event);
  console.log("\u66F4\u65B0\u7528\u6237\u8BF7\u6C42\u6570\u636E:", body);
  try {
    const response = await $fetch(`/api/users/${id}`, {
      baseURL: apiBaseUrl,
      method: "PUT",
      body,
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u66F4\u65B0\u7528\u6237:", response);
    return {
      ...normalizeUser(response),
      success: true,
      message: "\u7528\u6237\u66F4\u65B0\u6210\u529F"
    };
  } catch (err) {
    console.error("\u66F4\u65B0\u7528\u6237\u5931\u8D25:", err);
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u7528\u6237\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleDeleteUser(event, id, token, apiBaseUrl) {
  console.log(`\u5220\u9664\u7528\u6237, ID: ${id}`);
  try {
    const response = await $fetch(`/api/users/${id}`, {
      baseURL: apiBaseUrl,
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u5220\u9664\u7528\u6237:", response);
    return {
      id,
      success: true,
      message: "\u7528\u6237\u5220\u9664\u6210\u529F"
    };
  } catch (err) {
    console.error("\u5220\u9664\u7528\u6237\u5931\u8D25:", err);
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u7528\u6237\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
function normalizeUser(user) {
  return {
    id: user.id || user._id,
    name: user.name || user.username,
    email: user.email,
    isActive: user.isActive !== void 0 ? user.isActive : user.active !== void 0 ? user.active : true,
    role: user.role || "student",
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  };
}

export { _id_ as default };
//# sourceMappingURL=_id_.mjs.map
