import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch, r as readBody } from '../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const _id_ = defineEventHandler(async (event) => {
  var _a, _b;
  console.log("\u5904\u7406\u8BFE\u7A0B\u5305\u8BE6\u60C5\u8BF7\u6C42...");
  const id = (_a = event.context.params) == null ? void 0 : _a.id;
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: "\u7F3A\u5C11\u8BFE\u7A0B\u5305ID"
    });
  }
  const method = event.node.req.method;
  console.log(`\u5904\u7406${method}\u8BF7\u6C42\uFF0C\u8BFE\u7A0B\u5305ID: ${id}`);
  const cookies = parseCookies(event);
  const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
  if (!token) {
    console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
    throw createError({
      statusCode: 401,
      statusMessage: "\u672A\u6388\u6743",
      data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
    });
  }
  const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
  console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
  try {
    if (method === "GET") {
      return await handleGetCoursePackage(event, id, token, apiBaseUrl);
    } else if (method === "PUT") {
      return await handleUpdateCoursePackage(event, id, token, apiBaseUrl);
    } else if (method === "DELETE") {
      return await handleDeleteCoursePackage(event, id, token, apiBaseUrl);
    } else {
      throw createError({
        statusCode: 405,
        statusMessage: "Method Not Allowed"
      });
    }
  } catch (err) {
    console.error(`\u5904\u7406\u8BFE\u7A0B\u5305(ID: ${id})\u8BF7\u6C42\u51FA\u9519:`, err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: method === "GET" ? "\u83B7\u53D6\u8BFE\u7A0B\u5305\u5931\u8D25" : method === "PUT" ? "\u66F4\u65B0\u8BFE\u7A0B\u5305\u5931\u8D25" : "\u5220\u9664\u8BFE\u7A0B\u5305\u5931\u8D25",
      data: {
        error: err instanceof Error ? err.message : String(err),
        message: "\u65E0\u6CD5\u5904\u7406\u8BFE\u7A0B\u5305\u8BF7\u6C42\uFF0C\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C"
      }
    });
  }
});
async function handleGetCoursePackage(event, id, token, apiBaseUrl) {
  console.log(`\u83B7\u53D6\u8BFE\u7A0B\u5305\u8BE6\u60C5, ID: ${id}`);
  const apiPaths = [
    `/api/course-pack/${id}`,
    `/api/packages/${id}`,
    `/api/course-packages/${id}`,
    `/course-pack/${id}`,
    `/packages/${id}`
  ];
  let lastError = null;
  for (const path of apiPaths) {
    try {
      console.log(`\u5C1D\u8BD5\u901A\u8FC7 ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305...`);
      const response = await $fetch(path, {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log(`\u6210\u529F\u901A\u8FC7 ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305:`, response);
      return normalizeCoursePackage(response);
    } catch (err) {
      console.error(`\u901A\u8FC7 ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305\u5931\u8D25:`, err);
      lastError = err;
    }
  }
  console.log("\u6240\u6709API\u5C1D\u8BD5\u5931\u8D25\uFF0C\u8FD4\u56DE\u9519\u8BEF");
  throw createError({
    statusCode: 404,
    statusMessage: "\u8BFE\u7A0B\u5305\u6570\u636E\u83B7\u53D6\u5931\u8D25",
    data: { error: lastError instanceof Error ? lastError.message : String(lastError) }
  });
}
async function handleUpdateCoursePackage(event, id, token, apiBaseUrl) {
  console.log(`\u66F4\u65B0\u8BFE\u7A0B\u5305, ID: ${id}`);
  const body = await readBody(event);
  console.log("\u66F4\u65B0\u8BFE\u7A0B\u5305\u8BF7\u6C42\u6570\u636E:", body);
  const apiPaths = [
    `/api/course-pack/${id}`,
    `/api/packages/${id}`,
    `/api/course-packages/${id}`,
    `/course-pack/${id}`,
    `/packages/${id}`
  ];
  let lastError = null;
  for (const path of apiPaths) {
    try {
      console.log(`\u5C1D\u8BD5\u901A\u8FC7 ${path} \u66F4\u65B0\u8BFE\u7A0B\u5305...`);
      const response = await $fetch(path, {
        baseURL: apiBaseUrl,
        method: "PUT",
        body,
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log(`\u6210\u529F\u901A\u8FC7 ${path} \u66F4\u65B0\u8BFE\u7A0B\u5305:`, response);
      return {
        ...normalizeCoursePackage(response),
        success: true,
        message: "\u8BFE\u7A0B\u5305\u66F4\u65B0\u6210\u529F"
      };
    } catch (err) {
      console.error(`\u901A\u8FC7 ${path} \u66F4\u65B0\u8BFE\u7A0B\u5305\u5931\u8D25:`, err);
      lastError = err;
    }
  }
  console.log("\u6240\u6709API\u5C1D\u8BD5\u5931\u8D25\uFF0C\u8FD4\u56DE\u9519\u8BEF");
  throw createError({
    statusCode: 500,
    statusMessage: "\u8BFE\u7A0B\u5305\u66F4\u65B0\u5931\u8D25",
    data: { error: lastError instanceof Error ? lastError.message : String(lastError) }
  });
}
async function handleDeleteCoursePackage(event, id, token, apiBaseUrl) {
  console.log(`\u5220\u9664\u8BFE\u7A0B\u5305, ID: ${id}`);
  const apiPaths = [
    `/api/course-pack/${id}`,
    `/api/packages/${id}`,
    `/api/course-packages/${id}`,
    `/course-pack/${id}`,
    `/packages/${id}`
  ];
  let lastError = null;
  for (const path of apiPaths) {
    try {
      console.log(`\u5C1D\u8BD5\u901A\u8FC7 ${path} \u5220\u9664\u8BFE\u7A0B\u5305...`);
      const response = await $fetch(path, {
        baseURL: apiBaseUrl,
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log(`\u6210\u529F\u901A\u8FC7 ${path} \u5220\u9664\u8BFE\u7A0B\u5305:`, response);
      return {
        id,
        success: true,
        message: "\u8BFE\u7A0B\u5305\u5220\u9664\u6210\u529F"
      };
    } catch (err) {
      console.error(`\u901A\u8FC7 ${path} \u5220\u9664\u8BFE\u7A0B\u5305\u5931\u8D25:`, err);
      lastError = err;
    }
  }
  console.log("\u6240\u6709API\u5C1D\u8BD5\u5931\u8D25\uFF0C\u8FD4\u56DE\u9519\u8BEF");
  throw createError({
    statusCode: 500,
    statusMessage: "\u8BFE\u7A0B\u5305\u5220\u9664\u5931\u8D25",
    data: { error: lastError instanceof Error ? lastError.message : String(lastError) }
  });
}
function normalizeCoursePackage(pkg) {
  var _a;
  return {
    id: pkg.id || pkg._id,
    title: pkg.title || pkg.name,
    description: pkg.description || "",
    isFree: pkg.isFree !== void 0 ? pkg.isFree : false,
    cover: pkg.cover || "",
    courseIds: pkg.courseIds || ((_a = pkg.courses) == null ? void 0 : _a.map((c) => c.id)) || [],
    courses: Array.isArray(pkg.courses) ? pkg.courses.map((course) => ({
      id: course.id || course._id,
      name: course.title || course.name,
      description: course.description || "",
      order: course.order || 0,
      coursePackId: pkg.id || pkg._id,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    })) : [],
    createdAt: pkg.createdAt,
    updatedAt: pkg.updatedAt
  };
}

export { _id_ as default };
//# sourceMappingURL=_id_.mjs.map
