import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch, r as readBody } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  console.log("\u5F00\u59CB\u5904\u7406\u7528\u6237\u6570\u636E\u8BF7\u6C42...");
  const method = event.node.req.method;
  if (method === "GET") {
    console.log("\u5904\u7406GET\u8BF7\u6C42...");
    return await handleGetUsers(event);
  } else if (method === "POST") {
    console.log("\u5904\u7406POST\u8BF7\u6C42...");
    return await handleCreateUser(event);
  } else {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
});
async function handleGetUsers(event) {
  var _a;
  try {
    console.log("\u5F00\u59CB\u83B7\u53D6\u7528\u6237\u5217\u8868...");
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    console.log("\u9A8C\u8BC1\u4EE4\u724C\u6709\u6548\u6027...");
    const dashboardResponse = await $fetch("/api/dashboard", {
      baseURL: apiBaseUrl,
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u4EEA\u8868\u76D8API\u9A8C\u8BC1\u6210\u529F:", dashboardResponse);
    console.log("\u5C1D\u8BD5\u76F4\u63A5\u4ECE/api/users\u83B7\u53D6\u7528\u6237\u6570\u636E...");
    try {
      const response = await $fetch("/api/users", {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u6210\u529F\u83B7\u53D6\u7528\u6237\u6570\u636E:", response);
      if (response.users) {
        return normalizeUsers(response.users);
      } else if (response.data) {
        return normalizeUsers(response.data);
      } else if (response.items) {
        return normalizeUsers(response.items);
      } else if (Array.isArray(response)) {
        return normalizeUsers(response);
      }
      throw createError({
        statusCode: 500,
        statusMessage: "\u83B7\u53D6\u7528\u6237\u6570\u636E\u5931\u8D25",
        data: { error: "\u670D\u52A1\u5668\u8FD4\u56DE\u7684\u6570\u636E\u683C\u5F0F\u4E0D\u7B26\u5408\u9884\u671F" }
      });
    } catch (apiError) {
      console.error("API\u8C03\u7528\u5931\u8D25:", apiError);
      throw createError({
        statusCode: apiError.statusCode || 500,
        statusMessage: "\u83B7\u53D6\u7528\u6237\u6570\u636E\u5931\u8D25",
        data: {
          error: apiError.message || "\u65E0\u6CD5\u4ECEAPI\u83B7\u53D6\u7528\u6237\u6570\u636E",
          details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
        }
      });
    }
  } catch (err) {
    console.error("\u83B7\u53D6\u7528\u6237\u5217\u8868\u51FA\u9519:", err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: "\u83B7\u53D6\u7528\u6237\u6570\u636E\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleCreateUser(event) {
  var _a;
  try {
    console.log("\u5F00\u59CB\u5904\u7406\u521B\u5EFA\u7528\u6237\u8BF7\u6C42...");
    const body = await readBody(event);
    console.log("\u6536\u5230\u521B\u5EFA\u7528\u6237\u8BF7\u6C42\u6570\u636E:", {
      name: body.name,
      email: body.email,
      role: body.role,
      password: "******"
      // 不记录密码明文
    });
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u521B\u5EFA\u7528\u6237\u9519\u8BEF: \u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    try {
      console.log(`\u5C1D\u8BD5API\u8C03\u7528 - POST ${apiBaseUrl}/api/user/register`);
      const response = await $fetch("/api/user/register", {
        baseURL: apiBaseUrl,
        method: "POST",
        body: {
          username: body.name,
          email: body.email,
          password: body.password,
          role: body.role || "user"
        },
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log("\u6210\u529F\u521B\u5EFA\u7528\u6237\uFF0C\u54CD\u5E94:", {
        id: response.id,
        username: response.username || response.name,
        email: response.email,
        role: response.role
      });
      return {
        id: response.id,
        name: response.username || body.name,
        email: response.email,
        role: response.role || body.role,
        isActive: true
      };
    } catch (apiError) {
      console.error("\u7B2C\u4E00\u6B21\u5C1D\u8BD5\u521B\u5EFA\u7528\u6237\u5931\u8D25\uFF0C\u5C1D\u8BD5\u5907\u7528\u8DEF\u5F84:", apiError);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", apiError.statusCode, apiError.message, apiError.data);
      try {
        console.log(`\u5C1D\u8BD5\u5907\u7528API\u8C03\u7528 - POST ${apiBaseUrl}/api/users`);
        const response = await $fetch("/api/users", {
          baseURL: apiBaseUrl,
          method: "POST",
          body: {
            username: body.name,
            email: body.email,
            password: body.password,
            role: body.role || "user"
          },
          headers: {
            "Authorization": `Bearer ${token}`,
            "Accept": "application/json",
            "Content-Type": "application/json"
          }
        });
        console.log("\u901A\u8FC7\u5907\u7528\u8DEF\u5F84\u6210\u529F\u521B\u5EFA\u7528\u6237\uFF0C\u54CD\u5E94:", {
          id: response.id,
          username: response.username || response.name,
          email: response.email,
          role: response.role
        });
        return {
          id: response.id,
          name: response.username || body.name,
          email: response.email,
          role: response.role || body.role,
          isActive: true
        };
      } catch (secondApiError) {
        console.error("\u4E24\u6B21\u5C1D\u8BD5\u521B\u5EFA\u7528\u6237\u90FD\u5931\u8D25:", secondApiError);
        console.error("\u9519\u8BEF\u8BE6\u60C5:", secondApiError.statusCode, secondApiError.message, secondApiError.data);
        throw createError({
          statusCode: secondApiError.statusCode || 500,
          statusMessage: "\u521B\u5EFA\u7528\u6237\u5931\u8D25",
          data: {
            error: secondApiError.message || "\u65E0\u6CD5\u901A\u8FC7API\u521B\u5EFA\u7528\u6237",
            details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
          }
        });
      }
    }
  } catch (err) {
    console.error("\u521B\u5EFA\u7528\u6237\u51FA\u9519:", err);
    console.error("\u9519\u8BEF\u8BE6\u60C5:", err.statusCode, err.message, err.data);
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: "\u521B\u5EFA\u7528\u6237\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
function normalizeUsers(users) {
  return users.map((user) => ({
    id: user.id || user._id,
    name: user.name || user.username,
    email: user.email,
    isActive: user.isActive !== void 0 ? user.isActive : user.active !== void 0 ? user.active : true,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    role: user.role || "user"
  }));
}

export { index as default };
//# sourceMappingURL=index4.mjs.map
