{"version": 3, "file": "toggle-status.mjs", "sources": ["../../../../../../../../server/api/admin/courses/[id]/toggle-status.ts"], "sourcesContent": null, "names": [], "mappings": ";;;;;;;;;;AAGA,qBAAe,kBAAA,CAAmB,OAAO,KAAA,KAAU;AAHnD,EAAA,IAAA,EAAA,EAAA,EAAA;AAIE,EAAA,IAAI;AAEF,IAAA,MAAM,EAAA,GAAA,CAAK,EAAA,GAAA,KAAA,CAAM,OAAA,CAAQ,MAAA,KAAd,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAsB,EAAA;AACjC,IAAA,IAAI,CAAC,EAAA,EAAI;AACP,MAAA,MAAM,WAAA,CAAY;AAAA,QAChB,UAAA,EAAY,GAAA;AAAA,QACZ,aAAA,EAAe;AAAA,OAChB,CAAA;AAAA,IACH;AAEA,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,wCAAA,EAAa,EAAE,CAAA,sBAAA,CAAS,CAAA;AAGpC,IAAA,MAAM,OAAA,GAAU,aAAa,KAAK,CAAA;AAClC,IAAA,MAAM,KAAA,GAAA,CAAA,CAAQ,EAAA,GAAA,KAAA,CAAM,GAAA,CAAI,OAAA,CAAQ,aAAA,KAAlB,mBAAiC,OAAA,CAAQ,SAAA,EAAW,EAAA,CAAA,KACtD,OAAA,CAAQ,UAAA,IAAc,EAAA;AAGlC,IAAA,IAAI,CAAC,KAAA,EAAO;AACV,MAAA,OAAA,CAAQ,MAAM,sCAAQ,CAAA;AACtB,MAAA,MAAM,WAAA,CAAY;AAAA,QAChB,UAAA,EAAY,GAAA;AAAA,QACZ,aAAA,EAAe,oBAAA;AAAA,QACf,IAAA,EAAM,EAAE,OAAA,EAAS,oEAAA;AAAc,OAChC,CAAA;AAAA,IACH;AAGA,IAAA,MAAM,UAAA,GAAa,OAAA,CAAQ,GAAA,CAAI,YAAA,IAAgB,uBAAA;AAC/C,IAAA,OAAA,CAAQ,GAAA,CAAI,mCAAe,UAAU,CAAA;AAErC,IAAA,IAAI;AAEF,MAAA,MAAM,iBAAA,GAAoB,MAAM,MAAA,CAAO,gBAAA,EAAkB;AAAA,QACvD,OAAA,EAAS,UAAA;AAAA,QACT,MAAA,EAAQ,KAAA;AAAA,QACR,OAAA,EAAS;AAAA,UACP,eAAA,EAAiB,UAAU,KAAK,CAAA,CAAA;AAAA,UAChC,QAAA,EAAU,kBAAA;AAAA,UACV,cAAA,EAAgB;AAAA;AAClB,OACD,CAAA;AAED,MAAA,OAAA,CAAQ,GAAA,CAAI,iEAAe,iBAAiB,CAAA;AAG5C,MAAA,IAAI;AAEF,QAAA,MAAM,cAAA,GAAiB,MAAM,MAAA,CAAO,CAAA,aAAA,EAAgB,EAAE,CAAA,CAAA,EAAI;AAAA,UACxD,OAAA,EAAS,UAAA;AAAA,UACT,MAAA,EAAQ,KAAA;AAAA,UACR,OAAA,EAAS;AAAA,YACP,eAAA,EAAiB,UAAU,KAAK,CAAA,CAAA;AAAA,YAChC,QAAA,EAAU,kBAAA;AAAA,YACV,cAAA,EAAgB;AAAA;AAClB,SACD,CAAA;AAED,QAAA,OAAA,CAAQ,GAAA,CAAI,+CAAY,cAAc,CAAA;AAGtC,QAAA,MAAM,aAAA,GAAgB,cAAA,CAAe,WAAA,KAAgB,KAAA,CAAA,GACjD,cAAA,CAAe,cACd,cAAA,CAAe,SAAA,KAAc,KAAA,CAAA,GAAY,cAAA,CAAe,SAAA,GAAY,KAAA;AAEzE,QAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,sCAAA,EAAW,aAAA,GAAgB,oBAAA,GAAQ,oBAAK,+BAAW,CAAC,aAAA,GAAgB,oBAAA,GAAQ,oBAAK,CAAA,CAAE,CAAA;AAG/F,QAAA,IAAI;AACF,UAAA,MAAM,cAAA,GAAiB,MAAM,MAAA,CAAO,CAAA,aAAA,EAAgB,EAAE,CAAA,CAAA,EAAI;AAAA,YACxD,OAAA,EAAS,UAAA;AAAA,YACT,MAAA,EAAQ,KAAA;AAAA,YACR,OAAA,EAAS;AAAA,cACP,eAAA,EAAiB,UAAU,KAAK,CAAA,CAAA;AAAA,cAChC,QAAA,EAAU,kBAAA;AAAA,cACV,cAAA,EAAgB;AAAA,aAClB;AAAA,YACA,IAAA,EAAM;AAAA,cACJ,aAAa,CAAC,aAAA;AAAA,cACd,WAAW,CAAC;AAAA;AACd,WACD,CAAA;AAED,UAAA,OAAA,CAAQ,GAAA,CAAI,qDAAa,cAAc,CAAA;AAEvC,UAAA,OAAO;AAAA,YACL,GAAG,cAAA;AAAA,YACH,aAAa,CAAC,aAAA;AAAA,YACd,OAAA,EAAS,IAAA;AAAA,YACT,OAAA,EAAS;AAAA,WACX;AAAA,QACF,SAAS,WAAA,EAAa;AACpB,UAAA,OAAA,CAAQ,KAAA,CAAM,qDAAa,WAAW,CAAA;AAGtC,UAAA,MAAM,WAAA,CAAY;AAAA,YAChB,UAAA,EAAY,GAAA;AAAA,YACZ,aAAA,EAAe,wDAAA;AAAA,YACf,IAAA,EAAM,EAAE,KAAA,EAAO,WAAA,YAAuB,QAAQ,WAAA,CAAY,OAAA,GAAU,MAAA,CAAO,WAAW,CAAA;AAAE,WACzF,CAAA;AAAA,QACH;AAAA,MACF,SAAS,WAAA,EAAa;AACpB,QAAA,OAAA,CAAQ,KAAA,CAAM,qDAAa,WAAW,CAAA;AAEtC,QAAA,OAAO,WAAA,CAAY;AAAA,UACjB,UAAA,EAAY,GAAA;AAAA,UACZ,OAAA,EAAS;AAAA,SACV,CAAA;AAAA,MACH;AAAA,IACF,SAAS,cAAA,EAAgB;AACvB,MAAA,OAAA,CAAQ,KAAA,CAAM,yCAAW,cAAc,CAAA;AAEvC,MAAA,OAAO,WAAA,CAAY;AAAA,QACjB,UAAA,EAAY,GAAA;AAAA,QACZ,OAAA,EAAS;AAAA,OACV,CAAA;AAAA,IACH;AAAA,EACF,SAAS,GAAA,EAAK;AACZ,IAAA,OAAA,CAAQ,KAAA,CAAM,2DAAc,GAAG,CAAA;AAE/B,IAAA,OAAO,WAAA,CAAY;AAAA,MACjB,UAAA,EAAY,GAAA;AAAA,MACZ,OAAA,EAAS;AAAA,KACV,CAAA;AAAA,EACH;AACF,CAAC,CAAA;;;;"}