import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch, r as readBody } from '../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const _id_ = defineEventHandler(async (event) => {
  var _a, _b;
  console.log("\u5904\u7406\u8BFE\u7A0B\u8BE6\u60C5\u8BF7\u6C42...");
  const id = (_a = event.context.params) == null ? void 0 : _a.id;
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: "\u7F3A\u5C11\u8BFE\u7A0BID"
    });
  }
  const method = event.node.req.method;
  console.log(`\u5904\u7406${method}\u8BF7\u6C42\uFF0C\u8BFE\u7A0BID: ${id}`);
  const cookies = parseCookies(event);
  const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
  if (!token) {
    console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
    throw createError({
      statusCode: 401,
      statusMessage: "\u672A\u6388\u6743",
      data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
    });
  }
  const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
  console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
  try {
    if (method === "GET") {
      return await handleGetCourse(event, id, token, apiBaseUrl);
    } else if (method === "PUT") {
      return await handleUpdateCourse(event, id, token, apiBaseUrl);
    } else if (method === "DELETE") {
      return await handleDeleteCourse(event, id, token, apiBaseUrl);
    } else {
      throw createError({
        statusCode: 405,
        statusMessage: "Method Not Allowed"
      });
    }
  } catch (err) {
    console.error(`\u5904\u7406\u8BFE\u7A0B(ID: ${id})\u8BF7\u6C42\u51FA\u9519:`, err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5904\u7406\u8BFE\u7A0B\u8BF7\u6C42\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
});
async function handleGetCourse(event, id, token, apiBaseUrl) {
  console.log(`\u83B7\u53D6\u8BFE\u7A0B\u8BE6\u60C5, ID: ${id}`);
  try {
    const response = await $fetch(`/api/courses/${id}`, {
      baseURL: apiBaseUrl,
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u83B7\u53D6\u8BFE\u7A0B\u8BE6\u60C5:", response);
    return normalizeCourse(response);
  } catch (err) {
    console.error("\u83B7\u53D6\u8BFE\u7A0B\u8BE6\u60C5\u5931\u8D25:", err);
    throw createError({
      statusCode: 404,
      statusMessage: "\u8BFE\u7A0B\u6570\u636E\u83B7\u53D6\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleUpdateCourse(event, id, token, apiBaseUrl) {
  console.log(`\u66F4\u65B0\u8BFE\u7A0B, ID: ${id}`);
  const body = await readBody(event);
  console.log("\u66F4\u65B0\u8BFE\u7A0B\u8BF7\u6C42\u6570\u636E:", body);
  try {
    const response = await $fetch(`/api/courses/${id}`, {
      baseURL: apiBaseUrl,
      method: "PUT",
      body,
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u66F4\u65B0\u8BFE\u7A0B:", response);
    return {
      ...normalizeCourse(response),
      success: true,
      message: "\u8BFE\u7A0B\u66F4\u65B0\u6210\u529F"
    };
  } catch (err) {
    console.error("\u66F4\u65B0\u8BFE\u7A0B\u5931\u8D25:", err);
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u8BFE\u7A0B\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
async function handleDeleteCourse(event, id, token, apiBaseUrl) {
  console.log(`\u5220\u9664\u8BFE\u7A0B, ID: ${id}`);
  try {
    const response = await $fetch(`/api/courses/${id}`, {
      baseURL: apiBaseUrl,
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      }
    });
    console.log("\u6210\u529F\u5220\u9664\u8BFE\u7A0B:", response);
    return {
      id,
      success: true,
      message: "\u8BFE\u7A0B\u5220\u9664\u6210\u529F"
    };
  } catch (err) {
    console.error("\u5220\u9664\u8BFE\u7A0B\u5931\u8D25:", err);
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u8BFE\u7A0B\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
}
function normalizeCourse(course) {
  var _a, _b;
  return {
    id: course.id || course._id,
    name: course.name || course.title,
    description: course.description || "",
    price: course.price || 0,
    isPublished: course.isPublished !== void 0 ? course.isPublished : course.published !== void 0 ? course.published : false,
    studentCount: course.studentCount || course.enrollments || (((_a = course._count) == null ? void 0 : _a.students) || ((_b = course._count) == null ? void 0 : _b.enrollments) || 0),
    createdAt: course.createdAt,
    updatedAt: course.updatedAt
  };
}

export { _id_ as default };
//# sourceMappingURL=_id_.mjs.map
