import { d as defineEvent<PERSON>and<PERSON>, p as parseCookies, $ as $fetch, c as createError } from '../../../nitro/nitro.mjs';
import jwt from 'jsonwebtoken';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  var _a;
  console.log("\u5F00\u59CB\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E...");
  try {
    const cookies = parseCookies(event);
    const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
    console.log("\u4EEA\u8868\u76D8API: Token\u72B6\u6001:", token ? "\u5B58\u5728" : "\u7F3A\u5931");
    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || "default_secret");
        console.log("\u4EEA\u8868\u76D8API: Token\u6709\u6548\u8F7D\u8377:", decoded);
      } catch (e) {
        console.log("\u4EEA\u8868\u76D8API: Token\u89E3\u6790\u5931\u8D25:", e);
      }
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log(`\u4EEA\u8868\u76D8API: \u5C1D\u8BD5\u4ECE ${apiBaseUrl}/api/dashboard \u83B7\u53D6\u6570\u636E\uFF0C\u4F7F\u7528token: ${!!token}`);
    const response = await $fetch("/api/dashboard", {
      baseURL: apiBaseUrl,
      method: "GET",
      headers: token ? {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "application/json"
      } : void 0
    });
    console.log("\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E:", response);
    return response;
  } catch (err) {
    console.error("\u83B7\u53D6\u4EEA\u8868\u76D8\u7EDF\u8BA1\u6570\u636E\u51FA\u9519:", err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u4EEA\u8868\u76D8\u6570\u636E\u5931\u8D25",
      data: { error: err instanceof Error ? err.message : String(err) }
    });
  }
});

export { index as default };
//# sourceMappingURL=index3.mjs.map
