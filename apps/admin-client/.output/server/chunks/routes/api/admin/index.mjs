import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, p as parseCookies, c as createError, $ as $fetch, r as readBody } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  var _a;
  console.log("\u5904\u7406\u8BFE\u7A0B\u5305\u8BF7\u6C42...");
  const method = event.node.req.method;
  console.log(`\u5904\u7406${method}\u8BF7\u6C42`);
  const cookies = parseCookies(event);
  const token = ((_a = event.req.headers.authorization) == null ? void 0 : _a.replace("Bearer ", "")) || cookies.adminToken || "";
  if (!token) {
    console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
    throw createError({
      statusCode: 401,
      statusMessage: "\u672A\u6388\u6743",
      data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
    });
  }
  const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
  console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
  try {
    if (method === "GET") {
      return await handleGetCoursePackages(token, apiBaseUrl);
    } else if (method === "POST") {
      return await handleCreateCoursePackage(event, token, apiBaseUrl);
    } else {
      throw createError({
        statusCode: 405,
        statusMessage: "Method Not Allowed"
      });
    }
  } catch (err) {
    console.error("\u5904\u7406\u8BFE\u7A0B\u5305\u8BF7\u6C42\u51FA\u9519:", err);
    if (err instanceof Error) {
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
    }
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: method === "GET" ? "\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E\u5931\u8D25" : "\u521B\u5EFA\u8BFE\u7A0B\u5305\u5931\u8D25",
      data: {
        error: err instanceof Error ? err.message : String(err),
        details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
      }
    });
  }
});
async function handleGetCoursePackages(token, apiBaseUrl) {
  console.log("\u83B7\u53D6\u8BFE\u7A0B\u5305\u5217\u8868...");
  const apiPaths = [
    "/api/course-pack",
    "/api/packages",
    "/api/course-packages",
    "/course-pack",
    "/packages"
  ];
  let lastError = null;
  for (const path of apiPaths) {
    try {
      console.log(`\u5C1D\u8BD5\u4ECE ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E...`);
      const response = await $fetch(path, {
        baseURL: apiBaseUrl,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log(`\u6210\u529F\u4ECE ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E:`, response);
      if (Array.isArray(response)) {
        return response.map(normalizeCoursePackage);
      } else if (response && typeof response === "object") {
        return [normalizeCoursePackage(response)];
      }
      return [];
    } catch (err) {
      console.error(`\u4ECE ${path} \u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E\u5931\u8D25:`, err);
      lastError = err;
    }
  }
  console.error("\u6240\u6709API\u5C1D\u8BD5\u5931\u8D25");
  throw createError({
    statusCode: (lastError == null ? void 0 : lastError.statusCode) || 404,
    statusMessage: "\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E\u5931\u8D25",
    data: {
      error: "\u65E0\u6CD5\u4ECEAPI\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E",
      details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u63D0\u4F9B\u4E86\u8BFE\u7A0B\u5305\u7AEF\u70B9\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
    }
  });
}
async function handleCreateCoursePackage(event, token, apiBaseUrl) {
  console.log("\u521B\u5EFA\u8BFE\u7A0B\u5305...");
  const body = await readBody(event);
  console.log("\u521B\u5EFA\u8BFE\u7A0B\u5305\u8BF7\u6C42\u6570\u636E:", body);
  const apiPaths = [
    "/api/course-pack",
    "/api/packages",
    "/api/course-packages",
    "/course-pack",
    "/packages"
  ];
  let lastError = null;
  for (const path of apiPaths) {
    try {
      console.log(`\u5C1D\u8BD5\u901A\u8FC7 ${path} \u521B\u5EFA\u8BFE\u7A0B\u5305...`);
      const response = await $fetch(path, {
        baseURL: apiBaseUrl,
        method: "POST",
        body,
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        }
      });
      console.log(`\u6210\u529F\u901A\u8FC7 ${path} \u521B\u5EFA\u8BFE\u7A0B\u5305:`, response);
      return {
        ...normalizeCoursePackage(response),
        success: true,
        message: "\u8BFE\u7A0B\u5305\u521B\u5EFA\u6210\u529F"
      };
    } catch (err) {
      console.error(`\u901A\u8FC7 ${path} \u521B\u5EFA\u8BFE\u7A0B\u5305\u5931\u8D25:`, err);
      lastError = err;
    }
  }
  console.error("\u6240\u6709API\u5C1D\u8BD5\u5931\u8D25");
  throw createError({
    statusCode: (lastError == null ? void 0 : lastError.statusCode) || 500,
    statusMessage: "\u521B\u5EFA\u8BFE\u7A0B\u5305\u5931\u8D25",
    data: {
      error: "\u65E0\u6CD5\u901A\u8FC7API\u521B\u5EFA\u8BFE\u7A0B\u5305",
      details: "\u8BF7\u786E\u4FDDAPI\u670D\u52A1\u5668\u63D0\u4F9B\u4E86\u8BFE\u7A0B\u5305\u521B\u5EFA\u7AEF\u70B9\u5E76\u4E14\u60A8\u6709\u6B63\u786E\u7684\u8BBF\u95EE\u6743\u9650"
    }
  });
}
function normalizeCoursePackage(pkg) {
  var _a;
  return {
    id: pkg.id || pkg._id,
    title: pkg.title || pkg.name,
    description: pkg.description || "",
    isFree: pkg.isFree !== void 0 ? pkg.isFree : false,
    cover: pkg.cover || "",
    courseIds: pkg.courseIds || ((_a = pkg.courses) == null ? void 0 : _a.map((c) => c.id)) || [],
    courses: Array.isArray(pkg.courses) ? pkg.courses.map((course) => ({
      id: course.id || course._id,
      name: course.title || course.name,
      description: course.description || "",
      order: course.order || 0,
      coursePackId: pkg.id || pkg._id,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    })) : [],
    createdAt: pkg.createdAt,
    updatedAt: pkg.updatedAt
  };
}

export { index as default };
//# sourceMappingURL=index.mjs.map
