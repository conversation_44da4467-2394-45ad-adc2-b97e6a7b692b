import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError, p as parseCookies, $ as $fetch } from '../../../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const toggleStatus = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const id = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F3A\u5C11\u8BFE\u7A0B\u5305ID"
      });
    }
    console.log(`\u5F00\u59CB\u5207\u6362\u8BFE\u7A0B\u5305ID: ${id} \u7684\u72B6\u6001...`);
    const cookies = parseCookies(event);
    const token = ((_b = event.req.headers.authorization) == null ? void 0 : _b.replace("Bearer ", "")) || cookies.adminToken || "";
    if (!token) {
      console.error("\u7F3A\u5C11\u6388\u6743\u4EE4\u724C");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743",
        data: { message: "\u9700\u8981\u63D0\u4F9B\u6709\u6548\u7684\u6388\u6743\u4EE4\u724C" }
      });
    }
    const apiBaseUrl = process.env.API_BASE_URL || "http://localhost:5000";
    console.log("\u4F7F\u7528API\u57FA\u7840URL:", apiBaseUrl);
    const apiPaths = [
      `/api/course-pack/${id}/toggle-status`,
      `/api/packages/${id}/toggle-status`,
      `/api/course-packages/${id}/toggle-status`,
      `/course-pack/${id}/toggle-status`,
      `/packages/${id}/toggle-status`
    ];
    let lastError = null;
    try {
      const coursePackResponse = await $fetch(`/api/admin/course-packages/${id}`, {
        method: "GET"
      });
      console.log("\u83B7\u53D6\u5230\u8BFE\u7A0B\u5305\u6570\u636E:", coursePackResponse);
      const currentStatus = coursePackResponse.isFree !== void 0 ? coursePackResponse.isFree : false;
      console.log(`\u5F53\u524D\u8BFE\u7A0B\u5305\u72B6\u6001: ${currentStatus ? "\u514D\u8D39" : "\u4ED8\u8D39"}, \u5C06\u5207\u6362\u4E3A: ${!currentStatus ? "\u514D\u8D39" : "\u4ED8\u8D39"}`);
      for (const path of apiPaths) {
        try {
          console.log(`\u5C1D\u8BD5\u901A\u8FC7 ${path} \u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001...`);
          const response = await $fetch(path, {
            baseURL: apiBaseUrl,
            method: "POST",
            headers: {
              "Authorization": `Bearer ${token}`,
              "Accept": "application/json",
              "Content-Type": "application/json"
            }
          });
          console.log(`\u6210\u529F\u901A\u8FC7 ${path} \u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001:`, response);
          return {
            id,
            isFree: !currentStatus,
            success: true,
            message: "\u8BFE\u7A0B\u5305\u72B6\u6001\u5207\u6362\u6210\u529F"
          };
        } catch (err) {
          console.error(`\u901A\u8FC7 ${path} \u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001\u5931\u8D25:`, err);
          lastError = err;
        }
      }
      throw createError({
        statusCode: 500,
        statusMessage: "\u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001\u5931\u8D25",
        data: { error: lastError instanceof Error ? lastError.message : String(lastError) }
      });
    } catch (coursePackErr) {
      console.error("\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E\u5931\u8D25:", coursePackErr);
      throw createError({
        statusCode: 500,
        statusMessage: "\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E\u5931\u8D25",
        data: { error: coursePackErr instanceof Error ? coursePackErr.message : String(coursePackErr) }
      });
    }
  } catch (err) {
    console.error("\u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001\u65F6\u51FA\u9519:", err);
    return createError({
      statusCode: 500,
      message: "\u5207\u6362\u8BFE\u7A0B\u5305\u72B6\u6001\u65F6\u51FA\u9519"
    });
  }
});

export { toggleStatus as default };
//# sourceMappingURL=toggle-status.mjs.map
