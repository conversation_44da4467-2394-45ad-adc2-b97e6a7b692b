import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, u as useRuntimeConfig, g as getRequestURL, a as getQuery, r as readBody, b as getHeaders, c as createError } from '../../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const _____ = defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const apiBaseUrl = config.public.apiBaseUrl || "http://localhost:5000";
  const url = getRequestURL(event);
  const path = url.pathname.replace("/api/admin/", "");
  const query = getQuery(event);
  const targetUrl = `${apiBaseUrl}/api/admin/${path}`;
  console.log(`[Admin Proxy] ${event.node.req.method} ${url.pathname} -> ${targetUrl}`);
  try {
    const response = await $fetch(targetUrl, {
      method: event.node.req.method,
      headers: {
        ...getHeaders(event),
        host: void 0
        // 移除host头以避免冲突
      },
      body: event.node.req.method !== "GET" ? await readBody(event) : void 0,
      query
    });
    return response;
  } catch (error) {
    console.error(`[Admin Proxy Error] ${targetUrl}:`, error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Proxy Error"
    });
  }
});

export { _____ as default };
//# sourceMappingURL=_..._.mjs.map
