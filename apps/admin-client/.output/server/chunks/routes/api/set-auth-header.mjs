import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, s as setCookie } from '../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const setAuthHeader = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { token } = body;
    if (!token) {
      return { success: false, message: "\u7F3A\u5C11\u4EE4\u724C" };
    }
    setCookie(event, "adminToken", token, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
      // 7天过期
      secure: true,
      sameSite: "lax"
    });
    setCookie(event, "Authorization", `Bearer ${token}`, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
      // 7天过期
      secure: true,
      sameSite: "lax"
    });
    return { success: true };
  } catch (error) {
    console.error("\u8BBE\u7F6E\u6388\u6743\u5934\u51FA\u9519:", error);
    return { success: false, message: "\u8BBE\u7F6E\u6388\u6743\u5934\u5931\u8D25" };
  }
});

export { setAuthHeader as default };
//# sourceMappingURL=set-auth-header.mjs.map
