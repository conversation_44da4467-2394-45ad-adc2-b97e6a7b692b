{"version": 3, "file": "set-auth-header.mjs", "sources": ["../../../../../server/api/set-auth-header.ts"], "sourcesContent": null, "names": [], "mappings": ";;;;;;;;;;AAEA,sBAAe,kBAAA,CAAmB,OAAO,KAAA,KAAU;AACjD,EAAA,IAAI;AACF,IAAA,MAAM,IAAA,GAAO,MAAM,QAAA,CAAS,KAAK,CAAA;AACjC,IAAA,MAAM,EAAE,OAAM,GAAI,IAAA;AAElB,IAAA,IAAI,CAAC,KAAA,EAAO;AACV,MAAA,OAAO,EAAE,OAAA,EAAS,KAAA,EAAO,OAAA,EAAS,0BAAA,EAAO;AAAA,IAC3C;AAGA,IAAA,SAAA,CAAU,KAAA,EAAO,cAAc,KAAA,EAAO;AAAA,MACpC,QAAA,EAAU,IAAA;AAAA,MACV,IAAA,EAAM,GAAA;AAAA,MACN,MAAA,EAAQ,EAAA,GAAK,EAAA,GAAK,EAAA,GAAK,CAAA;AAAA;AAAA,MACvB,MAAA,EAAQ,IAAA;AAAA,MACR,QAAA,EAAU;AAAA,KACX,CAAA;AAGD,IAAA,SAAA,CAAU,KAAA,EAAO,eAAA,EAAiB,CAAA,OAAA,EAAU,KAAK,CAAA,CAAA,EAAI;AAAA,MACnD,QAAA,EAAU,IAAA;AAAA,MACV,IAAA,EAAM,GAAA;AAAA,MACN,MAAA,EAAQ,EAAA,GAAK,EAAA,GAAK,EAAA,GAAK,CAAA;AAAA;AAAA,MACvB,MAAA,EAAQ,IAAA;AAAA,MACR,QAAA,EAAU;AAAA,KACX,CAAA;AAED,IAAA,OAAO,EAAE,SAAS,IAAA,EAAK;AAAA,EACzB,SAAS,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,KAAA,CAAM,+CAAY,KAAK,CAAA;AAC/B,IAAA,OAAO,EAAE,OAAA,EAAS,KAAA,EAAO,OAAA,EAAS,4CAAA,EAAU;AAAA,EAC9C;AACF,CAAC,CAAA;;;;"}