import { PrismaClient } from '@prisma/client';
import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as createError } from '../../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index = defineEventHandler(async (event) => {
  const prisma = new PrismaClient();
  try {
    const contents = await prisma.content.findMany({
      orderBy: {
        createdAt: "desc"
      }
    });
    return contents;
  } catch (error) {
    console.error("\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u9519\u8BEF:", error);
    throw createError({
      statusCode: 500,
      message: "\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u5931\u8D25"
    });
  } finally {
    await prisma.$disconnect();
  }
});

export { index as default };
//# sourceMappingURL=index.mjs.map
