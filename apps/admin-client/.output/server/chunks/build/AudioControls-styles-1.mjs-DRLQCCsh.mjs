const AudioControls_vue_vue_type_style_index_0_scoped_3a3dfcc4_lang = ".audio-controls[data-v-3a3dfcc4],audio[data-v-3a3dfcc4]{min-width:120px}audio[data-v-3a3dfcc4]{height:32px;min-height:32px;width:120px}.btn.loading[data-v-3a3dfcc4]{cursor:not-allowed;opacity:.6}";

export { AudioControls_vue_vue_type_style_index_0_scoped_3a3dfcc4_lang as A };
//# sourceMappingURL=AudioControls-styles-1.mjs-DRLQCCsh.mjs.map
