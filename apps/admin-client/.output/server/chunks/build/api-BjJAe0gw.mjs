function getApiBaseUrl() {
  console.log("\u4F7F\u7528\u76F8\u5BF9API\u8DEF\u5F84");
  return "";
}
function getAuthHeaders() {
  const headers = {
    "Content-Type": "application/json"
  };
  return headers;
}
async function handleResponse(response) {
  console.log(`API\u54CD\u5E94: ${response.status} ${response.statusText} - ${response.url}`);
  if (response.status === 401 || response.status === 403) {
    const data = await response.json().catch(() => ({}));
    console.error("API\u6388\u6743\u9519\u8BEF:", data.message || response.statusText);
    throw new Error(data.message || "\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55");
  }
  if (!response.ok) {
    let errorMessage = "";
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const errorData = await response.json();
        console.error("API\u9519\u8BEF\u54CD\u5E94\u6570\u636E:", errorData);
        errorMessage = errorData.message || `${response.status} ${response.statusText}`;
      } else {
        const text = await response.text();
        console.error("API\u9519\u8BEF\u54CD\u5E94\u6587\u672C:", text);
        errorMessage = text || `${response.status} ${response.statusText}`;
      }
    } catch (parseError) {
      console.error("\u89E3\u6790API\u9519\u8BEF\u54CD\u5E94\u5931\u8D25:", parseError);
      errorMessage = `\u8BF7\u6C42\u5931\u8D25: ${response.status} ${response.statusText}`;
    }
    console.error(`API\u8BF7\u6C42\u5931\u8D25 (${response.status}): ${errorMessage}`);
    throw new Error(errorMessage);
  }
  if (response.status === 204) {
    return {};
  }
  try {
    const data = await response.json();
    return data;
  } catch (err) {
    console.error("\u89E3\u6790API\u54CD\u5E94JSON\u5931\u8D25:", err);
    return {};
  }
}
async function apiGet(url, params) {
  try {
    const fullUrl = new URL(url, getApiBaseUrl());
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== void 0) {
          fullUrl.searchParams.append(key, String(value));
        }
      });
    }
    console.log("API GET \u8BF7\u6C42 URL:", fullUrl.toString());
    console.log("API GET \u8BF7\u6C42\u53C2\u6570:", params);
    const headers = await getAuthHeaders();
    console.log("\u8BF7\u6C42\u5934:", headers);
    const response = await fetch(fullUrl.toString(), {
      method: "GET",
      headers
    });
    console.log("\u54CD\u5E94\u72B6\u6001:", response.status, response.statusText);
    if (!response.ok) {
      const errorBody = await response.text();
      console.error("API \u9519\u8BEF\u54CD\u5E94:", {
        status: response.status,
        statusText: response.statusText,
        body: errorBody
      });
      throw new Error(`HTTP \u9519\u8BEF! \u72B6\u6001: ${response.status}, \u6D88\u606F: ${errorBody}`);
    }
    const data = await response.json();
    console.group("API \u54CD\u5E94\u6570\u636E\u8BE6\u60C5");
    console.log("\u54CD\u5E94\u6570\u636E\u7C7B\u578B:", typeof data);
    console.log("\u54CD\u5E94\u6570\u636E\u952E:", Object.keys(data));
    console.log("\u54CD\u5E94\u6570\u636E\u5185\u5BB9(JSON):", JSON.stringify(data, null, 2));
    console.groupEnd();
    return data;
  } catch (error) {
    console.error("API GET \u8BF7\u6C42\u9519\u8BEF:", {
      url,
      params,
      errorName: error == null ? void 0 : error.name,
      errorMessage: error == null ? void 0 : error.message,
      errorStack: error == null ? void 0 : error.stack
    });
    throw error;
  }
}
async function apiPost(url, data) {
  const response = await fetch(`${getApiBaseUrl()}${url}`, {
    method: "POST",
    headers: getAuthHeaders(),
    body: JSON.stringify(data)
  });
  return handleResponse(response);
}
async function apiPut(url, data, options = {}) {
  try {
    console.log(`[API] PUT \u8BF7\u6C42: ${url}`, {
      data: typeof data === "object" ? "\u5BF9\u8C61\u6570\u636E" : data,
      hasOptions: !!options
    });
    const fullUrl = `${getApiBaseUrl()}${url}`;
    console.log(`[API] \u5B8C\u6574URL: ${fullUrl}`);
    const headers = getAuthHeaders();
    const requestOptions = {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
      credentials: "include",
      ...options
    };
    console.log(`[API] \u53D1\u9001PUT\u8BF7\u6C42\u5230 ${fullUrl}`);
    const response = await fetch(fullUrl, requestOptions);
    if (response.ok) {
      const result = await response.json();
      console.log(`[API] PUT\u8BF7\u6C42\u6210\u529F: ${url}`, { status: response.status });
      return result;
    } else {
      console.error(`[API] PUT\u8BF7\u6C42\u5931\u8D25: ${url}`, {
        status: response.status,
        statusText: response.statusText
      });
      let errorData = {};
      try {
        errorData = await response.json();
        console.error("[API] \u9519\u8BEF\u8BE6\u60C5:", errorData);
      } catch (e) {
        errorData = { message: response.statusText || "\u672A\u77E5\u9519\u8BEF" };
      }
      if (response.status === 401) {
        console.error("[API] \u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55");
        throw new Error("\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55");
      } else if (response.status === 403) {
        console.error("[API] \u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u6267\u884C\u6B64\u64CD\u4F5C");
        throw new Error(errorData.message || "\u65E0\u6743\u6267\u884C\u6B64\u64CD\u4F5C");
      }
      throw new Error(errorData.message || "\u8BF7\u6C42\u5931\u8D25");
    }
  } catch (error) {
    console.error(`[API] PUT\u8BF7\u6C42\u5F02\u5E38: ${url}`, error);
    throw error;
  }
}
async function apiDelete(url) {
  const response = await fetch(`${getApiBaseUrl()}${url}`, {
    method: "DELETE",
    headers: getAuthHeaders()
  });
  return handleResponse(response);
}
const api = {
  get: apiGet,
  post: apiPost,
  put: apiPut,
  delete: apiDelete
};

export { api as a };
//# sourceMappingURL=api-BjJAe0gw.mjs.map
