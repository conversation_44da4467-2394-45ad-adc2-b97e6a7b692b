const client_manifest = {
  "_47Na0wgx.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "47Na0wgx.js",
    "name": "QuillEditor",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": [
      "QuillEditor.BhgiJC-O.css"
    ]
  },
  "QuillEditor.BhgiJC-O.css": {
    "file": "QuillEditor.BhgiJC-O.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_AdminSidebar.CwhSaOil.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "AdminSidebar.CwhSaOil.css",
    "src": "_AdminSidebar.CwhSaOil.css"
  },
  "_BMvTVg1A.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BMvTVg1A.js",
    "name": "useApi",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CreateCourseModal.THr8NYb1.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "CreateCourseModal.THr8NYb1.css",
    "src": "_CreateCourseModal.THr8NYb1.css"
  },
  "_CxzfvvNm.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CxzfvvNm.js",
    "name": "api"
  },
  "_D0FjqKqG.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D0FjqKqG.js",
    "name": "LoadingSpinner",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": [
      "LoadingSpinner.6GNNx-ct.css"
    ]
  },
  "LoadingSpinner.6GNNx-ct.css": {
    "file": "LoadingSpinner.6GNNx-ct.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_DgOJsROy.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DgOJsROy.js",
    "name": "CreateCourseModal.vue",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js",
      "_DlAUqK2U.js",
      "_D0FjqKqG.js",
      "_x_rD_Ya3.js"
    ],
    "dynamicImports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "CreateCourseModal.THr8NYb1.css"
    ]
  },
  "CreateCourseModal.THr8NYb1.css": {
    "file": "CreateCourseModal.THr8NYb1.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_DlAUqK2U.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DlAUqK2U.js",
    "name": "_plugin-vue_export-helper"
  },
  "_DqjI2rGC.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DqjI2rGC.js",
    "name": "nuxt-link",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_LoadingSpinner.6GNNx-ct.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "LoadingSpinner.6GNNx-ct.css",
    "src": "_LoadingSpinner.6GNNx-ct.css"
  },
  "_QuillEditor.BhgiJC-O.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "QuillEditor.BhgiJC-O.css",
    "src": "_QuillEditor.BhgiJC-O.css"
  },
  "_eAbK7LvP.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "eAbK7LvP.js",
    "name": "client-only",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_oUsj6fiR.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "oUsj6fiR.js",
    "name": "AdminSidebar",
    "imports": [
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": [
      "AdminSidebar.CwhSaOil.css"
    ]
  },
  "AdminSidebar.CwhSaOil.css": {
    "file": "AdminSidebar.CwhSaOil.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_tJcwIiS2.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "tJcwIiS2.js",
    "name": "content",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js"
    ]
  },
  "_x_rD_Ya3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "x_rD_Ya3.js",
    "name": "interval"
  },
  "components/dashboard/StatCard.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BD6aUi4i.js",
    "name": "StatCard",
    "src": "components/dashboard/StatCard.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/admin.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "COUzQLYt.js",
    "name": "admin",
    "src": "layouts/admin.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DqjI2rGC.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_D0FjqKqG.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "admin.BM9C9Em8.css": {
    "file": "admin.BM9C9Em8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "layouts/empty.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CM36AUMK.js",
    "name": "empty",
    "src": "layouts/empty.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/error.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BmcZM3jl.js",
    "name": "error",
    "src": "layouts/error.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "middleware/admin-auth.ts": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "7wtxnzhR.js",
    "name": "admin-auth",
    "src": "middleware/admin-auth.ts",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "middleware/auth.ts": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CpZ_iGkp.js",
    "name": "auth",
    "src": "middleware/auth.ts",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "middleware/content-permission.ts": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BzhcG9LO.js",
    "name": "content-permission",
    "src": "middleware/content-permission.ts",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "middleware/guest.ts": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CrsPXLHN.js",
    "name": "guest",
    "src": "middleware/guest.ts",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "middleware/superadmin.ts": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DGbkuXTG.js",
    "name": "superadmin",
    "src": "middleware/superadmin.ts",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/nuxt/dist/app/components/error-404.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DBdL1Sjp.js",
    "name": "error-404",
    "src": "node_modules/nuxt/dist/app/components/error-404.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DqjI2rGC.js",
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "error-404.4oxyXxx0.css": {
    "file": "error-404.4oxyXxx0.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/components/error-500.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BFB8Lm3h.js",
    "name": "error-500",
    "src": "node_modules/nuxt/dist/app/components/error-500.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "error-500.CZqNkBuR.css": {
    "file": "error-500.CZqNkBuR.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "qi-a-hqW.js",
    "name": "entry",
    "src": "node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "isDynamicEntry": true,
    "dynamicImports": [
      "middleware/admin-auth.ts",
      "middleware/auth.ts",
      "middleware/content-permission.ts",
      "middleware/guest.ts",
      "middleware/superadmin.ts",
      "layouts/admin.vue",
      "layouts/empty.vue",
      "layouts/error.vue",
      "node_modules/nuxt/dist/app/components/error-404.vue",
      "node_modules/nuxt/dist/app/components/error-500.vue"
    ],
    "css": [
      "entry.DXiz2RTu.css"
    ],
    "_globalCSS": true
  },
  "entry.DXiz2RTu.css": {
    "file": "entry.DXiz2RTu.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/activities.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CVKo7Eal.js",
    "name": "activities",
    "src": "pages/admin/activities.vue",
    "isDynamicEntry": true,
    "imports": [
      "_CxzfvvNm.js",
      "_oUsj6fiR.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/audio.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CuQ6fOUL.js",
    "name": "audio",
    "src": "pages/admin/audio.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js"
    ]
  },
  "pages/admin/content/edit/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "O-FvJRYQ.js",
    "name": "_id_",
    "src": "pages/admin/content/edit/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_tJcwIiS2.js",
      "_CxzfvvNm.js",
      "_oUsj6fiR.js",
      "_47Na0wgx.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/content/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BmGJv__x.js",
    "name": "index",
    "src": "pages/admin/content/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_tJcwIiS2.js",
      "_oUsj6fiR.js",
      "_CxzfvvNm.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/content/new.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DWmyyj19.js",
    "name": "new",
    "src": "pages/admin/content/new.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_tJcwIiS2.js",
      "_oUsj6fiR.js",
      "_47Na0wgx.js",
      "_CxzfvvNm.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "new.1O9gAi9s.css": {
    "file": "new.1O9gAi9s.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/content/preview/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DDmPhIbq.js",
    "name": "_id_",
    "src": "pages/admin/content/preview/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js",
      "_oUsj6fiR.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js"
    ],
    "css": []
  },
  "_id_.VwyI97y2.css": {
    "file": "_id_.VwyI97y2.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/content/preview/id.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BCL8QiX0.js",
    "name": "id",
    "src": "pages/admin/content/preview/id.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js",
      "_oUsj6fiR.js",
      "_x_rD_Ya3.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "id.DNfpYM1W.css": {
    "file": "id.DNfpYM1W.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/course-packages.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DC45JndY.js",
    "name": "course-packages",
    "src": "pages/admin/course-packages.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_CxzfvvNm.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/courses.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DX1gc3BO.js",
    "name": "courses",
    "src": "pages/admin/courses.vue",
    "isDynamicEntry": true,
    "imports": [
      "_oUsj6fiR.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DgOJsROy.js",
      "_CxzfvvNm.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_D0FjqKqG.js",
      "_x_rD_Ya3.js"
    ],
    "css": []
  },
  "courses.BwCB2lK-.css": {
    "file": "courses.BwCB2lK-.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/dashboard.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "yvXxMCrY.js",
    "name": "dashboard",
    "src": "pages/admin/dashboard.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_CxzfvvNm.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ],
    "dynamicImports": [
      "components/dashboard/StatCard.vue"
    ]
  },
  "pages/admin/database.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DZxtrXBg.js",
    "name": "database",
    "src": "pages/admin/database.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/frontend-settings.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BEyI4QXP.js",
    "name": "frontend-settings",
    "src": "pages/admin/frontend-settings.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js"
    ],
    "css": []
  },
  "frontend-settings.BMcEfzFv.css": {
    "file": "frontend-settings.BMcEfzFv.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/images.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BfCLnaqw.js",
    "name": "images",
    "src": "pages/admin/images.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Co1TOLpw.js",
    "name": "index",
    "src": "pages/admin/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/login.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CXmYIQ9G.js",
    "name": "login",
    "src": "pages/admin/login.vue",
    "isDynamicEntry": true,
    "imports": [
      "_eAbK7LvP.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/logs.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DCHI5jcE.js",
    "name": "logs",
    "src": "pages/admin/logs.vue",
    "isDynamicEntry": true,
    "imports": [
      "_eAbK7LvP.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_CxzfvvNm.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js"
    ],
    "css": []
  },
  "logs.CTmxC5nf.css": {
    "file": "logs.CTmxC5nf.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/messages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Vyizhu7m.js",
    "name": "index",
    "src": "pages/admin/messages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_BMvTVg1A.js",
      "_oUsj6fiR.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js"
    ],
    "css": []
  },
  "index.wlaSsRJ8.css": {
    "file": "index.wlaSsRJ8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/reset.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "lrjd3tiF.js",
    "name": "reset",
    "src": "pages/admin/reset.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/unauthorized.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DhtHeO2S.js",
    "name": "unauthorized",
    "src": "pages/admin/unauthorized.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js"
    ],
    "css": []
  },
  "unauthorized.DVm4v8We.css": {
    "file": "unauthorized.DVm4v8We.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/users.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CNtCxq2z.js",
    "name": "users",
    "src": "pages/admin/users.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_oUsj6fiR.js",
      "_CxzfvvNm.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js",
      "_DlAUqK2U.js"
    ]
  },
  "pages/admin/users/[id]/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DIK2epto.js",
    "name": "edit",
    "src": "pages/admin/users/[id]/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/users/[id]/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CuHGhcKx.js",
    "name": "index",
    "src": "pages/admin/users/[id]/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/users/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dqfi7Vkd.js",
    "name": "index",
    "src": "pages/admin/users/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js",
      "_oUsj6fiR.js",
      "_DlAUqK2U.js",
      "_DqjI2rGC.js",
      "_eAbK7LvP.js"
    ],
    "css": []
  },
  "index.CQvy_Vka.css": {
    "file": "index.CQvy_Vka.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/content/debug.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BZXsH3h9.js",
    "name": "debug",
    "src": "pages/content/debug.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js"
    ]
  },
  "pages/content/edit/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DyHhmlLV.js",
    "name": "_id_",
    "src": "pages/content/edit/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/content/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Bg_Gmw6k.js",
    "name": "index",
    "src": "pages/content/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CxzfvvNm.js"
    ]
  },
  "pages/course/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BLsUNvJB.js",
    "name": "index",
    "src": "pages/course/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DgOJsROy.js",
      "_CxzfvvNm.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_D0FjqKqG.js",
      "_x_rD_Ya3.js"
    ]
  },
  "pages/messages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BWqKwFVc.js",
    "name": "index",
    "src": "pages/messages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_BMvTVg1A.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "index.xkZUUGQ0.css": {
    "file": "index.xkZUUGQ0.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/unauthorized.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CsVZzAWa.js",
    "name": "unauthorized",
    "src": "pages/unauthorized.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
