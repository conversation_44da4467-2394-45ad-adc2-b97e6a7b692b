import { defineComponent, ref, watch, computed, mergeProps, unref, reactive, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderAttr, ssrR<PERSON>List, ssrRenderComponent, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderClass, ssrRenderStyle } from 'vue/server-renderer';
import { b as useGlobalLoading, a as useToast } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import { a as api } from './api-BjJAe0gw.mjs';
import { L as LoadingSpinner } from './LoadingSpinner-Bp5Uxtv1.mjs';

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "CoverModal",
  __ssrInlineRender: true,
  props: {
    course: {}
  },
  emits: ["close", "updated"],
  setup(__props, { emit: __emit }) {
    useToast();
    const previewUrl = ref("");
    ref(null);
    const uploading = ref(false);
    const courseTitle = ref("");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" }, _attrs))}><div class="bg-white p-6 rounded-lg shadow-lg w-96"><h2 class="text-lg font-bold mb-4">\u7F16\u8F91\u8BFE\u7A0B\u4FE1\u606F</h2><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u8BFE\u7A0B\u6807\u9898</label><input${ssrRenderAttr("value", courseTitle.value)} type="text" placeholder="\u8F93\u5165\u8BFE\u7A0B\u6807\u9898" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div><div class="mb-4">`);
      if (previewUrl.value) {
        _push(`<div class="mb-2"><img${ssrRenderAttr("src", previewUrl.value)} class="w-full h-40 object-cover rounded"></div>`);
      } else if (_ctx.course.cover) {
        _push(`<div class="mb-2"><img${ssrRenderAttr("src", _ctx.course.cover)} class="w-full h-40 object-cover rounded"></div>`);
      } else {
        _push(`<div class="w-full h-40 bg-gray-200 flex items-center justify-center rounded mb-2"><span class="text-gray-500">\u65E0\u5C01\u9762</span></div>`);
      }
      _push(`</div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u9009\u62E9\u56FE\u7247</label><input type="file" accept="image/*" class="w-full"></div><div class="flex justify-end space-x-2"><button class="btn">\u53D6\u6D88</button><button class="btn btn-primary"${ssrIncludeBooleanAttr(uploading.value) ? " disabled" : ""}>${ssrInterpolate(uploading.value ? "\u4E0A\u4F20\u4E2D..." : "\u4FDD\u5B58")}</button></div></div></div>`);
    };
  }
});
const _sfc_setup$7 = _sfc_main$7.setup;
_sfc_main$7.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/CoverModal.vue");
  return _sfc_setup$7 ? _sfc_setup$7(props, ctx) : void 0;
};
const _sfc_main$6 = {
  __name: "AudioReuseInfo",
  __ssrInlineRender: true,
  props: {
    statement: {
      type: Object,
      required: true
    },
    // 从父组件接收复用信息
    reuseInfo: {
      type: Object,
      default: () => ({
        reused: false,
        audioFiles: {},
        source: null
      })
    }
  },
  setup(__props) {
    const props = __props;
    const showDetails = ref(false);
    const reused = computed(() => props.reuseInfo.reused);
    const source = computed(() => props.reuseInfo.source);
    const audioFiles = computed(() => props.reuseInfo.audioFiles || {});
    const getVoiceLabel = (key) => {
      const labels = {
        enUsMale: "\u7F8E\u7537",
        enUsFemale: "\u7F8E\u5973",
        enGbMale: "\u82F1\u7537",
        enGbFemale: "\u82F1\u5973"
      };
      return labels[key] || key;
    };
    const getFullAudioUrl = (url) => {
      if (!url) return "";
      if (url.startsWith("http")) return url;
      const fullUrl = `http://localhost:5000${url}`;
      console.log("\u{1F3B5} \u97F3\u9891URL:", fullUrl);
      return fullUrl;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "audio-reuse-info" }, _attrs))} data-v-a74abc8f>`);
      if (unref(reused)) {
        _push(`<div class="text-purple-600 text-sm" data-v-a74abc8f><div class="flex items-center mb-1" data-v-a74abc8f><span class="mr-1" data-v-a74abc8f>\u{1F504}</span><span data-v-a74abc8f>\u590D\u7528\u97F3\u9891 (\u6765\u6E90: ${ssrInterpolate(unref(source).courseTitle)})</span></div>`);
        if (showDetails.value) {
          _push(`<div class="mt-2 space-y-1 text-xs" data-v-a74abc8f><!--[-->`);
          ssrRenderList(unref(audioFiles), (url, type) => {
            _push(`<div class="flex items-center" data-v-a74abc8f><span class="w-10" data-v-a74abc8f>${ssrInterpolate(getVoiceLabel(type))}:</span>`);
            if (url) {
              _push(`<audio${ssrRenderAttr("src", getFullAudioUrl(url))} controls preload="metadata" class="h-8 w-32" style="${ssrRenderStyle({ "min-height": "32px", "min-width": "128px" })}" data-v-a74abc8f></audio>`);
            } else {
              _push(`<span class="text-gray-400" data-v-a74abc8f>\u65E0\u97F3\u9891</span>`);
            }
            _push(`</div>`);
          });
          _push(`<!--]--><div class="mt-1 text-xs text-gray-500" data-v-a74abc8f><span data-v-a74abc8f>\u6765\u6E90\u8BED\u53E5ID: ${ssrInterpolate(unref(source).id)}</span></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<button class="text-xs text-blue-500 hover:underline mt-1" data-v-a74abc8f>${ssrInterpolate(showDetails.value ? "\u9690\u85CF\u8BE6\u60C5" : "\u67E5\u770B\u8BE6\u60C5")}</button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
};
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/AudioReuseInfo.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const AudioReuseInfo = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["__scopeId", "data-v-a74abc8f"]]);
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "SimpleAudioControls",
  __ssrInlineRender: true,
  props: {
    statement: {},
    courseId: {}
  },
  emits: ["audio-generated"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const generating = ref(false);
    ref(false);
    const showAudioList = ref(false);
    const audioStatus = ref(props.statement.audioStatus || "pending");
    const audioFiles = ref({
      enUsMale: props.statement.audioEnUsMale || null,
      enUsFemale: props.statement.audioEnUsFemale || null,
      enGbMale: props.statement.audioEnGbMale || null,
      enGbFemale: props.statement.audioEnGbFemale || null
    });
    const getStatusText = (status) => {
      switch (status) {
        case "completed":
          return "\u5DF2\u751F\u6210";
        case "generating":
          return "\u751F\u6210\u4E2D";
        case "failed":
          return "\u751F\u6210\u5931\u8D25";
        default:
          return "\u672A\u751F\u6210";
      }
    };
    const getStatusClass = (status) => {
      switch (status) {
        case "completed":
          return "text-green-600";
        case "generating":
          return "text-blue-600";
        case "failed":
          return "text-red-600";
        default:
          return "text-gray-600";
      }
    };
    const getVoiceLabel = (key) => {
      const labels = {
        enUsMale: "\u7F8E\u7537",
        enUsFemale: "\u7F8E\u5973",
        enGbMale: "\u82F1\u7537",
        enGbFemale: "\u82F1\u5973"
      };
      return labels[key] || key;
    };
    const initializeAudioStatus = () => {
      if (props.statement) {
        audioStatus.value = props.statement.audioStatus || "pending";
        if (props.statement.audioStatus === "completed") {
          audioFiles.value = {
            enUsMale: props.statement.audioEnUsMale || null,
            enUsFemale: props.statement.audioEnUsFemale || null,
            enGbMale: props.statement.audioEnGbMale || null,
            enGbFemale: props.statement.audioEnGbFemale || null
          };
          console.log("\u{1F3B5} \u97F3\u9891\u6587\u4EF6\u72B6\u6001:");
          Object.entries(audioFiles.value).forEach(([key, url]) => {
            console.log(`  ${getVoiceLabel(key)}: ${url ? "\u2705 \u6709\u6587\u4EF6" : "\u274C \u65E0\u6587\u4EF6"} ${url || ""}`);
          });
          showAudioList.value = true;
        }
      }
    };
    watch(() => props.statement, (newStatement) => {
      if (newStatement) {
        initializeAudioStatus();
      }
    }, { immediate: true, deep: true });
    watch(() => props.statement.english, (newEnglish) => {
      if (!newEnglish) {
        audioStatus.value = "pending";
        audioFiles.value = {
          enUsMale: null,
          enUsFemale: null,
          enGbMale: null,
          enGbFemale: null
        };
        showAudioList.value = false;
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "simple-audio-controls" }, _attrs))} data-v-9bf431db><div class="flex items-center space-x-2 mb-1" data-v-9bf431db><span class="${ssrRenderClass([getStatusClass(audioStatus.value), "text-xs"])}" data-v-9bf431db>${ssrInterpolate(getStatusText(audioStatus.value))}</span>`);
      if (audioStatus.value === "completed") {
        _push(`<button class="btn btn-xs btn-outline" data-v-9bf431db>${ssrInterpolate(showAudioList.value ? "\u9690\u85CF" : "\u8BD5\u542C")}</button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
      _push(ssrRenderComponent(AudioReuseInfo, { statement: _ctx.statement }, null, _parent));
      if (showAudioList.value && audioStatus.value === "completed") {
        _push(`<div class="space-y-1 mb-2 p-2 bg-gray-50 rounded" data-v-9bf431db><!--[-->`);
        ssrRenderList(audioFiles.value, (audio, key) => {
          _push(`<div class="flex items-center space-x-2" data-v-9bf431db><span class="text-xs text-gray-600 w-12" data-v-9bf431db>${ssrInterpolate(getVoiceLabel(key))}</span>`);
          if (audio) {
            _push(`<audio${ssrRenderAttr("src", audio)} controls preload="metadata" class="h-6 text-xs flex-1" data-v-9bf431db> \u60A8\u7684\u6D4F\u89C8\u5668\u4E0D\u652F\u6301\u97F3\u9891\u64AD\u653E </audio>`);
          } else {
            _push(`<span class="text-xs text-gray-400 flex-1" data-v-9bf431db>\u672A\u751F\u6210</span>`);
          }
          _push(`</div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex space-x-1" data-v-9bf431db><button${ssrIncludeBooleanAttr(generating.value || !_ctx.statement.english) ? " disabled" : ""} class="btn btn-xs btn-primary" data-v-9bf431db>${ssrInterpolate(generating.value ? "\u751F\u6210\u4E2D..." : "\u751F\u6210\u97F3\u9891")}</button>`);
      if (audioStatus.value === "completed") {
        _push(`<button class="btn btn-xs btn-error" data-v-9bf431db> \u5220\u9664 </button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/SimpleAudioControls.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const SimpleAudioControls = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["__scopeId", "data-v-9bf431db"]]);
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "StatementModal",
  __ssrInlineRender: true,
  props: {
    course: {},
    statements: {}
  },
  emits: ["close", "updated"],
  setup(__props, { emit: __emit }) {
    const editingStatements = ref([]);
    const saving = ref(false);
    const showTemplateMenu = ref(false);
    const generatingAudio = ref(false);
    const onAudioGenerated = (statementData) => {
      console.log("\u97F3\u9891\u751F\u6210\u5B8C\u6210:", statementData);
      if (statementData && statementData.id) {
        const statementIndex = editingStatements.value.findIndex((s) => s.id === statementData.id);
        if (statementIndex !== -1) {
          editingStatements.value[statementIndex].audioStatus = statementData.audioStatus || "completed";
          if (statementData.audioFiles) {
            editingStatements.value[statementIndex].audioEnUsMale = statementData.audioFiles.enUsMale;
            editingStatements.value[statementIndex].audioEnUsFemale = statementData.audioFiles.enUsFemale;
            editingStatements.value[statementIndex].audioEnGbMale = statementData.audioFiles.enGbMale;
            editingStatements.value[statementIndex].audioEnGbFemale = statementData.audioFiles.enGbFemale;
          }
          console.log("\u5DF2\u66F4\u65B0\u8BED\u53E5\u97F3\u9891\u72B6\u6001\u548C\u6587\u4EF6");
        }
      }
    };
    const onAudioDeleted = (deleteInfo) => {
      console.log("\u{1F5D1}\uFE0F \u97F3\u9891\u5220\u9664\u4E8B\u4EF6:", deleteInfo);
      if (deleteInfo && deleteInfo.statementId) {
        const statementIndex = editingStatements.value.findIndex((s) => s.id === deleteInfo.statementId);
        if (statementIndex !== -1) {
          editingStatements.value[statementIndex].audioStatus = "pending";
          editingStatements.value[statementIndex].audioEnUsMale = null;
          editingStatements.value[statementIndex].audioEnUsFemale = null;
          editingStatements.value[statementIndex].audioEnGbMale = null;
          editingStatements.value[statementIndex].audioEnGbFemale = null;
          editingStatements.value[statementIndex].audioGeneratedAt = null;
          console.log("\u2705 \u5DF2\u6E05\u9664\u8BED\u53E5\u97F3\u9891\u72B6\u6001\u548C\u6587\u4EF6");
        }
      }
      if (deleteInfo.deletedFiles > 0 || deleteInfo.affectedStatements > 0) {
        console.log("\u{1F504} \u66F4\u65B0\u6240\u6709\u53D7\u5F71\u54CD\u7684\u8BED\u53E5\u72B6\u6001");
        if (deleteInfo.affectedStatementIds && deleteInfo.affectedStatementIds.length > 0) {
          deleteInfo.affectedStatementIds.forEach((statementId) => {
            const statement = editingStatements.value.find((s) => s.id === statementId);
            if (statement) {
              statement.audioStatus = "pending";
              statement.audioEnUsMale = null;
              statement.audioEnUsFemale = null;
              statement.audioEnGbMale = null;
              statement.audioEnGbFemale = null;
              statement.audioGeneratedAt = null;
              console.log(`\u2705 \u5DF2\u66F4\u65B0\u8BED\u53E5 ${statement.id} \u7684\u72B6\u6001\u4E3Apending`);
            }
          });
        } else {
          const englishText = deleteInfo.english;
          if (englishText) {
            editingStatements.value.forEach((statement) => {
              if (statement.english === englishText) {
                statement.audioStatus = "pending";
                statement.audioEnUsMale = null;
                statement.audioEnUsFemale = null;
                statement.audioEnGbMale = null;
                statement.audioEnGbFemale = null;
                statement.audioGeneratedAt = null;
                console.log(`\u2705 \u5DF2\u66F4\u65B0\u8BED\u53E5 ${statement.id} \u7684\u72B6\u6001\u4E3Apending`);
              }
            });
          }
        }
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" }, _attrs))}><div class="bg-white rounded-lg shadow-lg w-4/5 max-w-4xl max-h-[90vh] flex flex-col"><div class="flex-shrink-0 p-6 pb-4 border-b border-gray-200"><div class="flex justify-between items-center mb-4"><h2 class="text-lg font-bold">\u7F16\u8F91\u8BFE\u7A0B\u8BED\u53E5</h2><div class="flex space-x-2"><button class="btn btn-sm btn-secondary">\u6DFB\u52A0\u8BED\u53E5</button><button class="btn btn-sm btn-info"${ssrIncludeBooleanAttr(generatingAudio.value) ? " disabled" : ""}>${ssrInterpolate(generatingAudio.value ? "\u751F\u6210\u4E2D..." : "\u6279\u91CF\u751F\u6210\u97F3\u9891")}</button><div class="relative"><button class="btn btn-sm btn-outline"> \u4E0B\u8F7D\u6A21\u677F <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button>`);
      if (showTemplateMenu.value) {
        _push(`<div class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10"><div class="py-1"><a href="/excel/course-import-template.xlsx" download class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Excel\u6A21\u677F (.xlsx)</a><a href="/excel/course-import-template.csv" download class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">CSV\u6A21\u677F (.csv)</a><a href="/excel/course-import-template.txt" download class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">\u6587\u672C\u6A21\u677F (.txt)</a><div class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100 cursor-pointer">\u67E5\u770B\u683C\u5F0F\u8BF4\u660E</div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div></div><div class="flex-1 overflow-auto px-6 py-4">`);
      if (editingStatements.value.length === 0) {
        _push(`<div class="text-center py-4 bg-gray-50 rounded"><p>\u6682\u65E0\u8BED\u53E5\u6570\u636E\uFF0C\u8BF7\u6DFB\u52A0\u8BED\u53E5\u6216\u901A\u8FC7\u6279\u91CF\u5BFC\u5165</p></div>`);
      } else {
        _push(`<!---->`);
      }
      if (editingStatements.value.length > 0) {
        _push(`<div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">\u5E8F\u53F7</th><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u4E2D\u6587</th><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u82F1\u6587</th><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u97F3\u6807</th><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u97F3\u9891</th><th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">\u64CD\u4F5C</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
        ssrRenderList(editingStatements.value, (statement, index) => {
          _push(`<tr><td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(index + 1)}</td><td class="px-4 py-2"><input${ssrRenderAttr("value", statement.chinese)} class="w-full border rounded px-2 py-1" placeholder="\u4E2D\u6587"></td><td class="px-4 py-2"><input${ssrRenderAttr("value", statement.english)} class="w-full border rounded px-2 py-1" placeholder="\u82F1\u6587"></td><td class="px-4 py-2"><input${ssrRenderAttr("value", statement.soundmark)} class="w-full border rounded px-2 py-1" placeholder="\u97F3\u6807(\u53EF\u9009)"></td><td class="px-4 py-2">`);
          _push(ssrRenderComponent(SimpleAudioControls, {
            statement,
            courseId: _ctx.course.id,
            onAudioGenerated,
            onUpdated: onAudioGenerated,
            onAudioDeleted
          }, null, _parent));
          _push(`</td><td class="px-4 py-2 whitespace-nowrap"><button${ssrIncludeBooleanAttr(index === 0) ? " disabled" : ""} class="btn btn-xs btn-outline mr-1">\u2191</button><button${ssrIncludeBooleanAttr(index === editingStatements.value.length - 1) ? " disabled" : ""} class="btn btn-xs btn-outline mr-1">\u2193</button><button class="btn btn-xs btn-error">\u5220\u9664</button></td></tr>`);
        });
        _push(`<!--]--></tbody></table></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="flex-shrink-0 p-6 pt-4 border-t border-gray-200"><div class="flex justify-end space-x-2"><button class="btn">\u53D6\u6D88</button><button class="btn btn-primary"${ssrIncludeBooleanAttr(saving.value) ? " disabled" : ""}>${ssrInterpolate(saving.value ? "\u4FDD\u5B58\u4E2D..." : "\u4FDD\u5B58\u8BED\u53E5")}</button></div></div></div></div>`);
    };
  }
});
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/StatementModal.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const reuseCache = reactive(/* @__PURE__ */ new Map());
const isInitialized = ref(false);
const isInitializing = ref(false);
const reuseRelations = reactive(/* @__PURE__ */ new Map());
const pendingChecks = reactive(/* @__PURE__ */ new Set());
class AudioReuseManager {
  constructor() {
    __publicField(this, "CACHE_DURATION", 5 * 60 * 1e3);
    __publicField(this, "BATCH_SIZE", 100);
    __publicField(this, "BATCH_DELAY", 200);
  }
  /**
   * 初始化复用管理器
   * 一次性获取所有复用关系，避免重复检查
   */
  async initialize() {
    if (isInitialized.value || isInitializing.value) {
      return;
    }
    isInitializing.value = true;
    console.log("\u{1F680} \u521D\u59CB\u5316\u97F3\u9891\u590D\u7528\u7BA1\u7406\u5668");
    try {
      const response = await api.get("/api/admin/audio/reuse-relations");
      if (response.relations) {
        for (const relation of response.relations) {
          const key = this.getCacheKey(relation.english);
          reuseRelations.set(key, relation.sourceStatementId);
          reuseCache.set(key, {
            reused: true,
            audioFiles: relation.audioFiles,
            sourceStatement: relation.sourceStatement,
            lastChecked: Date.now(),
            verified: true
          });
        }
      }
      isInitialized.value = true;
      console.log(`\u2705 \u590D\u7528\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210\uFF0C\u52A0\u8F7D\u4E86 ${reuseRelations.size} \u4E2A\u590D\u7528\u5173\u7CFB`);
    } catch (error) {
      console.error("\u274C \u590D\u7528\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
    } finally {
      isInitializing.value = false;
    }
  }
  /**
   * 获取语句的复用状态
   * 优先从缓存获取，避免重复检查
   */
  async getReuseState(english, statementId) {
    const key = this.getCacheKey(english);
    const cached = reuseCache.get(key);
    if (cached && this.isCacheValid(cached)) {
      console.log(`\u{1F4CB} \u4ECE\u7F13\u5B58\u83B7\u53D6\u590D\u7528\u72B6\u6001: ${english}`);
      return cached;
    }
    if (pendingChecks.has(key)) {
      console.log(`\u23F3 \u590D\u7528\u68C0\u67E5\u8FDB\u884C\u4E2D: ${english}`);
      return this.waitForCheck(key);
    }
    pendingChecks.add(key);
    try {
      console.log(`\u{1F50D} \u68C0\u67E5\u590D\u7528\u72B6\u6001: ${english}`);
      const result = await this.checkReuseState(english, statementId);
      reuseCache.set(key, {
        ...result,
        lastChecked: Date.now(),
        verified: true
      });
      return result;
    } finally {
      pendingChecks.delete(key);
    }
  }
  /**
   * 批量获取复用状态
   * 用于页面初始化时的批量检查
   */
  async batchGetReuseStates(requests) {
    const results = /* @__PURE__ */ new Map();
    const needCheck = [];
    for (const request of requests) {
      const key = this.getCacheKey(request.english);
      const cached = reuseCache.get(key);
      if (cached && this.isCacheValid(cached)) {
        results.set(key, cached);
      } else {
        needCheck.push(request);
      }
    }
    if (needCheck.length > 0) {
      console.log(`\u{1F50D} \u6279\u91CF\u68C0\u67E5 ${needCheck.length} \u4E2A\u590D\u7528\u72B6\u6001`);
      const batchResults = await this.batchCheckReuseStates(needCheck);
      for (const [key, result] of batchResults) {
        reuseCache.set(key, {
          ...result,
          lastChecked: Date.now(),
          verified: true
        });
        results.set(key, result);
      }
    }
    return results;
  }
  /**
   * 智能更新复用状态
   * 当音频生成或删除时，智能更新相关的复用关系
   */
  async updateReuseRelations(english, sourceStatementId) {
    const key = this.getCacheKey(english);
    if (sourceStatementId) {
      reuseRelations.set(key, sourceStatementId);
      console.log(`\u{1F4DD} \u66F4\u65B0\u590D\u7528\u5173\u7CFB: ${english} -> ${sourceStatementId}`);
    } else {
      reuseRelations.delete(key);
      reuseCache.delete(key);
      console.log(`\u{1F5D1}\uFE0F \u5220\u9664\u590D\u7528\u5173\u7CFB: ${english}`);
    }
    this.notifyReuseChange(key);
  }
  /**
   * 清除缓存
   */
  clearCache() {
    reuseCache.clear();
    reuseRelations.clear();
    pendingChecks.clear();
    isInitialized.value = false;
    console.log("\u{1F9F9} \u6E05\u9664\u590D\u7528\u7F13\u5B58");
  }
  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalRelations: reuseRelations.size,
      cachedStates: reuseCache.size,
      pendingChecks: pendingChecks.size,
      isInitialized: isInitialized.value
    };
  }
  // 私有方法
  getCacheKey(english) {
    return english.trim().toLowerCase();
  }
  isCacheValid(cached) {
    return cached.verified && Date.now() - cached.lastChecked < this.CACHE_DURATION;
  }
  async checkReuseState(english, statementId) {
    try {
      const response = await api.post("/api/admin/audio/check-reuse", {
        english: english.trim(),
        excludeStatementId: statementId
      });
      return {
        reused: response.reused || false,
        audioFiles: response.audioFiles || {},
        sourceStatement: response.source,
        lastChecked: Date.now(),
        verified: true
      };
    } catch (error) {
      console.error("\u68C0\u67E5\u590D\u7528\u72B6\u6001\u5931\u8D25:", error);
      return {
        reused: false,
        audioFiles: {},
        lastChecked: Date.now(),
        verified: false
      };
    }
  }
  async batchCheckReuseStates(requests) {
    try {
      const response = await api.post("/api/admin/audio/batch-check-reuse", {
        requests: requests.map((req) => ({
          english: req.english.trim(),
          excludeStatementId: req.statementId
        }))
      });
      const results = /* @__PURE__ */ new Map();
      if (response.results) {
        for (const [english, result] of Object.entries(response.results)) {
          const key = this.getCacheKey(english);
          results.set(key, {
            reused: result.reused || false,
            audioFiles: result.audioFiles || {},
            sourceStatement: result.sourceStatement,
            lastChecked: Date.now(),
            verified: true
          });
        }
      }
      return results;
    } catch (error) {
      console.error("\u6279\u91CF\u68C0\u67E5\u590D\u7528\u72B6\u6001\u5931\u8D25:", error);
      return /* @__PURE__ */ new Map();
    }
  }
  async waitForCheck(key) {
    while (pendingChecks.has(key)) {
      await new Promise((resolve) => setTimeout(resolve, 50));
    }
    return reuseCache.get(key) || {
      reused: false,
      audioFiles: {},
      lastChecked: Date.now(),
      verified: false
    };
  }
  notifyReuseChange(key) {
    console.log(`\u{1F4E2} \u901A\u77E5\u590D\u7528\u72B6\u6001\u53D8\u5316: ${key}`);
  }
}
const audioReuseManager = new AudioReuseManager();
function useAudioReuseManager() {
  return {
    // 状态
    isInitialized: computed(() => isInitialized.value),
    isInitializing: computed(() => isInitializing.value),
    // 方法
    initialize: () => audioReuseManager.initialize(),
    getReuseState: (english, statementId) => audioReuseManager.getReuseState(english, statementId),
    batchGetReuseStates: (requests) => audioReuseManager.batchGetReuseStates(requests),
    updateReuseRelations: (english, sourceStatementId) => audioReuseManager.updateReuseRelations(english, sourceStatementId),
    clearCache: () => audioReuseManager.clearCache(),
    getStats: () => audioReuseManager.getStats()
  };
}
class BatchAudioReuseService {
  constructor() {
    __publicField(this, "pendingRequests", /* @__PURE__ */ new Map());
    __publicField(this, "batchQueue", []);
    __publicField(this, "batchTimeout", null);
    __publicField(this, "BATCH_SIZE", 50);
    __publicField(this, "BATCH_DELAY", 100);
  }
  // 100ms内的请求会被合并
  /**
   * 检查单个音频复用（会被自动批量处理）
   */
  async checkAudioReuse(english, excludeStatementId) {
    const key = english.trim();
    console.log(`\u{1F50D} batchAudioReuseService.checkAudioReuse: "${key}"`);
    if (this.pendingRequests.has(key)) {
      console.log(`\u267B\uFE0F \u590D\u7528\u73B0\u6709\u8BF7\u6C42: "${key}"`);
      return this.pendingRequests.get(key);
    }
    const requestPromise = new Promise((resolve, reject) => {
      this.batchQueue.push({
        english: key,
        excludeStatementId,
        resolve,
        reject
      });
      console.log(`\u{1F4DD} \u6DFB\u52A0\u5230\u6279\u91CF\u961F\u5217: "${key}", \u961F\u5217\u957F\u5EA6: ${this.batchQueue.length}`);
      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
        console.log(`\u23F0 \u6E05\u9664\u65E7\u7684\u6279\u91CF\u5904\u7406\u5B9A\u65F6\u5668`);
      }
      console.log(`\u23F0 \u8BBE\u7F6E\u6279\u91CF\u5904\u7406\u5B9A\u65F6\u5668: ${this.BATCH_DELAY}ms`);
      this.batchTimeout = setTimeout(() => {
        console.log(`\u{1F680} \u6279\u91CF\u5904\u7406\u5B9A\u65F6\u5668\u89E6\u53D1`);
        this.processBatch();
      }, this.BATCH_DELAY);
    });
    this.pendingRequests.set(key, requestPromise);
    console.log(`\u{1F4BE} \u4FDD\u5B58\u8BF7\u6C42Promise: "${key}", \u603B\u8BF7\u6C42\u6570: ${this.pendingRequests.size}`);
    return requestPromise;
  }
  /**
   * 处理批量请求
   */
  async processBatch() {
    if (this.batchQueue.length === 0) {
      return;
    }
    const currentBatch = this.batchQueue.splice(0, this.BATCH_SIZE);
    console.log(`\u{1F50D} \u6279\u91CF\u5904\u7406\u97F3\u9891\u590D\u7528\u68C0\u67E5\uFF0C\u5171 ${currentBatch.length} \u4E2A\u8BF7\u6C42`);
    try {
      const response = await api.post("/api/admin/audio/batch-check-reuse", {
        requests: currentBatch.map((req) => ({
          english: req.english,
          excludeStatementId: req.excludeStatementId
        }))
      });
      const results = /* @__PURE__ */ new Map();
      if (response && response.results) {
        for (const [englishText, result] of Object.entries(response.results)) {
          results.set(englishText, result);
        }
      }
      for (const request of currentBatch) {
        const key = request.english.trim();
        const result = results.get(key);
        if (request.resolve) {
          if (result) {
            request.resolve(result);
          } else {
            request.resolve({ reused: false, audioFiles: {} });
          }
        }
        this.pendingRequests.delete(key);
      }
      console.log(`\u2705 \u6279\u91CF\u68C0\u67E5\u5B8C\u6210\uFF0C\u627E\u5230 ${Array.from(results.values()).filter((r) => r.reused).length} \u4E2A\u53EF\u590D\u7528\u97F3\u9891`);
    } catch (error) {
      console.error("\u6279\u91CF\u97F3\u9891\u590D\u7528\u68C0\u67E5\u5931\u8D25:", error);
      for (const request of currentBatch) {
        if (request.reject) {
          request.reject(error);
        }
        this.pendingRequests.delete(request.english.trim());
      }
    }
  }
  /**
   * 清理所有待处理的请求
   */
  clearPendingRequests() {
    this.pendingRequests.clear();
    this.batchQueue = [];
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
  }
  /**
   * 获取当前待处理请求数量
   */
  getPendingCount() {
    return this.pendingRequests.size + this.batchQueue.length;
  }
}
const batchAudioReuseService = new BatchAudioReuseService();
const CACHE_DURATION = 2 * 60 * 1e3;
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "AudioControls",
  __ssrInlineRender: true,
  props: {
    statement: {},
    courseId: {}
  },
  emits: ["audio-generated", "updated", "audio-deleted"],
  setup(__props, { emit: __emit }) {
    const globalReuseCache = /* @__PURE__ */ new Map();
    const props = __props;
    const globalLoading = useGlobalLoading();
    const generating = ref(false);
    const showAudioList = ref(false);
    const reuseSource = ref(null);
    const audioStatus = ref("pending");
    const audioFiles = ref({
      enUsMale: null,
      enUsFemale: null,
      enGbMale: null,
      enGbFemale: null
    });
    const statusClass = computed(() => {
      if (hasAvailableAudio.value) {
        return "text-green-600";
      }
      switch (audioStatus.value) {
        case "generating":
          return "text-blue-600";
        case "failed":
          return "text-red-600";
        case "reusable":
          return "text-purple-600";
        default:
          return "text-gray-600";
      }
    });
    const statusText = computed(() => {
      var _a;
      if (hasAvailableAudio.value) {
        if (audioStatus.value === "completed") {
          if (isReusedAudio.value) {
            return `\u5DF2\u751F\u6210 (\u590D\u7528)`;
          }
          return "\u5DF2\u751F\u6210";
        } else if (reuseSource.value !== null) {
          return `\u5DF2\u751F\u6210 (\u590D\u7528)`;
        }
      }
      switch (audioStatus.value) {
        case "generating":
          return "\u751F\u6210\u4E2D";
        case "failed":
          return "\u751F\u6210\u5931\u8D25";
        case "reusable":
          return `\u53EF\u590D\u7528 (\u6765\u6E90: ${((_a = reuseSource.value) == null ? void 0 : _a.courseTitle) || "\u5176\u4ED6\u8BFE\u7A0B"})`;
        default:
          return "\u672A\u751F\u6210";
      }
    });
    const isReusedAudio = computed(() => {
      return reuseSource.value !== null;
    });
    const hasAvailableAudio = computed(() => {
      if (audioStatus.value === "completed") {
        return true;
      }
      if (reuseSource.value !== null) {
        return true;
      }
      return false;
    });
    const getButtonText = () => {
      if (generating.value) return "\u751F\u6210\u4E2D...";
      if (isReusedAudio.value && audioStatus.value === "pending") {
        return "\u751F\u6210\u65B0\u97F3\u9891";
      }
      if (audioStatus.value === "completed") {
        return "\u91CD\u65B0\u751F\u6210";
      }
      switch (audioStatus.value) {
        case "reusable":
          return "\u91CD\u65B0\u751F\u6210";
        default:
          return "\u751F\u6210\u97F3\u9891";
      }
    };
    const getVoiceLabel = (key) => {
      const labels = {
        enUsMale: "\u7F8E\u7537",
        enUsFemale: "\u7F8E\u5973",
        enGbMale: "\u82F1\u7537",
        enGbFemale: "\u82F1\u5973"
      };
      return labels[key] || key;
    };
    const checkingReuse = ref(false);
    const initializing = ref(false);
    let initializationTimer = null;
    const checkAudioReuse = async () => {
      if (!props.statement.english || !props.statement.english.trim()) {
        console.log(`\u274C \u8DF3\u8FC7\u590D\u7528\u68C0\u67E5 - \u82F1\u6587\u5185\u5BB9\u4E3A\u7A7A: ${props.statement.id}`);
        return;
      }
      if (checkingReuse.value) {
        console.log(`\u274C \u8DF3\u8FC7\u590D\u7528\u68C0\u67E5 - \u6B63\u5728\u68C0\u67E5\u4E2D: ${props.statement.id}`);
        return;
      }
      const cacheKey = props.statement.english.trim().toLowerCase();
      const cached = globalReuseCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        console.log(`\u{1F4CB} \u4ECE\u7F13\u5B58\u83B7\u53D6\u590D\u7528\u72B6\u6001: ${props.statement.id} - "${props.statement.english}"`);
        applyReuseResult(cached.result);
        return;
      }
      console.log(`\u{1F50D} \u5F00\u59CB\u68C0\u67E5\u590D\u7528: ${props.statement.id} - "${props.statement.english}"`);
      checkingReuse.value = true;
      try {
        console.log(`\u{1F4DE} \u8C03\u7528\u6279\u91CF\u590D\u7528\u670D\u52A1: ${props.statement.english}`);
        const response = await batchAudioReuseService.checkAudioReuse(
          props.statement.english,
          props.statement.id
        );
        console.log(`\u{1F4DE} \u6279\u91CF\u590D\u7528\u670D\u52A1\u54CD\u5E94:`, response);
        console.log(`\u{1F50D} \u54CD\u5E94\u8BE6\u60C5 - reused: ${response == null ? void 0 : response.reused}, audioFiles:`, response == null ? void 0 : response.audioFiles);
        globalReuseCache.set(cacheKey, {
          result: response,
          timestamp: Date.now()
        });
        applyReuseResult(response);
      } catch (error) {
        console.error("\u68C0\u67E5\u97F3\u9891\u590D\u7528\u5931\u8D25:", error);
        if (!initializing.value) {
          reuseSource.value = null;
        }
      } finally {
        checkingReuse.value = false;
      }
    };
    const hasAudioInDatabase = (statement) => {
      if (!statement) return false;
      const hasAnyAudio = statement.audioEnUsMale || statement.audioEnUsFemale || statement.audioEnGbMale || statement.audioEnGbFemale;
      console.log(`\u{1F50D} \u68C0\u67E5\u6570\u636E\u5E93\u97F3\u9891: ${statement.id}, \u6709\u97F3\u9891: ${!!hasAnyAudio}`);
      return !!hasAnyAudio;
    };
    const applyReuseResult = (response) => {
      var _a, _b;
      if (response && response.reused) {
        const responseAudioFiles = response.audioFiles || {};
        const hasValidAudio = Object.values(responseAudioFiles).some((url) => url);
        if (hasValidAudio) {
          console.log(`\u2705 \u627E\u5230\u53EF\u590D\u7528\u97F3\u9891: ${props.statement.id}`);
          console.log(`   \u6765\u6E90: ${(_a = response.sourceStatement) == null ? void 0 : _a.courseTitle}`);
          console.log(`   \u82F1\u6587: "${(_b = response.sourceStatement) == null ? void 0 : _b.english}"`);
          audioFiles.value = {
            enUsMale: responseAudioFiles.enUsMale || null,
            enUsFemale: responseAudioFiles.enUsFemale || null,
            enGbMale: responseAudioFiles.enGbMale || null,
            enGbFemale: responseAudioFiles.enGbFemale || null
          };
          reuseSource.value = response.sourceStatement;
          audioStatus.value = "pending";
          showAudioList.value = false;
        } else {
          console.log(`\u274C \u590D\u7528\u97F3\u9891\u65E0\u6548\uFF08\u65E0\u97F3\u9891\u6587\u4EF6\uFF09: ${props.statement.id}`);
          reuseSource.value = null;
        }
      } else {
        console.log(`\u274C \u672A\u627E\u5230\u53EF\u590D\u7528\u97F3\u9891: ${props.statement.id}`);
        reuseSource.value = null;
      }
    };
    const initializeAudioStatus = async () => {
      var _a, _b, _c, _d, _e;
      if (initializationTimer) {
        clearTimeout(initializationTimer);
      }
      if (initializing.value) {
        console.log(`\u26A0\uFE0F \u7EC4\u4EF6 ${(_a = props.statement) == null ? void 0 : _a.id} \u6B63\u5728\u521D\u59CB\u5316\u4E2D\uFF0C\u8DF3\u8FC7\u91CD\u590D\u521D\u59CB\u5316`);
        return;
      }
      initializing.value = true;
      console.log(`\u{1F680} \u5F00\u59CB\u521D\u59CB\u5316\u7EC4\u4EF6: ${(_b = props.statement) == null ? void 0 : _b.id}`);
      reuseSource.value = null;
      showAudioList.value = false;
      const loadingKey = `audio-init-${((_c = props.statement) == null ? void 0 : _c.id) || "new"}`;
      globalLoading.showLoading({
        title: "\u68C0\u67E5\u97F3\u9891\u72B6\u6001",
        message: "\u6B63\u5728\u68C0\u67E5\u8BED\u53E5\u7684\u97F3\u9891\u72B6\u6001\uFF0C\u8BF7\u7A0D\u5019",
        showProgress: true,
        key: loadingKey
      });
      try {
        globalLoading.updateProgress(10);
        await new Promise((resolve) => setTimeout(resolve, 100));
        globalLoading.updateProgress(30);
        if (!props.statement) {
          audioStatus.value = "pending";
          audioFiles.value = {
            enUsMale: null,
            enUsFemale: null,
            enGbMale: null,
            enGbFemale: null
          };
          globalLoading.updateProgress(100);
          return;
        }
        globalLoading.updateProgress(50);
        if (props.statement.audioStatus === "completed" && hasAudioInDatabase(props.statement)) {
          console.log(`\u{1F4BE} \u4ECE\u6570\u636E\u5E93\u52A0\u8F7D\u97F3\u9891: ${props.statement.id}`);
          audioStatus.value = "completed";
          audioFiles.value = {
            enUsMale: props.statement.audioEnUsMale || null,
            enUsFemale: props.statement.audioEnUsFemale || null,
            enGbMale: props.statement.audioEnGbMale || null,
            enGbFemale: props.statement.audioEnGbFemale || null
          };
          reuseSource.value = null;
          showAudioList.value = false;
          globalLoading.updateProgress(100);
        } else if (props.statement.audioStatus === "generating") {
          console.log(`\u23F3 \u97F3\u9891\u751F\u6210\u4E2D: ${props.statement.id}`);
          audioStatus.value = "generating";
          audioFiles.value = {
            enUsMale: null,
            enUsFemale: null,
            enGbMale: null,
            enGbFemale: null
          };
          reuseSource.value = null;
          showAudioList.value = false;
          globalLoading.updateProgress(100);
        } else {
          console.log(`\u{1F50D} \u6570\u636E\u5E93\u65E0\u97F3\u9891\uFF0C\u68C0\u67E5\u590D\u7528: ${props.statement.id}`);
          globalLoading.updateProgress(70);
          audioStatus.value = "pending";
          audioFiles.value = {
            enUsMale: null,
            enUsFemale: null,
            enGbMale: null,
            enGbFemale: null
          };
          reuseSource.value = null;
          showAudioList.value = false;
          if (props.statement.english && props.statement.english.trim()) {
            try {
              globalLoading.updateProgress(90);
              await checkAudioReuse();
            } catch (reuseError) {
              console.error(`\u274C \u590D\u7528\u68C0\u67E5\u5931\u8D25: ${props.statement.id}`, reuseError);
            }
          }
          globalLoading.updateProgress(100);
        }
      } catch (error) {
        console.error(`\u274C \u97F3\u9891\u72B6\u6001\u521D\u59CB\u5316\u5931\u8D25: ${(_d = props.statement) == null ? void 0 : _d.id}`, error);
        audioStatus.value = "pending";
        audioFiles.value = {
          enUsMale: null,
          enUsFemale: null,
          enGbMale: null,
          enGbFemale: null
        };
        globalLoading.updateProgress(100);
      } finally {
        try {
          globalLoading.hideLoading(loadingKey);
        } catch (hideError) {
          console.error(`\u274C \u9690\u85CF\u52A0\u8F7D\u52A8\u753B\u5931\u8D25: ${loadingKey}`, hideError);
          globalLoading.forceCleanQueue();
        }
        console.log(`\u2705 \u7EC4\u4EF6\u521D\u59CB\u5316\u5B8C\u6210: ${(_e = props.statement) == null ? void 0 : _e.id}`);
        initializing.value = false;
      }
    };
    watch(() => props.statement, (newStatement, oldStatement) => {
      if (newStatement && newStatement !== oldStatement) {
        console.log(`\u{1F504} \u8BED\u53E5\u53D8\u5316\u89E6\u53D1\u91CD\u65B0\u521D\u59CB\u5316: ${newStatement == null ? void 0 : newStatement.id}`);
        initializationTimer = setTimeout(() => {
          initializeAudioStatus();
        }, 100);
      }
    }, { deep: true });
    watch(() => {
      var _a;
      return (_a = props.statement) == null ? void 0 : _a.audioStatus;
    }, (newStatus, oldStatus) => {
      if (newStatus !== oldStatus && newStatus !== audioStatus.value) {
        audioStatus.value = newStatus || "pending";
        if (newStatus === "pending") {
          audioFiles.value = {
            enUsMale: null,
            enUsFemale: null,
            enGbMale: null,
            enGbFemale: null
          };
          reuseSource.value = null;
          showAudioList.value = false;
        }
      }
    });
    watch(() => {
      var _a, _b, _c, _d;
      return [
        (_a = props.statement) == null ? void 0 : _a.audioEnUsMale,
        (_b = props.statement) == null ? void 0 : _b.audioEnUsFemale,
        (_c = props.statement) == null ? void 0 : _c.audioEnGbMale,
        (_d = props.statement) == null ? void 0 : _d.audioEnGbFemale
      ];
    }, (newFiles, oldFiles) => {
      const allNewNull = newFiles.every((file) => !file);
      const hadOldFiles = oldFiles && oldFiles.some((file) => file);
      if (allNewNull && hadOldFiles) {
        reuseSource.value = null;
        showAudioList.value = false;
        audioStatus.value = "pending";
        audioFiles.value = {
          enUsMale: null,
          enUsFemale: null,
          enGbMale: null,
          enGbFemale: null
        };
      }
    }, { deep: true });
    watch(() => {
      var _a, _b, _c, _d;
      return [
        (_a = props.statement) == null ? void 0 : _a.audioEnUsMale,
        (_b = props.statement) == null ? void 0 : _b.audioEnUsFemale,
        (_c = props.statement) == null ? void 0 : _c.audioEnGbMale,
        (_d = props.statement) == null ? void 0 : _d.audioEnGbFemale
      ];
    }, (newFiles, oldFiles) => {
      var _a, _b, _c, _d;
      const hasChanged = newFiles.some((file, index) => file !== (oldFiles == null ? void 0 : oldFiles[index]));
      if (hasChanged) {
        audioFiles.value = {
          enUsMale: ((_a = props.statement) == null ? void 0 : _a.audioEnUsMale) || null,
          enUsFemale: ((_b = props.statement) == null ? void 0 : _b.audioEnUsFemale) || null,
          enGbMale: ((_c = props.statement) == null ? void 0 : _c.audioEnGbMale) || null,
          enGbFemale: ((_d = props.statement) == null ? void 0 : _d.audioEnGbFemale) || null
        };
        const hasAnyAudio = Object.values(audioFiles.value).some((file) => file);
        showAudioList.value = hasAnyAudio;
      }
    }, { deep: true });
    watch(() => {
      var _a;
      return (_a = props.statement) == null ? void 0 : _a.english;
    }, (newEnglish, oldEnglish) => {
      if (!newEnglish) {
        audioStatus.value = "pending";
        audioFiles.value = {
          enUsMale: null,
          enUsFemale: null,
          enGbMale: null,
          enGbFemale: null
        };
        showAudioList.value = false;
      } else if (newEnglish !== oldEnglish && oldEnglish !== void 0) {
        checkAudioReuse();
      }
    });
    const getFullAudioUrl = (url) => {
      if (!url) return "";
      if (url.startsWith("http")) return url;
      const fullUrl = `http://localhost:5000${url}`;
      return fullUrl;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "audio-controls" }, _attrs))} data-v-3a3dfcc4><div class="flex items-center space-x-2 mb-2" data-v-3a3dfcc4><span class="${ssrRenderClass([statusClass.value, "text-xs"])}" data-v-3a3dfcc4>${ssrInterpolate(statusText.value)}</span>`);
      if (audioStatus.value === "completed") {
        _push(`<button class="btn btn-xs btn-outline" data-v-3a3dfcc4>${ssrInterpolate(showAudioList.value ? "\u9690\u85CF" : "\u67E5\u770B\u8BE6\u60C5")}</button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
      _push(ssrRenderComponent(AudioReuseInfo, {
        statement: _ctx.statement,
        reuseInfo: {
          reused: reuseSource.value !== null,
          audioFiles: reuseSource.value ? audioFiles.value : {},
          source: reuseSource.value
        }
      }, null, _parent));
      if (showAudioList.value && hasAvailableAudio.value) {
        _push(`<div class="space-y-1 mb-2" data-v-3a3dfcc4><!--[-->`);
        ssrRenderList(audioFiles.value, (audio, key) => {
          _push(`<div class="flex items-center space-x-2" data-v-3a3dfcc4><span class="text-xs text-gray-600 w-16" data-v-3a3dfcc4>${ssrInterpolate(getVoiceLabel(key))}</span>`);
          if (audio) {
            _push(`<audio${ssrRenderAttr("src", getFullAudioUrl(audio))} controls preload="none" class="h-8 text-xs" style="${ssrRenderStyle({ "min-height": "32px", "min-width": "120px" })}" data-v-3a3dfcc4></audio>`);
          } else {
            _push(`<span class="text-xs text-gray-400" data-v-3a3dfcc4>\u672A\u751F\u6210</span>`);
          }
          _push(`</div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex space-x-1" data-v-3a3dfcc4>`);
      if (isReusedAudio.value && audioStatus.value === "pending") {
        _push(`<button${ssrIncludeBooleanAttr(generating.value) ? " disabled" : ""} class="${ssrRenderClass([{ "loading": generating.value }, "btn btn-xs btn-success"])}" data-v-3a3dfcc4>${ssrInterpolate(generating.value ? "\u5E94\u7528\u4E2D..." : "\u5E94\u7528\u590D\u7528")}</button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<button${ssrIncludeBooleanAttr(generating.value || !_ctx.statement.english) ? " disabled" : ""} class="${ssrRenderClass([{ "loading": generating.value }, "btn btn-xs btn-primary"])}" data-v-3a3dfcc4>${ssrInterpolate(getButtonText())}</button>`);
      if (audioStatus.value === "completed" || isReusedAudio.value && audioStatus.value === "pending") {
        _push(`<button class="btn btn-xs btn-error" data-v-3a3dfcc4> \u5220\u9664 </button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/AudioControls.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const AudioControls = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-3a3dfcc4"]]);
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "AudioManagementModal",
  __ssrInlineRender: true,
  props: {
    course: {},
    statements: {}
  },
  emits: ["close", "updated"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const globalLoading = useGlobalLoading();
    useAudioReuseManager();
    const loading = ref(false);
    const batchGenerating = ref(false);
    const selectedVoices = ref(["en-US-Male", "en-US-Female", "en-GB-Male", "en-GB-Female"]);
    const batchProgress = ref({ current: 0, total: 0 });
    const refreshCounter = ref(0);
    const courseStats = ref({
      totalStatements: 0,
      completedStatements: 0,
      pendingStatements: 0
    });
    const basicStats = computed(() => {
      if (!props.statements || props.statements.length === 0) {
        return {
          totalStatements: 0,
          completedStatements: 0,
          pendingStatements: 0
        };
      }
      const total = props.statements.length;
      const completed = props.statements.filter((s) => s.audioStatus === "completed").length;
      const pending = props.statements.filter((s) => s.audioStatus === "pending").length;
      return {
        totalStatements: total,
        completedStatements: completed,
        pendingStatements: pending
      };
    });
    const fetchCourseAudioStats = async () => {
      try {
        console.log("\u6B63\u5728\u83B7\u53D6\u8BFE\u7A0B\u97F3\u9891\u7EDF\u8BA1:", props.course.id);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("\u83B7\u53D6\u8BFE\u7A0B\u97F3\u9891\u7EDF\u8BA1\u8D85\u65F6")), 5e3);
        });
        const apiPromise = api.get(`/api/admin/audio/course/${props.course.id}/stats`);
        const response = await Promise.race([apiPromise, timeoutPromise]);
        console.log("\u8BFE\u7A0B\u97F3\u9891\u7EDF\u8BA1\u54CD\u5E94:", response);
        if (response && response.stats) {
          const stats = response.stats;
          courseStats.value = {
            totalStatements: stats.totalStatements,
            completedStatements: stats.statementsWithAudio,
            // 使用包含复用的统计
            pendingStatements: stats.pendingStatements
          };
          console.log("\u8BFE\u7A0B\u97F3\u9891\u7EDF\u8BA1\u66F4\u65B0\u6210\u529F:", courseStats.value);
        } else {
          console.log("\u4F7F\u7528\u57FA\u7840\u7EDF\u8BA1\u4F5C\u4E3A\u5907\u7528");
          courseStats.value = basicStats.value;
        }
      } catch (error) {
        console.error("\u83B7\u53D6\u8BFE\u7A0B\u97F3\u9891\u7EDF\u8BA1\u5931\u8D25:", error);
        console.log("\u4F7F\u7528\u57FA\u7840\u7EDF\u8BA1\u4F5C\u4E3A\u5907\u7528");
        courseStats.value = basicStats.value;
      }
    };
    watch(basicStats, (newStats) => {
      if (courseStats.value.totalStatements === 0) {
        courseStats.value = {
          totalStatements: newStats.totalStatements,
          completedStatements: newStats.completedStatements,
          pendingStatements: newStats.pendingStatements
        };
      }
    }, { immediate: true });
    const onAudioGenerated = async (statement) => {
      const index = props.statements.findIndex((s) => s.id === statement.id);
      if (index !== -1) {
        props.statements[index] = {
          ...props.statements[index],
          ...statement,
          audioStatus: statement.audioStatus,
          audioEnUsMale: statement.audioEnUsMale || null,
          audioEnUsFemale: statement.audioEnUsFemale || null,
          audioEnGbMale: statement.audioEnGbMale || null,
          audioEnGbFemale: statement.audioEnGbFemale || null,
          audioGeneratedAt: statement.audioGeneratedAt || null
        };
      }
      emit("updated");
      await fetchCourseAudioStats();
    };
    const onAudioDeleted = async (deleteInfo) => {
      emit("updated");
      await new Promise((resolve) => setTimeout(resolve, 200));
      refreshCounter.value++;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" }, _attrs))} data-v-271c84ae><div class="bg-white p-6 rounded-lg shadow-lg w-4/5 max-w-5xl max-h-[90vh] overflow-auto relative" data-v-271c84ae>`);
      _push(ssrRenderComponent(LoadingSpinner, {
        show: unref(globalLoading).isLoading.value,
        title: unref(globalLoading).loadingTitle.value,
        message: unref(globalLoading).loadingMessage.value,
        "show-progress": unref(globalLoading).showProgress.value,
        progress: unref(globalLoading).progress.value,
        class: "absolute inset-0 z-10"
      }, null, _parent));
      _push(`<div class="flex justify-between items-center mb-6" data-v-271c84ae><h2 class="text-2xl font-bold text-gray-800" data-v-271c84ae>\u{1F3B5} \u97F3\u9891\u7BA1\u7406</h2><button class="text-gray-500 hover:text-gray-700" data-v-271c84ae><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-271c84ae><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" data-v-271c84ae></path></svg></button></div><div class="bg-blue-50 p-4 rounded-lg mb-6" data-v-271c84ae><h3 class="text-lg font-semibold text-blue-800 mb-2" data-v-271c84ae>${ssrInterpolate(_ctx.course.title)}</h3><p class="text-blue-600" data-v-271c84ae>\u5171 ${ssrInterpolate(_ctx.statements.length)} \u4E2A\u8BED\u53E5</p></div><div class="mb-6 p-4 bg-green-50 rounded-lg" data-v-271c84ae><h3 class="text-lg font-semibold mb-3 text-green-800" data-v-271c84ae>\u{1F3B5} ${ssrInterpolate(_ctx.course.title)} - \u97F3\u9891\u7EDF\u8BA1</h3><div class="grid grid-cols-3 gap-6 text-sm" data-v-271c84ae><div class="text-center" data-v-271c84ae><div class="text-2xl font-bold text-blue-600" data-v-271c84ae>${ssrInterpolate(courseStats.value.totalStatements)}</div><div class="text-gray-600" data-v-271c84ae>\u603B\u8BED\u53E5\u6570</div></div><div class="text-center" data-v-271c84ae><div class="text-2xl font-bold text-green-600" data-v-271c84ae>${ssrInterpolate(courseStats.value.completedStatements)}</div><div class="text-gray-600" data-v-271c84ae>\u5DF2\u6709\u97F3\u9891</div></div><div class="text-center" data-v-271c84ae><div class="text-2xl font-bold text-orange-600" data-v-271c84ae>${ssrInterpolate(courseStats.value.pendingStatements)}</div><div class="text-gray-600" data-v-271c84ae>\u5F85\u751F\u6210</div></div></div>`);
      if (courseStats.value.completedStatements > 0) {
        _push(`<div class="mt-3 text-sm text-green-700" data-v-271c84ae> \u2705 \u672C\u8BFE\u7A0B\u5DF2\u5B8C\u6210 ${ssrInterpolate(courseStats.value.completedStatements)} \u4E2A\u8BED\u53E5\u7684\u97F3\u9891\u751F\u6210 </div>`);
      } else {
        _push(`<!---->`);
      }
      if (courseStats.value.pendingStatements > 0) {
        _push(`<div class="mt-2 text-sm text-blue-700" data-v-271c84ae> \u23F3 \u8FD8\u6709 ${ssrInterpolate(courseStats.value.pendingStatements)} \u4E2A\u8BED\u53E5\u5F85\u751F\u6210\u97F3\u9891 </div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="bg-gray-50 p-4 rounded-lg mb-6" data-v-271c84ae><h4 class="text-lg font-semibold mb-4" data-v-271c84ae>\u6279\u91CF\u64CD\u4F5C</h4><div class="flex flex-wrap gap-4 items-center" data-v-271c84ae><div class="flex items-center space-x-2" data-v-271c84ae><label class="text-sm font-medium" data-v-271c84ae>\u8BED\u97F3\u7C7B\u578B:</label><div class="flex space-x-2" data-v-271c84ae><label class="flex items-center" data-v-271c84ae><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(selectedVoices.value) ? ssrLooseContain(selectedVoices.value, "en-US-Male") : selectedVoices.value) ? " checked" : ""} value="en-US-Male" class="mr-1" data-v-271c84ae><span class="text-sm" data-v-271c84ae>\u7F8E\u5F0F\u7537\u58F0</span></label><label class="flex items-center" data-v-271c84ae><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(selectedVoices.value) ? ssrLooseContain(selectedVoices.value, "en-US-Female") : selectedVoices.value) ? " checked" : ""} value="en-US-Female" class="mr-1" data-v-271c84ae><span class="text-sm" data-v-271c84ae>\u7F8E\u5F0F\u5973\u58F0</span></label><label class="flex items-center" data-v-271c84ae><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(selectedVoices.value) ? ssrLooseContain(selectedVoices.value, "en-GB-Male") : selectedVoices.value) ? " checked" : ""} value="en-GB-Male" class="mr-1" data-v-271c84ae><span class="text-sm" data-v-271c84ae>\u82F1\u5F0F\u7537\u58F0</span></label><label class="flex items-center" data-v-271c84ae><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(selectedVoices.value) ? ssrLooseContain(selectedVoices.value, "en-GB-Female") : selectedVoices.value) ? " checked" : ""} value="en-GB-Female" class="mr-1" data-v-271c84ae><span class="text-sm" data-v-271c84ae>\u82F1\u5F0F\u5973\u58F0</span></label></div></div><button${ssrIncludeBooleanAttr(batchGenerating.value) ? " disabled" : ""} class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50 mr-2" title="\u5C06\u6240\u6709\u53EF\u590D\u7528\u7684\u97F3\u9891\u6301\u4E45\u5316\u5230\u6570\u636E\u5E93\uFF0C\u786E\u4FDD\u5B66\u4E60\u5E73\u53F0\u80FD\u6B63\u786E\u8BFB\u53D6" data-v-271c84ae>${ssrInterpolate(batchGenerating.value ? "\u5E94\u7528\u4E2D..." : "\u6279\u91CF\u5E94\u7528\u590D\u7528\u97F3\u9891\u5230\u6570\u636E\u5E93")}</button><button${ssrIncludeBooleanAttr(batchGenerating.value || selectedVoices.value.length === 0) ? " disabled" : ""} class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50 mr-2" data-v-271c84ae>${ssrInterpolate(batchGenerating.value ? "\u6279\u91CF\u751F\u6210\u4E2D..." : "\u6279\u91CF\u751F\u6210\u97F3\u9891")}</button><button${ssrIncludeBooleanAttr(batchGenerating.value || courseStats.value.completedStatements === 0) ? " disabled" : ""} class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50" data-v-271c84ae> \u6279\u91CF\u5220\u9664\u97F3\u9891 </button></div>`);
      if (batchGenerating.value) {
        _push(`<div class="mt-4" data-v-271c84ae><div class="flex justify-between text-sm text-gray-600 mb-1" data-v-271c84ae><span data-v-271c84ae>\u751F\u6210\u8FDB\u5EA6</span><span data-v-271c84ae>${ssrInterpolate(batchProgress.value.current)} / ${ssrInterpolate(batchProgress.value.total)}</span></div><div class="w-full bg-gray-200 rounded-full h-2" data-v-271c84ae><div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="${ssrRenderStyle({ width: batchProgress.value.total > 0 ? batchProgress.value.current / batchProgress.value.total * 100 + "%" : "0%" })}" data-v-271c84ae></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="space-y-4" data-v-271c84ae><h4 class="text-lg font-semibold" data-v-271c84ae>\u8BED\u53E5\u97F3\u9891\u72B6\u6001</h4>`);
      if (loading.value) {
        _push(`<div class="text-center py-8" data-v-271c84ae><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" data-v-271c84ae></div><p class="mt-2 text-gray-600" data-v-271c84ae>\u52A0\u8F7D\u4E2D...</p></div>`);
      } else if (_ctx.statements.length === 0) {
        _push(`<div class="text-center py-8 text-gray-500" data-v-271c84ae><p data-v-271c84ae>\u8BE5\u8BFE\u7A0B\u6682\u65E0\u8BED\u53E5</p><button class="mt-2 text-blue-500 hover:underline" data-v-271c84ae> \u53BB\u6DFB\u52A0\u8BED\u53E5 </button></div>`);
      } else {
        _push(`<div class="space-y-3" data-v-271c84ae><!--[-->`);
        ssrRenderList(_ctx.statements, (statement, index) => {
          _push(`<div class="border rounded-lg p-4 bg-white shadow-sm" data-v-271c84ae><div class="flex items-start justify-between" data-v-271c84ae><div class="flex-1" data-v-271c84ae><div class="flex items-center mb-2" data-v-271c84ae><span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2" data-v-271c84ae>${ssrInterpolate(index + 1)}</span><div class="flex-1" data-v-271c84ae><div class="font-medium text-gray-900 mb-1" data-v-271c84ae>${ssrInterpolate(statement.chinese)}</div><div class="text-gray-600" data-v-271c84ae>${ssrInterpolate(statement.english)}</div>`);
          if (statement.soundmark) {
            _push(`<div class="text-gray-500 text-sm" data-v-271c84ae>${ssrInterpolate(statement.soundmark)}</div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div></div><div class="ml-4" data-v-271c84ae>`);
          _push(ssrRenderComponent(AudioControls, {
            key: `${statement.id}-${statement.audioStatus}-${statement.audioEnUsMale}-${statement.audioEnUsFemale}-${statement.audioEnGbMale}-${statement.audioEnGbFemale}-${refreshCounter.value}`,
            statement,
            courseId: _ctx.course.id,
            onAudioGenerated,
            onUpdated: onAudioGenerated,
            onAudioDeleted
          }, null, _parent));
          _push(`</div></div></div>`);
        });
        _push(`<!--]--></div>`);
      }
      _push(`</div><div class="flex justify-end mt-6 pt-4 border-t" data-v-271c84ae><button class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded" data-v-271c84ae> \u5173\u95ED </button></div></div></div>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/AudioManagementModal.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const AudioManagementModal = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-271c84ae"]]);
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CourseDetail",
  __ssrInlineRender: true,
  props: {
    course: {},
    coursePacks: {}
  },
  setup(__props) {
    const props = __props;
    const statements = ref([]);
    const showCoverModal = ref(false);
    const showStatementModal = ref(false);
    const showAudioModal = ref(false);
    const globalLoading = useGlobalLoading();
    watch(showAudioModal, (newValue) => {
      if (newValue) {
        globalLoading.hideLoading("audio-management");
      }
    });
    watch(showStatementModal, (newValue) => {
      if (newValue) {
        globalLoading.hideLoading("statement-edit");
      }
    });
    const loading = ref(false);
    ref(null);
    const coverUpdated = ref(false);
    const displayCover = computed(() => {
      if (props.course.cover) {
        return props.course.cover;
      }
      const coverStorageKey = `course_cover_${props.course.id}`;
      return localStorage.getItem(coverStorageKey) || "";
    });
    const getPackageTitle = (course) => {
      var _a, _b;
      if ((_a = course.course_packs) == null ? void 0 : _a.title) {
        return course.course_packs.title;
      }
      if (course.coursePackId && ((_b = props.coursePacks) == null ? void 0 : _b.length)) {
        const pack = props.coursePacks.find((p) => p.id === course.coursePackId);
        if (pack == null ? void 0 : pack.title) {
          return pack.title;
        }
      }
      return "";
    };
    const fetchStatements = async () => {
      var _a;
      if (!((_a = props.course) == null ? void 0 : _a.id)) return;
      loading.value = true;
      globalLoading.showLoading({
        title: "\u52A0\u8F7D\u8BFE\u7A0B\u6570\u636E",
        message: "\u6B63\u5728\u83B7\u53D6\u8BFE\u7A0B\u8BED\u53E5\u6570\u636E\uFF0C\u8BF7\u7A0D\u5019",
        key: "fetch-statements"
      });
      try {
        const data = await api.get(`/api/admin/courses/${props.course.id}/statements`);
        statements.value = data || [];
      } catch (error) {
        statements.value = [];
      } finally {
        globalLoading.hideLoading("fetch-statements");
        loading.value = false;
      }
    };
    const refresh = async () => {
      await fetchStatements();
    };
    const onCoverUpdated = () => {
      coverUpdated.value = true;
      refresh();
    };
    watch(() => {
      var _a;
      return (_a = props.course) == null ? void 0 : _a.id;
    }, fetchStatements, { immediate: true });
    return (_ctx, _push, _parent, _attrs) => {
      if (_ctx.course) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-4" }, _attrs))}><div class="flex justify-between items-center mb-4"><h2 class="text-xl font-bold">${ssrInterpolate(_ctx.course.title)}</h2><div class="flex space-x-2"><button class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">\u7F16\u8F91\u5C01\u9762</button><button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">\u8BFE\u7A0B\u8BED\u53E5\u7F16\u8F91</button><button class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">\u{1F3B5} \u97F3\u9891\u7BA1\u7406</button><button class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm">\u8BED\u53E5\u6279\u91CF\u5BFC\u5165</button></div></div><div class="mb-4"><div class="flex items-center mb-2"><span class="mr-2 font-bold">\u6240\u5C5E\u8BFE\u7A0B\u5305:</span><span>${ssrInterpolate(getPackageTitle(_ctx.course))}</span></div>`);
        if (displayCover.value) {
          _push(`<div class="mb-2"><img${ssrRenderAttr("src", displayCover.value)} class="w-32 h-20 object-cover rounded"></div>`);
        } else {
          _push(`<div class="w-32 h-20 bg-gray-200 flex items-center justify-center rounded mb-2"><span class="text-gray-500">\u65E0\u5C01\u9762</span></div>`);
        }
        _push(`</div><div class="mb-4"><h3 class="text-lg font-bold mb-2">\u8BED\u53E5\u5217\u8868 (${ssrInterpolate(statements.value.length)})</h3>`);
        if (loading.value) {
          _push(`<div class="text-center py-4">\u52A0\u8F7D\u4E2D...</div>`);
        } else if (statements.value.length === 0) {
          _push(`<div class="text-center py-4 bg-gray-50 rounded"><p>\u6682\u65E0\u8BED\u53E5\u6570\u636E</p><button class="btn btn-sm btn-primary mt-2">\u6DFB\u52A0\u8BED\u53E5</button></div>`);
        } else {
          _push(`<div class="border rounded divide-y divide-gray-100 bg-white"><!--[-->`);
          ssrRenderList(statements.value, (s, index) => {
            _push(`<div class="p-4"><div class="flex items-center mb-1"><div class="w-10 text-gray-400 text-center font-mono">${ssrInterpolate(index + 1)}</div><div class="w-16 font-bold">\u4E2D\u6587</div><div class="flex-1">${ssrInterpolate(s.chinese)}</div></div><div class="flex items-center mb-1"><div class="w-10"></div><div class="w-16 font-bold">\u82F1\u6587</div><div class="flex-1">${ssrInterpolate(s.english)}</div></div><div class="flex items-center"><div class="w-10"></div><div class="w-16 font-bold">\u97F3\u6807</div><div class="flex-1">${ssrInterpolate(s.soundmark)}</div></div></div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div>`);
        if (showCoverModal.value) {
          _push(ssrRenderComponent(_sfc_main$7, {
            course: _ctx.course,
            onClose: ($event) => showCoverModal.value = false,
            onUpdated: onCoverUpdated
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        if (showStatementModal.value) {
          _push(ssrRenderComponent(_sfc_main$4, {
            course: _ctx.course,
            statements: statements.value,
            onClose: ($event) => showStatementModal.value = false,
            onUpdated: refresh
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        if (showAudioModal.value) {
          _push(ssrRenderComponent(AudioManagementModal, {
            course: _ctx.course,
            statements: statements.value,
            onClose: ($event) => showAudioModal.value = false,
            onUpdated: refresh
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`<input type="file" accept=".xlsx,.xls,.csv,.txt" class="hidden"></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/CourseDetail.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "CreateCourseModal",
  __ssrInlineRender: true,
  props: {
    coursePacks: {}
  },
  emits: ["close", "created"],
  setup(__props, { emit: __emit }) {
    useToast();
    const form = ref({
      coursePackId: "",
      title: "",
      cover: "",
      statements: []
    });
    const single = ref({ chinese: "", english: "" });
    const mode = ref("single");
    const previewUrl = ref("");
    ref(null);
    const submitting = ref(false);
    const showTemplateOptions = ref(false);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" }, _attrs))}><div class="bg-white p-6 rounded-lg shadow-lg w-96 max-h-[90vh] overflow-auto"><h2 class="text-lg font-bold mb-4">\u521B\u5EFA\u65B0\u8BFE\u7A0B</h2><form><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u6240\u5C5E\u8BFE\u7A0B\u5305</label><select required class="w-full border rounded px-3 py-2"><option value="" disabled${ssrIncludeBooleanAttr(Array.isArray(form.value.coursePackId) ? ssrLooseContain(form.value.coursePackId, "") : ssrLooseEqual(form.value.coursePackId, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u8BFE\u7A0B\u5305</option><!--[-->`);
      ssrRenderList(_ctx.coursePacks, (pack) => {
        _push(`<option${ssrRenderAttr("value", pack.id)}${ssrIncludeBooleanAttr(Array.isArray(form.value.coursePackId) ? ssrLooseContain(form.value.coursePackId, pack.id) : ssrLooseEqual(form.value.coursePackId, pack.id)) ? " selected" : ""}>${ssrInterpolate(pack.title)}</option>`);
      });
      _push(`<!--]--></select></div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u8BFE\u7A0B\u6807\u9898</label><input${ssrRenderAttr("value", form.value.title)} required class="w-full border rounded px-3 py-2" placeholder="\u8F93\u5165\u8BFE\u7A0B\u6807\u9898"></div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u8BFE\u7A0B\u5C01\u9762</label><input type="file" accept="image/*" class="w-full">`);
      if (previewUrl.value) {
        _push(`<div class="mt-2"><img${ssrRenderAttr("src", previewUrl.value)} class="w-full h-32 object-cover rounded"></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u8BED\u53E5\u5BFC\u5165\u65B9\u5F0F</label><div class="flex space-x-2"><button type="button" class="${ssrRenderClass([mode.value === "single" ? "bg-blue-500 text-white" : "bg-gray-200", "px-3 py-1 rounded"])}"> \u9010\u53E5\u521B\u5EFA </button><button type="button" class="${ssrRenderClass([mode.value === "excel" ? "bg-blue-500 text-white" : "bg-gray-200", "px-3 py-1 rounded"])}"> \u8BED\u53E5\u6279\u91CF\u5BFC\u5165 </button></div></div>`);
      if (mode.value === "single") {
        _push(`<div class="mb-4"><div class="border rounded p-3 mb-2"><div class="mb-2"><label class="block text-sm font-medium text-gray-700 mb-1">\u4E2D\u6587</label><input${ssrRenderAttr("value", single.value.chinese)} class="w-full border rounded px-3 py-2" placeholder="\u8F93\u5165\u4E2D\u6587"></div><div class="mb-2"><label class="block text-sm font-medium text-gray-700 mb-1">\u82F1\u6587</label><input${ssrRenderAttr("value", single.value.english)} class="w-full border rounded px-3 py-2" placeholder="\u8F93\u5165\u82F1\u6587"></div><button type="button" class="btn btn-sm btn-secondary w-full"> \u6DFB\u52A0\u8BED\u53E5 </button></div>`);
        if (form.value.statements.length > 0) {
          _push(`<div class="border rounded p-2"><h3 class="text-sm font-medium mb-2">\u5DF2\u6DFB\u52A0\u8BED\u53E5 (${ssrInterpolate(form.value.statements.length)})</h3><ul class="max-h-40 overflow-y-auto"><!--[-->`);
          ssrRenderList(form.value.statements, (s, i) => {
            _push(`<li class="flex justify-between items-center py-1 border-b last:border-b-0"><span class="text-sm">${ssrInterpolate(s.chinese)} - ${ssrInterpolate(s.english)}</span><button type="button" class="text-red-500 text-xs">\u5220\u9664</button></li>`);
          });
          _push(`<!--]--></ul></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (mode.value === "excel") {
        _push(`<div class="mb-4"><input type="file" accept=".xlsx,.xls,.csv,.txt" class="w-full mb-2"><div class="text-sm text-gray-500"><p>\u8BF7\u4E0A\u4F20\u7B26\u5408\u683C\u5F0F\u7684\u6587\u4EF6\uFF0C\u652F\u6301\u4EE5\u4E0B\u683C\u5F0F\uFF1A</p><ul class="list-disc pl-5 mt-1"><li>Excel\u6587\u4EF6 (.xlsx, .xls)</li><li>CSV\u6587\u4EF6 (.csv)</li><li>\u6587\u672C\u6587\u4EF6 (.txt)</li></ul><div class="mt-2"><div class="flex items-center"><button type="button" class="text-blue-500 flex items-center"> \u4E0B\u8F7D\u5BFC\u5165\u6A21\u677F <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></div>`);
        if (showTemplateOptions.value) {
          _push(`<div class="mt-1 bg-gray-50 p-2 rounded"><a href="/excel/course-import-template.xlsx" download class="block text-blue-500 hover:underline mb-1">Excel\u6A21\u677F (.xlsx)</a><a href="/excel/course-import-template.csv" download class="block text-blue-500 hover:underline mb-1">CSV\u6A21\u677F (.csv)</a><a href="/excel/course-import-template.txt" download class="block text-blue-500 hover:underline">\u6587\u672C\u6A21\u677F (.txt)</a></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<button type="button" class="text-blue-500 mt-1">\u67E5\u770B\u683C\u5F0F\u8BF4\u660E</button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex justify-end space-x-2"><button type="button" class="btn">\u53D6\u6D88</button><button type="submit" class="btn btn-primary"${ssrIncludeBooleanAttr(submitting.value) ? " disabled" : ""}>${ssrInterpolate(submitting.value ? "\u521B\u5EFA\u4E2D..." : "\u521B\u5EFA\u8BFE\u7A0B")}</button></div></form></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/CreateCourseModal.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main$1 as _, _sfc_main as a, _sfc_main$7 as b };
//# sourceMappingURL=CreateCourseModal-CREXwZJc.mjs.map
