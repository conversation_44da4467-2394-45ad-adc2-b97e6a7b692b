import { defineComponent, ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderClass, ssrInterpolate, ssrRenderList, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderAttr } from 'vue/server-renderer';
import { b as useGlobalLoading } from './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "audio",
  __ssrInlineRender: true,
  setup(__props) {
    useGlobalLoading();
    const serviceStatus = ref({
      available: false,
      message: "\u68C0\u67E5\u4E2D...",
      voiceConfigs: {}
    });
    const courses = ref([]);
    const selectedCourseId = ref("");
    const voiceTypes = ["en-US-Male", "en-US-Female", "en-GB-Male", "en-GB-Female"];
    const selectedVoiceTypes = ref([...voiceTypes]);
    const regenerateExisting = ref(false);
    const batchGenerating = ref(false);
    const batchResult = ref(null);
    const audioStats = ref({
      completed: 0,
      generating: 0,
      failed: 0,
      pending: 0
    });
    const getVoiceLabel = (voiceType) => {
      const labels = {
        "en-US-Male": "\u7F8E\u5F0F\u7537\u58F0",
        "en-US-Female": "\u7F8E\u5F0F\u5973\u58F0",
        "en-GB-Male": "\u82F1\u5F0F\u7537\u58F0",
        "en-GB-Female": "\u82F1\u5F0F\u5973\u58F0"
      };
      return labels[voiceType] || voiceType;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-6" }, _attrs))}><div class="flex justify-between items-center mb-6"><h1 class="text-2xl font-bold">\u97F3\u9891\u7BA1\u7406</h1><button class="btn btn-outline">\u68C0\u67E5\u670D\u52A1\u72B6\u6001</button></div><div class="card bg-base-100 shadow-xl mb-6"><div class="card-body"><h2 class="card-title">Edge TTS \u670D\u52A1\u72B6\u6001</h2><div class="flex items-center space-x-4"><div class="${ssrRenderClass([serviceStatus.value.available ? "badge-success" : "badge-error", "badge"])}">${ssrInterpolate(serviceStatus.value.available ? "\u53EF\u7528" : "\u4E0D\u53EF\u7528")}</div><span class="text-sm text-gray-600">${ssrInterpolate(serviceStatus.value.message)}</span></div>`);
      if (serviceStatus.value.available) {
        _push(`<div class="mt-4"><h3 class="font-semibold mb-2">\u652F\u6301\u7684\u8BED\u97F3\u7C7B\u578B\uFF1A</h3><div class="grid grid-cols-2 gap-2"><!--[-->`);
        ssrRenderList(serviceStatus.value.voiceConfigs, (config, key) => {
          _push(`<div class="flex items-center space-x-2"><span class="badge badge-outline">${ssrInterpolate(key)}</span><span class="text-sm">${ssrInterpolate(config.name)}</span></div>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div><div class="card bg-base-100 shadow-xl mb-6"><div class="card-body"><h2 class="card-title">\u6279\u91CF\u64CD\u4F5C</h2><div class="form-control mb-4"><label class="label"><span class="label-text">\u9009\u62E9\u8BFE\u7A0B</span></label><select class="select select-bordered"><option value=""${ssrIncludeBooleanAttr(Array.isArray(selectedCourseId.value) ? ssrLooseContain(selectedCourseId.value, "") : ssrLooseEqual(selectedCourseId.value, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u8BFE\u7A0B</option><!--[-->`);
      ssrRenderList(courses.value, (course) => {
        _push(`<option${ssrRenderAttr("value", course.id)}${ssrIncludeBooleanAttr(Array.isArray(selectedCourseId.value) ? ssrLooseContain(selectedCourseId.value, course.id) : ssrLooseEqual(selectedCourseId.value, course.id)) ? " selected" : ""}>${ssrInterpolate(course.title)}</option>`);
      });
      _push(`<!--]--></select></div><div class="form-control mb-4"><label class="label"><span class="label-text">\u8BED\u97F3\u7C7B\u578B</span></label><div class="flex flex-wrap gap-2"><!--[-->`);
      ssrRenderList(voiceTypes, (voiceType) => {
        _push(`<label class="cursor-pointer label"><input type="checkbox"${ssrRenderAttr("value", voiceType)}${ssrIncludeBooleanAttr(Array.isArray(selectedVoiceTypes.value) ? ssrLooseContain(selectedVoiceTypes.value, voiceType) : selectedVoiceTypes.value) ? " checked" : ""} class="checkbox checkbox-primary"><span class="label-text ml-2">${ssrInterpolate(getVoiceLabel(voiceType))}</span></label>`);
      });
      _push(`<!--]--></div></div><div class="form-control mb-4"><label class="cursor-pointer label"><span class="label-text">\u91CD\u65B0\u751F\u6210\u5DF2\u5B58\u5728\u7684\u97F3\u9891</span><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(regenerateExisting.value) ? ssrLooseContain(regenerateExisting.value, null) : regenerateExisting.value) ? " checked" : ""} class="checkbox checkbox-primary"></label></div><div class="card-actions"><button${ssrIncludeBooleanAttr(!selectedCourseId.value || selectedVoiceTypes.value.length === 0 || batchGenerating.value) ? " disabled" : ""} class="${ssrRenderClass([{ "loading": batchGenerating.value }, "btn btn-primary"])}">${ssrInterpolate(batchGenerating.value ? "\u751F\u6210\u4E2D..." : "\u6279\u91CF\u751F\u6210\u97F3\u9891")}</button></div></div></div>`);
      if (batchResult.value) {
        _push(`<div class="card bg-base-100 shadow-xl mb-6"><div class="card-body"><h2 class="card-title">\u751F\u6210\u7ED3\u679C</h2><div class="stats stats-vertical lg:stats-horizontal shadow"><div class="stat"><div class="stat-title">\u603B\u8BED\u53E5\u6570</div><div class="stat-value">${ssrInterpolate(batchResult.value.totalStatements)}</div></div><div class="stat"><div class="stat-title">\u6210\u529F</div><div class="stat-value text-success">${ssrInterpolate(batchResult.value.successCount)}</div></div><div class="stat"><div class="stat-title">\u5931\u8D25</div><div class="stat-value text-error">${ssrInterpolate(batchResult.value.failCount)}</div></div></div>`);
        if (batchResult.value.results && batchResult.value.results.length > 0) {
          _push(`<div class="mt-4"><h3 class="font-semibold mb-2">\u8BE6\u7EC6\u7ED3\u679C\uFF1A</h3><div class="overflow-x-auto"><table class="table table-zebra"><thead><tr><th>\u8BED\u53E5</th><th>\u72B6\u6001</th><th>\u9519\u8BEF\u4FE1\u606F</th></tr></thead><tbody><!--[-->`);
          ssrRenderList(batchResult.value.results, (result) => {
            _push(`<tr><td class="max-w-xs truncate">${ssrInterpolate(result.english)}</td><td><div class="${ssrRenderClass([result.success ? "badge-success" : "badge-error", "badge"])}">${ssrInterpolate(result.success ? "\u6210\u529F" : "\u5931\u8D25")}</div></td><td class="text-sm text-error">${ssrInterpolate(result.error || "-")}</td></tr>`);
          });
          _push(`<!--]--></tbody></table></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="card bg-base-100 shadow-xl"><div class="card-body"><h2 class="card-title">\u97F3\u9891\u7EDF\u8BA1</h2><div class="stats stats-vertical lg:stats-horizontal shadow"><div class="stat"><div class="stat-title">\u5DF2\u751F\u6210\u97F3\u9891\u7684\u8BED\u53E5</div><div class="stat-value">${ssrInterpolate(audioStats.value.completed)}</div></div><div class="stat"><div class="stat-title">\u751F\u6210\u4E2D</div><div class="stat-value text-warning">${ssrInterpolate(audioStats.value.generating)}</div></div><div class="stat"><div class="stat-title">\u751F\u6210\u5931\u8D25</div><div class="stat-value text-error">${ssrInterpolate(audioStats.value.failed)}</div></div><div class="stat"><div class="stat-title">\u672A\u751F\u6210</div><div class="stat-value text-info">${ssrInterpolate(audioStats.value.pending)}</div></div></div><div class="card-actions mt-4"><button class="btn btn-outline">\u5237\u65B0\u7EDF\u8BA1</button></div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/audio.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=audio-B1glWSlP.mjs.map
