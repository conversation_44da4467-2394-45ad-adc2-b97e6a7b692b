import { defineComponent, computed, reactive, ref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "edit",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const route = useRoute();
    computed(() => route.params.id);
    const formData = reactive({
      username: "",
      email: "",
      avatar: "",
      isActive: true,
      isNewUser: false
    });
    const isLoading = ref(true);
    const isSaving = ref(false);
    const error = ref("");
    const showSuccessAlert = ref(false);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)}><div class="mb-6 flex items-center"><button class="mr-3 inline-flex items-center text-sm font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"><span class="i-ph-arrow-left-bold mr-1 h-5 w-5"></span> \u8FD4\u56DE </button><h1 class="text-2xl font-bold">\u7F16\u8F91\u7528\u6237</h1></div>`);
      if (isLoading.value) {
        _push(`<div><div class="flex h-64 items-center justify-center"><span class="i-ph-spinner animate-spin h-10 w-10 text-purple-600"></span></div></div>`);
      } else if (error.value) {
        _push(`<div class="rounded-md bg-red-50 p-4 dark:bg-red-900/30"><div class="flex"><div class="flex-shrink-0"><span class="i-ph-warning-circle h-5 w-5 text-red-400" aria-hidden="true"></span></div><div class="ml-3"><h3 class="text-sm font-medium text-red-800 dark:text-red-200">${ssrInterpolate(error.value)}</h3></div></div></div>`);
      } else {
        _push(`<div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800"><form><div class="px-6 py-5"><div class="mb-8"><h2 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">\u57FA\u672C\u4FE1\u606F</h2><div class="grid grid-cols-1 gap-6 md:grid-cols-2"><div><label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300"> \u7528\u6237\u540D </label><input id="username"${ssrRenderAttr("value", formData.username)} type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm" required></div><div><label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300"> \u90AE\u7BB1 </label><input id="email"${ssrRenderAttr("value", formData.email)} type="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm" required></div></div></div><div class="mb-8"><h2 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">\u5934\u50CF</h2><div class="flex items-center space-x-6"><div class="h-20 w-20 flex-shrink-0 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"><img${ssrRenderAttr("src", formData.avatar || "https://api.dicebear.com/7.x/bottts/svg?seed=placeholder")} alt="\u7528\u6237\u5934\u50CF" class="h-full w-full object-cover"></div><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300"> \u5934\u50CFURL </label><input${ssrRenderAttr("value", formData.avatar)} type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm" placeholder="\u8F93\u5165\u5934\u50CFURL"><p class="mt-1 text-xs text-gray-500 dark:text-gray-400"> \u8F93\u5165\u6709\u6548\u7684\u56FE\u7247URL\u6216\u4F7F\u7528\u9ED8\u8BA4\u5934\u50CF </p></div></div></div><div class="mb-8"><h2 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">\u8D26\u6237\u72B6\u6001</h2><div class="flex items-center space-x-6"><div class="flex items-center"><input id="active"${ssrIncludeBooleanAttr(Array.isArray(formData.isActive) ? ssrLooseContain(formData.isActive, null) : formData.isActive) ? " checked" : ""} type="checkbox" class="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700"><label for="active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300"> \u8D26\u6237\u6FC0\u6D3B </label></div><div class="flex items-center"><input id="newUser"${ssrIncludeBooleanAttr(Array.isArray(formData.isNewUser) ? ssrLooseContain(formData.isNewUser, null) : formData.isNewUser) ? " checked" : ""} type="checkbox" class="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700"><label for="newUser" class="ml-2 block text-sm text-gray-700 dark:text-gray-300"> \u65B0\u7528\u6237 </label></div></div></div></div><div class="border-t border-gray-200 px-6 py-4 dark:border-gray-700"><div class="flex justify-end space-x-3"><button type="button" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"> \u53D6\u6D88 </button><button type="submit"${ssrIncludeBooleanAttr(isSaving.value) ? " disabled" : ""} class="inline-flex items-center rounded-md border border-transparent bg-purple-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 dark:bg-purple-700 dark:hover:bg-purple-800">`);
        if (isSaving.value) {
          _push(`<span class="i-ph-spinner animate-spin mr-2 h-4 w-4"></span>`);
        } else {
          _push(`<!---->`);
        }
        _push(` ${ssrInterpolate(isSaving.value ? "\u4FDD\u5B58\u4E2D..." : "\u4FDD\u5B58")}</button></div></div></form></div>`);
      }
      if (showSuccessAlert.value) {
        _push(`<div class="fixed bottom-4 right-4 z-50 flex items-center rounded-md bg-green-100 p-4 shadow-lg dark:bg-green-900/30"><span class="i-ph-check-circle-bold mr-2 h-5 w-5 text-green-600 dark:text-green-400"></span><span class="text-sm font-medium text-green-800 dark:text-green-200">\u4FDD\u5B58\u6210\u529F</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/users/[id]/edit.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=edit-BFvxIkNS.mjs.map
