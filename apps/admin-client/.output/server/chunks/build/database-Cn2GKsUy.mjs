import { defineComponent, computed, ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrIncludeBooleanAttr, ssrLooseContain, ssrRenderList, ssrInterpolate } from 'vue/server-renderer';
import { a as useToast, g as useAdminState } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "database",
  __ssrInlineRender: true,
  setup(__props) {
    useToast();
    const { adminUser } = useAdminState();
    const isSuperAdmin = computed(() => {
      if (!adminUser.value) return false;
      return adminUser.value.role === "superadmin";
    });
    const loading = ref(false);
    const loadingBackups = ref(false);
    const backups = ref([]);
    ref(null);
    const selectedBackup = ref("");
    const showRestoreConfirm = ref(false);
    const showDeleteConfirm = ref(false);
    const autoRestore = ref(false);
    function formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false
      });
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex min-h-screen bg-gray-100" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 pl-64"><div class="p-8"><div class="w-full max-w-6xl mx-auto"><h1 class="text-2xl font-bold mb-8 text-center">\u6570\u636E\u7BA1\u7406</h1>`);
      if (!isSuperAdmin.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"><strong class="font-bold">\u65E0\u6743\u9650\uFF1A</strong><span class="block sm:inline">\u60A8\u4E0D\u662F\u8D85\u7EA7\u7BA1\u7406\u5458\uFF0C\u65E0\u6CD5\u8BBF\u95EE\u6570\u636E\u7BA1\u7406\u529F\u80FD\u3002</span></div>`);
      } else {
        _push(`<div><div class="bg-white p-6 rounded-lg shadow mb-6"><h2 class="text-xl font-semibold mb-4">\u6570\u636E\u5E93\u64CD\u4F5C</h2><div class="flex flex-wrap gap-4"><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center"${ssrIncludeBooleanAttr(loading.value) ? " disabled" : ""}>`);
        if (loading.value) {
          _push(`<svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`);
        } else {
          _push(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path></svg>`);
        }
        _push(` \u521B\u5EFA\u6570\u636E\u5E93\u5907\u4EFD </button><div class="relative"><input type="file" accept=".sql,.gz" class="absolute inset-0 opacity-0 w-full h-full cursor-pointer"><button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12"></path></svg> \u4E0A\u4F20\u5907\u4EFD\u6587\u4EF6 </button></div><div class="ml-4"><label class="inline-flex items-center cursor-pointer"><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(autoRestore.value) ? ssrLooseContain(autoRestore.value, null) : autoRestore.value) ? " checked" : ""} class="form-checkbox h-5 w-5 text-blue-600"><span class="ml-2 text-gray-700">\u4E0A\u4F20\u540E\u81EA\u52A8\u6062\u590D</span></label></div></div></div><div class="bg-white rounded-lg shadow overflow-hidden"><div class="p-4 border-b"><h2 class="text-xl font-semibold">\u5907\u4EFD\u6587\u4EF6\u5217\u8868</h2><p class="text-sm text-gray-500 mt-1">\u8BF7\u8C28\u614E\u64CD\u4F5C\uFF0C\u6062\u590D\u6570\u636E\u5C06\u8986\u76D6\u5F53\u524D\u6570\u636E\u5E93\u5185\u5BB9</p></div>`);
        if (loadingBackups.value) {
          _push(`<div class="p-8 text-center"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"></div><p class="mt-2 text-gray-600">\u52A0\u8F7D\u5907\u4EFD\u5217\u8868\u4E2D...</p></div>`);
        } else if (!backups.value.length) {
          _push(`<div class="p-8 text-center text-gray-500"> \u6682\u65E0\u5907\u4EFD\u6587\u4EF6 </div>`);
        } else {
          _push(`<div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u6587\u4EF6\u540D </th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u5927\u5C0F </th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u521B\u5EFA\u65F6\u95F4 </th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u64CD\u4F5C </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
          ssrRenderList(backups.value, (backup) => {
            _push(`<tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${ssrInterpolate(backup.filename)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(formatFileSize(backup.size))}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(formatDate(backup.createdAt))}</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-900"> \u4E0B\u8F7D </button><button class="text-green-600 hover:text-green-900"> \u6062\u590D </button><button class="text-red-600 hover:text-red-900"> \u5220\u9664 </button></div></td></tr>`);
          });
          _push(`<!--]--></tbody></table></div>`);
        }
        _push(`</div></div>`);
      }
      _push(`</div></div></div>`);
      if (showRestoreConfirm.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full"><h3 class="text-xl font-bold text-red-600 mb-4">\u26A0\uFE0F \u8B66\u544A\uFF1A\u6570\u636E\u5E93\u6062\u590D</h3><p class="mb-4">\u60A8\u786E\u5B9A\u8981\u6062\u590D\u6B64\u5907\u4EFD\u6587\u4EF6\u5417\uFF1F\u6B64\u64CD\u4F5C\u5C06\u8986\u76D6\u5F53\u524D\u6570\u636E\u5E93\u4E2D\u7684\u6240\u6709\u6570\u636E\uFF01</p><p class="mb-6 font-semibold">\u6587\u4EF6\u540D\uFF1A${ssrInterpolate(selectedBackup.value)}</p><div class="flex justify-end space-x-3"><button class="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"> \u53D6\u6D88 </button><button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"> \u786E\u8BA4\u6062\u590D </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showDeleteConfirm.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full"><h3 class="text-xl font-bold mb-4">\u786E\u8BA4\u5220\u9664\u5907\u4EFD</h3><p class="mb-4">\u60A8\u786E\u5B9A\u8981\u5220\u9664\u6B64\u5907\u4EFD\u6587\u4EF6\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\u3002</p><p class="mb-6 font-semibold">\u6587\u4EF6\u540D\uFF1A${ssrInterpolate(selectedBackup.value)}</p><div class="flex justify-end space-x-3"><button class="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"> \u53D6\u6D88 </button><button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"> \u786E\u8BA4\u5220\u9664 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/database.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=database-Cn2GKsUy.mjs.map
