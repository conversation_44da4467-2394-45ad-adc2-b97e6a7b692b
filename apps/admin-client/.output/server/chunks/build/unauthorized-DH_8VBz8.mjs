import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { useSSRContext } from 'vue';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = {
  __name: "unauthorized",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-1d6706ba>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="ml-64 p-6" data-v-1d6706ba><div class="flex items-center justify-center min-h-[80vh]" data-v-1d6706ba><div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full" data-v-1d6706ba><div class="text-center" data-v-1d6706ba><h1 class="text-red-600 text-4xl font-bold mb-4" data-v-1d6706ba>\u6743\u9650\u4E0D\u8DB3</h1><div class="text-red-500 text-6xl mb-6" data-v-1d6706ba><svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-1d6706ba><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-4V8m0 0V6m0 2h2m-2 0H9m3 4v2m0 0v2m0-2h2m-2 0H9" data-v-1d6706ba></path></svg></div><p class="text-gray-700 mb-6" data-v-1d6706ba> \u60A8\u6CA1\u6709\u6743\u9650\u8BBF\u95EE\u6B64\u529F\u80FD\u3002\u6B64\u529F\u80FD\u4EC5\u9650\u8D85\u7EA7\u7BA1\u7406\u5458\u4F7F\u7528\u3002 </p><div class="flex flex-col space-y-3" data-v-1d6706ba><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-1d6706ba> \u8FD4\u56DE\u4EEA\u8868\u76D8 </button></div></div></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/unauthorized.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const unauthorized = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-1d6706ba"]]);

export { unauthorized as default };
//# sourceMappingURL=unauthorized-DH_8VBz8.mjs.map
