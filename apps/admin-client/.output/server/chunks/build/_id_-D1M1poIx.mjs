import { defineComponent, ref, reactive, computed, useSSRContext } from 'vue';
import { ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const route = useRoute();
    useToast();
    route.params.id;
    const loading = ref(true);
    const error = ref("");
    const autoSpeak = ref(false);
    const speechRate = ref("1");
    ref(false);
    const isChrome = ref(false);
    const showPronunciationModal = ref(false);
    const currentWord = ref("");
    const currentPronunciation = ref("");
    const content = reactive({
      id: "",
      title: "",
      type: "",
      body: "",
      categories: [],
      annotations: null,
      createdAt: "",
      updatedAt: ""
    });
    const processedContent = computed(() => {
      if (!content.body) return "";
      if (content.annotations && Array.isArray(content.annotations) && content.annotations.length > 0) {
        const tempDiv = (void 0).createElement("div");
        tempDiv.innerHTML = content.body;
        let newHtml = content.body;
        const sortedAnnotations = [...content.annotations].sort(
          (a, b) => b.word.length - a.word.length
        );
        sortedAnnotations.forEach((annotation) => {
          try {
            const { word, pronunciation } = annotation;
            if (!pronunciation) return;
            const safeWord = word.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
            const safePronunciation = pronunciation.replace(/&/g, "&amp;").replace(/"/g, "&quot;").replace(/'/g, "&#39;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
            const annotatedSpan = `<span class="pronunciation-word" data-pronunciation="${safePronunciation}" data-word="${safeWord}">${safeWord} 
            <button class="pronunciation-btn" data-word="${safeWord}" data-pronunciation="${safePronunciation}" title="\u70B9\u51FB\u64AD\u653E\u53D1\u97F3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.414l-2.829-2.828a4 4 0 015.657-5.657l2.828 2.829m7.072 7.072l2.828 2.829a4 4 0 11-5.657 5.657l-2.828-2.829" />
              </svg>
            </button>
          </span>`;
            const wordRegex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "g");
            newHtml = newHtml.replace(wordRegex, annotatedSpan);
          } catch (e) {
            console.error(`\u5904\u7406\u5355\u8BCD ${annotation.word} \u65F6\u51FA\u9519:`, e);
          }
        });
        return newHtml;
      }
      return content.body;
    });
    const formatContentType = (type) => {
      const typeMap = {
        ARTICLE: "\u6587\u7AE0",
        LESSON: "\u8BFE\u7A0B",
        EXERCISE: "\u7EC3\u4E60",
        NOTE: "\u7B14\u8BB0"
      };
      return typeMap[type] || type;
    };
    const formatDate = (date) => {
      if (!date) return "\u672A\u77E5";
      return new Date(date).toLocaleString();
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<!--[--><div class="flex" data-v-874cd033>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6" data-v-874cd033><div class="flex justify-between items-center mb-6" data-v-874cd033><h1 class="text-2xl font-bold" data-v-874cd033>\u5185\u5BB9\u9884\u89C8</h1><div class="flex space-x-3" data-v-874cd033><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded" data-v-874cd033>\u7F16\u8F91</button><button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded" data-v-874cd033> \u8FD4\u56DE\u5217\u8868 </button></div></div>`);
      if (loading.value) {
        _push(`<div class="text-center py-10" data-v-874cd033><p data-v-874cd033>\u52A0\u8F7D\u4E2D...</p></div>`);
      } else if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" data-v-874cd033><p class="font-bold" data-v-874cd033>\u52A0\u8F7D\u5931\u8D25:</p><p data-v-874cd033>${ssrInterpolate(error.value)}</p><button class="mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs" data-v-874cd033> \u91CD\u8BD5 </button></div>`);
      } else {
        _push(`<div data-v-874cd033><div class="bg-white shadow-md rounded p-6 mb-6" data-v-874cd033><div class="grid grid-cols-1 md:grid-cols-2 gap-4" data-v-874cd033><div data-v-874cd033><h3 class="text-sm font-medium text-gray-500" data-v-874cd033>\u6807\u9898</h3><p class="mt-1 text-lg" data-v-874cd033>${ssrInterpolate(content.title || "\u65E0\u6807\u9898")}</p></div><div data-v-874cd033><h3 class="text-sm font-medium text-gray-500" data-v-874cd033>\u7C7B\u578B</h3><p class="mt-1" data-v-874cd033>${ssrInterpolate(formatContentType(content.type))}</p></div><div data-v-874cd033><h3 class="text-sm font-medium text-gray-500" data-v-874cd033>\u680F\u76EE</h3><div class="mt-1 flex flex-wrap gap-2" data-v-874cd033><!--[-->`);
        ssrRenderList(content.categories, (category, index) => {
          _push(`<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded" data-v-874cd033>${ssrInterpolate(category)}</span>`);
        });
        _push(`<!--]-->`);
        if (!content.categories || content.categories.length === 0) {
          _push(`<span class="text-gray-500" data-v-874cd033> \u65E0\u680F\u76EE </span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div><div data-v-874cd033><h3 class="text-sm font-medium text-gray-500" data-v-874cd033>\u521B\u5EFA\u65F6\u95F4</h3><p class="mt-1" data-v-874cd033>${ssrInterpolate(formatDate(content.createdAt))}</p></div></div></div><div class="bg-white shadow-md rounded p-6 mb-6" data-v-874cd033><h2 class="text-lg font-medium text-gray-700 mb-4" data-v-874cd033>\u53D1\u97F3\u63A7\u5236</h2><div class="flex items-center space-x-4 mb-4" data-v-874cd033><button class="${ssrRenderClass([autoSpeak.value ? "bg-green-500 hover:bg-green-600 text-white" : "bg-gray-200 hover:bg-gray-300 text-gray-700", "px-4 py-2 rounded"])}" data-v-874cd033>${ssrInterpolate(autoSpeak.value ? "\u81EA\u52A8\u53D1\u97F3\u5DF2\u5F00\u542F" : "\u81EA\u52A8\u53D1\u97F3\u5DF2\u5173\u95ED")}</button><select class="border rounded px-3 py-2" data-v-874cd033><option value="0.8" data-v-874cd033${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "0.8") : ssrLooseEqual(speechRate.value, "0.8")) ? " selected" : ""}>\u6162\u901F (0.8x)</option><option value="1" data-v-874cd033${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "1") : ssrLooseEqual(speechRate.value, "1")) ? " selected" : ""}>\u6B63\u5E38 (1.0x)</option><option value="1.2" data-v-874cd033${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "1.2") : ssrLooseEqual(speechRate.value, "1.2")) ? " selected" : ""}>\u5FEB\u901F (1.2x)</option></select></div>`);
        if (isChrome.value) {
          _push(`<div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800" data-v-874cd033><p class="flex items-center" data-v-874cd033><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" data-v-874cd033><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" data-v-874cd033></path></svg> \u63D0\u793A\uFF1A\u5982\u679CChrome\u6D4F\u89C8\u5668\u65E0\u6CD5\u6B63\u5E38\u53D1\u97F3\uFF0C\u8BF7\u5C1D\u8BD5\u4F7F\u7528Microsoft Edge\u6D4F\u89C8\u5668\u8BBF\u95EE\u672C\u9875\u9762\u3002 </p></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="bg-white shadow-md rounded p-6" data-v-874cd033><h2 class="text-lg font-medium text-gray-700 mb-4" data-v-874cd033>\u5185\u5BB9\u6B63\u6587</h2><div class="prose max-w-none quill-content" data-v-874cd033>${(_a = processedContent.value) != null ? _a : ""}</div>`);
        if (!content.body || content.body.trim() === "") {
          _push(`<pre class="text-gray-500 mt-2" data-v-874cd033>            \u65E0\u5185\u5BB9
          </pre>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      }
      _push(`</div></div>`);
      if (showPronunciationModal.value) {
        _push(`<div class="fixed inset-0 flex items-center justify-center z-50" data-v-874cd033><div class="absolute inset-0 bg-black bg-opacity-50" data-v-874cd033></div><div class="bg-white rounded-lg p-6 shadow-xl z-10 max-w-md w-full" data-v-874cd033><div class="flex justify-between items-center mb-4" data-v-874cd033><h3 class="text-xl font-bold" data-v-874cd033>\u5355\u8BCD\u53D1\u97F3</h3><button class="text-gray-500 hover:text-gray-700" data-v-874cd033><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-874cd033><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" data-v-874cd033></path></svg></button></div><div class="mb-6" data-v-874cd033><div class="text-center mb-4" data-v-874cd033><span class="text-2xl font-bold" data-v-874cd033>${ssrInterpolate(currentWord.value)}</span><div class="text-gray-600 mt-1" data-v-874cd033>${ssrInterpolate(currentPronunciation.value)}</div></div><div class="flex justify-center flex-col items-center" data-v-874cd033><button class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full flex items-center mb-3" data-v-874cd033><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-874cd033><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.414l-2.829-2.828a4 4 0 015.657-5.657l2.828 2.829m7.072 7.072l2.828 2.829a4 4 0 11-5.657 5.657l-2.828-2.829" data-v-874cd033></path></svg> \u70B9\u51FB\u64AD\u653E\u53D1\u97F3 </button></div><div class="mt-6 text-sm text-gray-500" data-v-874cd033><p class="font-bold text-orange-500" data-v-874cd033>\u6CE8\u610F\uFF1AChrome\u6D4F\u89C8\u5668\u53EF\u80FD\u65E0\u6CD5\u6B63\u5E38\u53D1\u97F3</p><ul class="list-disc pl-5 mt-2" data-v-874cd033><li data-v-874cd033>Chrome\u6D4F\u89C8\u5668\u5BF9Web Speech API\u6709\u4E25\u683C\u9650\u5236\uFF0C\u53EF\u80FD\u5BFC\u81F4\u53D1\u97F3\u529F\u80FD\u65E0\u6CD5\u6B63\u5E38\u5DE5\u4F5C</li><li class="font-bold" data-v-874cd033>\u63A8\u8350\u4F7F\u7528Microsoft Edge\u6D4F\u89C8\u5668\u8BBF\u95EE\u672C\u9875\u9762\u83B7\u5F97\u6700\u4F73\u4F53\u9A8C</li><li data-v-874cd033>\u60A8\u4E5F\u53EF\u4EE5\u591A\u6B21\u70B9\u51FB&quot;\u64AD\u653E\u53D1\u97F3&quot;\u6309\u94AE\u5C1D\u8BD5\u6FC0\u6D3B\u53D1\u97F3\u529F\u80FD</li><li data-v-874cd033>\u786E\u4FDD\u60A8\u5DF2\u6388\u4E88\u7F51\u7AD9\u97F3\u9891\u6743\u9650\uFF08\u70B9\u51FB\u5730\u5740\u680F\u5DE6\u4FA7\u7684\u9501\u56FE\u6807\u68C0\u67E5\uFF09</li></ul></div></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/content/preview/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _id_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-874cd033"]]);

export { _id_ as default };
//# sourceMappingURL=_id_-D1M1poIx.mjs.map
