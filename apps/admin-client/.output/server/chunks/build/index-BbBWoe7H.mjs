import { defineComponent, ref, mergeProps, useSSRContext } from 'vue';
import { ssr<PERSON><PERSON><PERSON><PERSON><PERSON>, ssrRenderList, ssrInterpolate, ssrRenderClass, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './CreateCourseModal-CREXwZJc.mjs';
import { a as api } from './api-BjJAe0gw.mjs';
import './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './LoadingSpinner-Bp5Uxtv1.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const coursePacks = ref([]);
    const selectedCourse = ref(null);
    const showCreateModal = ref(false);
    const loading = ref(false);
    const fetchCoursePacks = async () => {
      loading.value = true;
      try {
        const data = await api.get("/api/admin/courses/tree");
        coursePacks.value = data || [];
      } catch (error) {
        console.error("\u83B7\u53D6\u8BFE\u7A0B\u5305\u5931\u8D25:", error);
      } finally {
        loading.value = false;
      }
    };
    const refresh = () => {
      fetchCoursePacks();
    };
    const refreshCourse = async () => {
      if (!selectedCourse.value) return;
      try {
        const data = await api.get(`/api/admin/courses/${selectedCourse.value.id}`);
        if (data) {
          selectedCourse.value = data;
          refresh();
        }
      } catch (error) {
        console.error("\u5237\u65B0\u8BFE\u7A0B\u6570\u636E\u5931\u8D25:", error);
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-4" }, _attrs))}><h1 class="text-2xl font-bold mb-4">\u8BFE\u7A0B\u7BA1\u7406</h1><div class="flex"><div class="w-1/3 border-r p-4">`);
      if (loading.value) {
        _push(`<div class="text-center py-4">\u52A0\u8F7D\u4E2D...</div>`);
      } else if (coursePacks.value.length === 0) {
        _push(`<div class="text-center py-4"><p>\u6682\u65E0\u8BFE\u7A0B\u5305\u6570\u636E</p></div>`);
      } else {
        _push(`<div><!--[-->`);
        ssrRenderList(coursePacks.value, (pack) => {
          _push(`<div class="mb-4"><div class="font-bold text-lg">${ssrInterpolate(pack.title)}</div><ul class="pl-2 mt-2"><!--[-->`);
          ssrRenderList(pack.courses, (course) => {
            _push(`<li class="${ssrRenderClass([{ "border-blue-500 bg-blue-50": selectedCourse.value && selectedCourse.value.id === course.id, "border-gray-200": !selectedCourse.value || selectedCourse.value.id !== course.id }, "border-l-2 pl-3 py-1 mb-1 hover:bg-gray-50"])}"><div class="flex justify-between items-center"><span class="cursor-pointer flex-grow">${ssrInterpolate(course.title)}</span><div class="flex space-x-1"><button class="btn btn-xs btn-outline">\u7F16\u8F91</button><button class="btn btn-xs btn-error">\u5220\u9664</button></div></div></li>`);
          });
          _push(`<!--]--></ul></div>`);
        });
        _push(`<!--]--></div>`);
      }
      _push(`<button class="mt-4 btn btn-primary">\u521B\u5EFA\u65B0\u8BFE\u7A0B</button></div><div class="flex-1 p-4">`);
      if (!selectedCourse.value) {
        _push(`<div class="text-center py-8 text-gray-500"><p>\u8BF7\u4ECE\u5DE6\u4FA7\u9009\u62E9\u4E00\u4E2A\u8BFE\u7A0B\u67E5\u770B\u8BE6\u60C5</p></div>`);
      } else {
        _push(ssrRenderComponent(_sfc_main$1, {
          course: selectedCourse.value,
          onUpdated: refreshCourse
        }, null, _parent));
      }
      _push(`</div></div>`);
      if (showCreateModal.value) {
        _push(ssrRenderComponent(_sfc_main$2, {
          onClose: ($event) => showCreateModal.value = false,
          onCreated: refresh,
          coursePacks: coursePacks.value
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/course/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-BbBWoe7H.mjs.map
