import { defineComponent, mergeProps, ref, watch, readonly, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssr<PERSON><PERSON>eEqual, ssrRender<PERSON>ist, ssrInterpolate } from 'vue/server-renderer';
import { h as defineStore } from './server.mjs';
import { useRouter } from 'vue-router';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const useCoursePackStore = defineStore("coursePack", {
  state: () => ({
    coursePacks: [],
    currentCoursePack: null,
    loading: false,
    error: null
  }),
  actions: {
    async fetchCoursePacks() {
      this.loading = true;
      try {
        this.coursePacks = [];
        this.error = null;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "Unknown error";
      } finally {
        this.loading = false;
      }
    },
    async createCoursePack(data) {
      this.loading = true;
      try {
        const newCoursePack = {
          id: Date.now().toString(),
          name: data.name || "",
          description: data.description,
          coverImage: data.coverImage,
          createdAt: (/* @__PURE__ */ new Date()).toISOString(),
          updatedAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        this.coursePacks.push(newCoursePack);
        return newCoursePack;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "Unknown error";
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async updateCoursePack(id, data) {
      this.loading = true;
      try {
        const index = this.coursePacks.findIndex((cp) => cp.id === id);
        if (index !== -1) {
          this.coursePacks[index] = { ...this.coursePacks[index], ...data, updatedAt: (/* @__PURE__ */ new Date()).toISOString() };
        }
      } catch (error) {
        this.error = error instanceof Error ? error.message : "Unknown error";
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async deleteCoursePack(id) {
      this.loading = true;
      try {
        this.coursePacks = this.coursePacks.filter((cp) => cp.id !== id);
      } catch (error) {
        this.error = error instanceof Error ? error.message : "Unknown error";
        throw error;
      } finally {
        this.loading = false;
      }
    },
    setCurrentCoursePack(coursePack) {
      this.currentCoursePack = coursePack;
    }
  },
  getters: {
    getCoursePackById: (state) => (id) => {
      return state.coursePacks.find((cp) => cp.id === id);
    }
  }
});
const useImageApi = () => {
  const uploading = ref(false);
  const error = ref(null);
  const uploadImage = async (file) => {
    uploading.value = true;
    error.value = null;
    try {
      const formData = new FormData();
      formData.append("image", file);
      await new Promise((resolve) => setTimeout(resolve, 1e3));
      const response = {
        success: true,
        url: `/uploads/images/${Date.now()}-${file.name}`,
        filename: file.name
      };
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Upload failed";
      error.value = errorMessage;
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      uploading.value = false;
    }
  };
  const deleteImage = async (url) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500));
      return true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "Delete failed";
      return false;
    }
  };
  const getImageUrl = (filename) => {
    if (filename.startsWith("http")) {
      return filename;
    }
    return `/uploads/images/${filename}`;
  };
  const validateImage = (file) => {
    const maxSize = 5 * 1024 * 1024;
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: "Only JPEG, PNG, GIF, and WebP images are allowed"
      };
    }
    if (file.size > maxSize) {
      return {
        valid: false,
        error: "Image size must be less than 5MB"
      };
    }
    return { valid: true };
  };
  return {
    uploading: readonly(uploading),
    error: readonly(error),
    uploadImage,
    deleteImage,
    getImageUrl,
    validateImage
  };
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "ImageManager",
  __ssrInlineRender: true,
  setup(__props) {
    const frontLogo = ref(null);
    const backLogo = ref("");
    const coursePackCover = ref(null);
    const courseCover = ref(null);
    const selectedCoursePackId = ref("");
    const selectedCoursePackForCourse = ref("");
    const selectedCourseId = ref("");
    const coursePacks = ref([]);
    const courses = ref([]);
    ref({
      frontLogo: null,
      backLogo: null,
      coursePackCover: null,
      courseCover: null
    });
    useCoursePackStore();
    const imageApi = useImageApi();
    watch(selectedCoursePackId, async (newValue) => {
      if (newValue) {
        await loadCoursePackCover(newValue);
      } else {
        coursePackCover.value = null;
      }
    });
    watch(selectedCourseId, async (newValue) => {
      if (newValue) {
        await loadCourseCover(newValue);
      } else {
        courseCover.value = null;
      }
    });
    async function loadCoursePackCover(coursePackId) {
      try {
        const coverInfo = await imageApi.getImageInfo("coursePack", coursePackId);
        if (coverInfo && coverInfo.url) {
          coursePackCover.value = coverInfo.url;
          return;
        }
        const pack = coursePacks.value.find((p) => p.id === coursePackId);
        if (pack && pack.cover) {
          coursePackCover.value = pack.cover;
        } else {
          coursePackCover.value = null;
        }
      } catch (error) {
        console.error("\u52A0\u8F7D\u8BFE\u7A0B\u5305\u5C01\u9762\u5931\u8D25:", error);
        coursePackCover.value = null;
      }
    }
    async function loadCourseCover(courseId) {
      try {
        const coverInfo = await imageApi.getImageInfo("course", courseId);
        if (coverInfo && coverInfo.url) {
          courseCover.value = coverInfo.url;
          return;
        }
        const course = courses.value.find((c) => c.id === courseId);
        if (course && course.cover) {
          courseCover.value = course.cover;
        } else {
          courseCover.value = null;
        }
      } catch (error) {
        console.error("\u52A0\u8F7D\u8BFE\u7A0B\u5C01\u9762\u5931\u8D25:", error);
        courseCover.value = null;
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full" }, _attrs))}><h2 class="mb-6 text-2xl font-bold">\u56FE\u7247\u7BA1\u7406</h2><div class="mb-8 rounded-lg border border-gray-200 p-6 dark:border-gray-700"><h3 class="mb-4 text-xl font-semibold">Logo \u7BA1\u7406</h3><div class="grid grid-cols-1 gap-6 md:grid-cols-2"><div class="space-y-4"><h4 class="font-medium">\u524D\u7AEF Logo</h4><div class="flex h-32 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600">`);
      if (frontLogo.value) {
        _push(`<img${ssrRenderAttr("src", frontLogo.value)} alt="\u524D\u7AEF Logo" class="h-full max-h-24 object-contain">`);
      } else {
        _push(`<div class="text-center text-gray-500"><p>\u6682\u65E0\u56FE\u7247</p></div>`);
      }
      _push(`</div><div class="flex space-x-2"><label class="flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"><span>\u4E0A\u4F20\u56FE\u7247</span><input type="file" class="hidden" accept="image/*"></label>`);
      if (frontLogo.value) {
        _push(`<button class="rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"> \u91CD\u7F6E </button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div><div class="space-y-4"><h4 class="font-medium">\u540E\u7AEF Logo</h4><div class="flex h-32 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600">`);
      if (backLogo.value) {
        _push(`<img${ssrRenderAttr("src", backLogo.value)} alt="\u540E\u7AEF Logo" class="h-full max-h-24 object-contain">`);
      } else {
        _push(`<div class="text-center text-gray-500"><p>\u6682\u65E0\u56FE\u7247</p></div>`);
      }
      _push(`</div><div class="flex space-x-2"><label class="flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"><span>\u4E0A\u4F20\u56FE\u7247</span><input type="file" class="hidden" accept="image/*"></label>`);
      if (backLogo.value) {
        _push(`<button class="rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"> \u91CD\u7F6E </button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div></div><div class="mb-8 rounded-lg border border-gray-200 p-6 dark:border-gray-700"><h3 class="mb-4 text-xl font-semibold">\u8BFE\u7A0B\u5305\u5C01\u9762\u7BA1\u7406</h3><div class="mb-4 space-y-2"><select class="w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800"><option value=""${ssrIncludeBooleanAttr(Array.isArray(selectedCoursePackId.value) ? ssrLooseContain(selectedCoursePackId.value, "") : ssrLooseEqual(selectedCoursePackId.value, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u8BFE\u7A0B\u5305</option><!--[-->`);
      ssrRenderList(coursePacks.value, (pack) => {
        _push(`<option${ssrRenderAttr("value", pack.id)}${ssrIncludeBooleanAttr(Array.isArray(selectedCoursePackId.value) ? ssrLooseContain(selectedCoursePackId.value, pack.id) : ssrLooseEqual(selectedCoursePackId.value, pack.id)) ? " selected" : ""}>${ssrInterpolate(pack.title)}</option>`);
      });
      _push(`<!--]--></select></div>`);
      if (selectedCoursePackId.value) {
        _push(`<div class="space-y-4"><div class="flex h-48 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600">`);
        if (coursePackCover.value) {
          _push(`<img${ssrRenderAttr("src", coursePackCover.value)} alt="\u8BFE\u7A0B\u5305\u5C01\u9762" class="h-full max-h-40 object-contain">`);
        } else {
          _push(`<div class="text-center text-gray-500"><p>\u6682\u65E0\u5C01\u9762\u56FE\u7247</p></div>`);
        }
        _push(`</div><div class="flex space-x-2"><label class="flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"><span>\u4E0A\u4F20\u5C01\u9762</span><input type="file" class="hidden" accept="image/*"></label>`);
        if (coursePackCover.value) {
          _push(`<button class="rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"> \u91CD\u7F6E </button>`);
        } else {
          _push(`<!---->`);
        }
        if (selectedCoursePackId.value && coursePackCover.value) {
          _push(`<button class="rounded-md bg-green-500 px-4 py-2 text-white hover:bg-green-600"> \u4FDD\u5B58\u66F4\u6539 </button>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="rounded-lg border border-gray-200 p-6 dark:border-gray-700"><h3 class="mb-4 text-xl font-semibold">\u8BFE\u7A0B\u5C01\u9762\u7BA1\u7406</h3><div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2"><div class="space-y-2"><label class="block font-medium">\u9009\u62E9\u8BFE\u7A0B\u5305</label><select class="w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800"><option value=""${ssrIncludeBooleanAttr(Array.isArray(selectedCoursePackForCourse.value) ? ssrLooseContain(selectedCoursePackForCourse.value, "") : ssrLooseEqual(selectedCoursePackForCourse.value, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u8BFE\u7A0B\u5305</option><!--[-->`);
      ssrRenderList(coursePacks.value, (pack) => {
        _push(`<option${ssrRenderAttr("value", pack.id)}${ssrIncludeBooleanAttr(Array.isArray(selectedCoursePackForCourse.value) ? ssrLooseContain(selectedCoursePackForCourse.value, pack.id) : ssrLooseEqual(selectedCoursePackForCourse.value, pack.id)) ? " selected" : ""}>${ssrInterpolate(pack.title)}</option>`);
      });
      _push(`<!--]--></select></div><div class="space-y-2"><label class="block font-medium">\u9009\u62E9\u8BFE\u7A0B</label><select class="w-full rounded-md border border-gray-300 p-2 dark:border-gray-600 dark:bg-gray-800"${ssrIncludeBooleanAttr(!selectedCoursePackForCourse.value) ? " disabled" : ""}><option value=""${ssrIncludeBooleanAttr(Array.isArray(selectedCourseId.value) ? ssrLooseContain(selectedCourseId.value, "") : ssrLooseEqual(selectedCourseId.value, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u8BFE\u7A0B</option><!--[-->`);
      ssrRenderList(courses.value, (course) => {
        _push(`<option${ssrRenderAttr("value", course.id)}${ssrIncludeBooleanAttr(Array.isArray(selectedCourseId.value) ? ssrLooseContain(selectedCourseId.value, course.id) : ssrLooseEqual(selectedCourseId.value, course.id)) ? " selected" : ""}>${ssrInterpolate(course.title)}</option>`);
      });
      _push(`<!--]--></select></div></div>`);
      if (selectedCourseId.value) {
        _push(`<div class="mt-4 space-y-4"><div class="flex h-48 w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4 dark:border-gray-600">`);
        if (courseCover.value) {
          _push(`<img${ssrRenderAttr("src", courseCover.value)} alt="\u8BFE\u7A0B\u5C01\u9762" class="h-full max-h-40 object-contain">`);
        } else {
          _push(`<div class="text-center text-gray-500"><p>\u6682\u65E0\u5C01\u9762\u56FE\u7247</p></div>`);
        }
        _push(`</div><div class="flex space-x-2"><label class="flex cursor-pointer items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"><span>\u4E0A\u4F20\u5C01\u9762</span><input type="file" class="hidden" accept="image/*"></label>`);
        if (courseCover.value) {
          _push(`<button class="rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"> \u91CD\u7F6E </button>`);
        } else {
          _push(`<!---->`);
        }
        if (selectedCourseId.value && courseCover.value) {
          _push(`<button class="rounded-md bg-green-500 px-4 py-2 text-white hover:bg-green-600"> \u4FDD\u5B58\u66F4\u6539 </button>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="mt-8 flex justify-end"><button class="rounded-md bg-purple-600 px-6 py-3 text-white shadow-md hover:bg-purple-700"> \u4FDD\u5B58\u6240\u6709\u66F4\u6539 </button></div></div>`);
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/admin/ImageManager.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
ref(false);
ref("");
function useAdminGuard() {
  useRouter();
  const isAdmin = ref(false);
  const isLoading = ref(true);
  return {
    isAdmin,
    isLoading
  };
}
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "images",
  __ssrInlineRender: true,
  setup(__props) {
    useAdminGuard();
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "container mx-auto px-4 py-8" }, _attrs))}><h1 class="mb-8 text-3xl font-bold">\u7F51\u7AD9\u56FE\u7247\u7BA1\u7406</h1>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/images.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=images-BNIL4ERx.mjs.map
