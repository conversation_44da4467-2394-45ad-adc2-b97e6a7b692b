import { ref, watch, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "QuillEditor",
  __ssrInlineRender: true,
  props: {
    content: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: "\u8BF7\u8F93\u5165\u5185\u5BB9..."
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:content", "editor-change", "add-pronunciation"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    ref(null);
    ref(null);
    let quill = null;
    const showHtmlEditor = ref(false);
    const htmlContent = ref("");
    watch(() => props.content, (newContent) => {
    });
    watch(() => props.readOnly, (newValue) => {
    });
    __expose({
      getQuill: () => quill,
      getHTML: () => "",
      getText: () => "",
      setContent: (content) => {
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "quill-editor-container" }, _attrs))} data-v-8a8c5be6><div class="quill-editor" data-v-8a8c5be6></div>`);
      if (showHtmlEditor.value) {
        _push(`<div class="html-editor-container" data-v-8a8c5be6><textarea class="html-textarea" data-v-8a8c5be6>${ssrInterpolate(htmlContent.value)}</textarea><div class="html-editor-buttons" data-v-8a8c5be6><button class="html-editor-button" data-v-8a8c5be6>\u5E94\u7528HTML\u66F4\u6539</button><button class="html-editor-button" data-v-8a8c5be6>\u5173\u95EDHTML\u7F16\u8F91\u5668</button></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/QuillEditor.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const QuillEditor = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-8a8c5be6"]]);

export { QuillEditor as Q };
//# sourceMappingURL=QuillEditor-DzFX0Zx0.mjs.map
