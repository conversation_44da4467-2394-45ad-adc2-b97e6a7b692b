import { i as axios } from './server.mjs';

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, key + "" , value);
const _ToastManager = class _ToastManager2 {
  constructor() {
  }
  static getInstance() {
    if (!_ToastManager2.instance) {
      _ToastManager2.instance = new _ToastManager2();
    }
    return _ToastManager2.instance;
  }
  createToast(message, type, duration = 3e3) {
    const toast = (void 0).createElement("div");
    toast.textContent = message;
    const colorMap = {
      "success": "#4CAF50",
      "error": "#F44336",
      "warning": "#FF9800",
      "info": "#2196F3"
    };
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: ${colorMap[type]};
      color: white;
      padding: 15px;
      border-radius: 5px;
      z-index: 1000;
      transition: opacity 0.3s;
    `;
    (void 0).body.appendChild(toast);
    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        (void 0).body.removeChild(toast);
      }, 300);
    }, duration);
  }
  success(message, duration = 3e3) {
    this.createToast(message, "success", duration);
  }
  error(message, duration = 3e3) {
    this.createToast(message, "error", duration);
  }
  warning(message, duration = 3e3) {
    this.createToast(message, "warning", duration);
  }
  info(message, duration = 3e3) {
    this.createToast(message, "info", duration);
  }
};
__publicField(_ToastManager, "instance");
let ToastManager = _ToastManager;
function useToast() {
  return ToastManager.getInstance();
}
const baseURL = "http://localhost:5000";
const apiClient = axios.create({
  baseURL,
  timeout: 1e4
  // 10秒超时
});
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("adminToken");
    if (token) {
      config.headers["Authorization"] = token.startsWith("Bearer ") ? token : `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    const toast = useToast();
    const { response, message } = error;
    if (response) {
      switch (response.status) {
        case 400:
          toast.error("\u8BF7\u6C42\u9519\u8BEF");
          break;
        case 401:
          toast.error("\u672A\u6388\u6743\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55");
          break;
        case 403:
          toast.error("\u62D2\u7EDD\u8BBF\u95EE");
          break;
        case 404:
          toast.error("\u8BF7\u6C42\u5730\u5740\u4E0D\u5B58\u5728");
          break;
        case 500:
          toast.error("\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF");
          break;
        default:
          toast.error(`\u8FDE\u63A5\u9519\u8BEF ${response.status}`);
      }
    } else if (message === "Network Error") {
      toast.error("\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25");
    } else {
      toast.error("\u672A\u77E5\u9519\u8BEF");
    }
    return Promise.reject(error);
  }
);
const useApi = {
  async get(url, config = {}) {
    try {
      const response = await apiClient.get(url, config);
      return response.data;
    } catch (error) {
      console.error("API GET Error:", error);
      throw error;
    }
  },
  async post(url, data = {}, config = {}) {
    try {
      const response = await apiClient.post(url, data, config);
      return response.data;
    } catch (error) {
      console.error("API POST Error:", error);
      throw error;
    }
  },
  async patch(url, data = {}, config = {}) {
    try {
      const response = await apiClient.patch(url, data, config);
      return response.data;
    } catch (error) {
      console.error("API PATCH Error:", error);
      throw error;
    }
  },
  async delete(url, config = {}) {
    try {
      const response = await apiClient.delete(url, config);
      return response.data;
    } catch (error) {
      console.error("API DELETE Error:", error);
      throw error;
    }
  }
};

export { useToast as a, useApi as u };
//# sourceMappingURL=useApi-Dd_IPgYv.mjs.map
