import { ref, computed, watch, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrInterpolate, ssrR<PERSON>List, ssrRenderClass } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { a as api } from './api-BjJAe0gw.mjs';
import { a as useToast } from './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const toast = useToast();
    const contentList = ref([]);
    const filterType = ref("");
    const loading = ref(false);
    const error = ref("");
    const showDebug = ref(false);
    const debugInfo = ref({});
    const categorySearch = ref("");
    ref(null);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalItems = ref(0);
    const fetchContent = async () => {
      loading.value = true;
      error.value = "";
      try {
        const params = new URLSearchParams();
        params.append("page", currentPage.value.toString());
        params.append("pageSize", pageSize.value.toString());
        if (filterType.value) {
          params.append("type", filterType.value);
        }
        if (categorySearch.value) {
          params.append("category", categorySearch.value);
        }
        const data = await api.get(`/api/admin/content?${params.toString()}`);
        contentList.value = data.contents || [];
        if (data.pagination) {
          const { total, page, pageSize: size, pageCount } = data.pagination;
          currentPage.value = page;
          pageSize.value = size;
          totalItems.value = total;
        }
      } catch (err) {
        console.error("Error fetching content:", err);
        error.value = err.message || "\u672A\u77E5\u9519\u8BEF";
        toast.error("\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u5931\u8D25: " + error.value);
      } finally {
        loading.value = false;
      }
    };
    const totalPages = computed(() => {
      return Math.max(1, Math.ceil(totalItems.value / pageSize.value));
    });
    const paginatedContent = computed(() => {
      return contentList.value;
    });
    const displayedPages = computed(() => {
      const total = totalPages.value;
      const current = currentPage.value;
      if (total <= 7) {
        return Array.from({ length: total }, (_, i) => i + 1);
      }
      const pages = [];
      pages.push(1);
      if (current <= 3) {
        pages.push(2, 3, 4, "...", total - 1, total);
      } else if (current >= total - 2) {
        pages.push("...", total - 4, total - 3, total - 2, total - 1, total);
      } else {
        pages.push("...", current - 1, current, current + 1, "...", total);
      }
      return pages;
    });
    const formatType = (type) => {
      const types = {
        "ARTICLE": "\u6587\u7AE0",
        "LESSON": "\u8BFE\u7A0B",
        "EXERCISE": "\u7EC3\u4E60",
        "NOTE": "\u7B14\u8BB0"
      };
      return types[type] || type;
    };
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN");
    };
    watch([filterType], () => {
      currentPage.value = 1;
    });
    watch([currentPage, filterType, categorySearch], () => {
      fetchContent();
    }, { deep: true });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-6" }, _attrs))}><h1 class="text-2xl font-bold mb-6">\u5185\u5BB9\u7BA1\u7406</h1><div class="flex justify-between mb-6"><div><button class="bg-blue-600 text-white px-4 py-2 rounded"> \u521B\u5EFA\u5185\u5BB9 </button></div><div class="flex space-x-2"><div class="relative"><input${ssrRenderAttr("value", categorySearch.value)} type="text" placeholder="\u641C\u7D22\u680F\u76EE..." class="border rounded px-3 py-2 pr-8">`);
      if (categorySearch.value) {
        _push(`<span class="absolute right-2 top-2 cursor-pointer text-gray-500"> \xD7 </span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><select class="border rounded px-3 py-2"><option value=""${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "") : ssrLooseEqual(filterType.value, "")) ? " selected" : ""}>\u5168\u90E8\u7C7B\u578B</option><option value="ARTICLE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "ARTICLE") : ssrLooseEqual(filterType.value, "ARTICLE")) ? " selected" : ""}>\u6587\u7AE0</option><option value="LESSON"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "LESSON") : ssrLooseEqual(filterType.value, "LESSON")) ? " selected" : ""}>\u8BFE\u7A0B</option><option value="EXERCISE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "EXERCISE") : ssrLooseEqual(filterType.value, "EXERCISE")) ? " selected" : ""}>\u7EC3\u4E60</option><option value="NOTE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "NOTE") : ssrLooseEqual(filterType.value, "NOTE")) ? " selected" : ""}>\u7B14\u8BB0</option></select></div></div>`);
      if (showDebug.value) {
        _push(`<div class="mb-6 p-4 bg-gray-100 border border-gray-300 rounded"><h3 class="font-bold mb-2">\u8C03\u8BD5\u4FE1\u606F</h3><pre class="text-xs overflow-auto max-h-40">${ssrInterpolate(debugInfo.value)}</pre><button class="mt-2 bg-gray-500 text-white px-2 py-1 text-xs rounded"> \u5237\u65B0\u72B6\u6001 </button></div>`);
      } else {
        _push(`<!---->`);
      }
      if (loading.value) {
        _push(`<div class="text-center py-10"><p>\u52A0\u8F7D\u4E2D...</p></div>`);
      } else if (contentList.value.length === 0) {
        _push(`<div class="text-center py-10"><p>\u6682\u65E0\u5185\u5BB9</p>`);
        if (error.value) {
          _push(`<div class="mt-4 p-3 bg-red-100 text-red-700 rounded-md text-sm max-w-lg mx-auto"><p class="font-bold">\u52A0\u8F7D\u5931\u8D25:</p><p>${ssrInterpolate(error.value)}</p><button class="mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"> \u91CD\u8BD5 </button><button class="mt-2 ml-2 bg-gray-600 text-white px-2 py-1 rounded text-xs">${ssrInterpolate(showDebug.value ? "\u9690\u85CF\u8C03\u8BD5" : "\u663E\u793A\u8C03\u8BD5")}</button></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<div class="bg-white shadow rounded-lg overflow-hidden"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u6807\u9898</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u7C7B\u578B</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u680F\u76EE</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u521B\u5EFA\u65F6\u95F4</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u64CD\u4F5C</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
        ssrRenderList(paginatedContent.value, (item) => {
          _push(`<tr><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(item.title || "\u65E0\u6807\u9898")}</td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(formatType(item.type))}</td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(item.categories ? item.categories.join(", ") : "-")}</td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(formatDate(item.createdAt))}</td><td class="px-6 py-4 whitespace-nowrap"><button class="text-blue-600 hover:text-blue-900 mr-3">\u7F16\u8F91</button><button class="text-red-600 hover:text-red-900">\u5220\u9664</button></td></tr>`);
        });
        _push(`<!--]--></tbody></table><div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"><div class="flex-1 flex justify-between sm:hidden"><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"])}"> \u4E0A\u4E00\u9875 </button><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"])}"> \u4E0B\u4E00\u9875 </button></div><div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"><div><p class="text-sm text-gray-700"> \u663E\u793A\u7B2C <span class="font-medium">${ssrInterpolate(contentList.value.length > 0 ? (currentPage.value - 1) * pageSize.value + 1 : 0)}</span> \u81F3 <span class="font-medium">${ssrInterpolate(Math.min(currentPage.value * pageSize.value, totalItems.value))}</span> \u6761\uFF0C\u5171 <span class="font-medium">${ssrInterpolate(totalItems.value)}</span> \u6761 </p></div><div><nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination"><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u9996\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></button><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0A\u4E00\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></button><!--[-->`);
        ssrRenderList(displayedPages.value, (page) => {
          _push(`<!--[-->`);
          if (page !== "...") {
            _push(`<button class="${ssrRenderClass([
              currentPage.value === page ? "z-10 bg-blue-50 border-blue-500 text-blue-600" : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50",
              "relative inline-flex items-center px-4 py-2 border text-sm font-medium"
            ])}">${ssrInterpolate(page)}</button>`);
          } else {
            _push(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"> ... </span>`);
          }
          _push(`<!--]-->`);
        });
        _push(`<!--]--><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0B\u4E00\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg></button><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u5C3E\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg></button></nav></div></div></div></div>`);
      }
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/content/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-D70mvaln.mjs.map
