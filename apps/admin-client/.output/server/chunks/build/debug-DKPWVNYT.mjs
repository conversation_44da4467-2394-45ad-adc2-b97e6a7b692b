import { ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate } from 'vue/server-renderer';
import { useRouter } from 'vue-router';

const _sfc_main = {
  __name: "debug",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const isLoggedIn = ref(false);
    const hasToken = ref(false);
    const token = ref("");
    const user = ref(null);
    const serverStatus = ref(null);
    const loading = ref(false);
    const error = ref("");
    const tokenPreview = computed(() => {
      if (!token.value) return "\u65E0";
      return token.value.substring(0, 10) + "...";
    });
    const userJson = computed(() => {
      return JSON.stringify(user.value, null, 2);
    });
    const serverStatusJson = computed(() => {
      return JSON.stringify(serverStatus.value, null, 2);
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-6" }, _attrs))}><h1 class="text-2xl font-bold mb-6">\u8BA4\u8BC1\u8C03\u8BD5</h1><div class="bg-white shadow rounded-lg p-6"><h2 class="text-xl font-bold mb-4">\u5BA2\u6237\u7AEF\u8BA4\u8BC1\u72B6\u6001</h2><div class="mb-6"><div class="grid grid-cols-2 gap-2"><div class="font-bold">\u662F\u5426\u5DF2\u767B\u5F55:</div><div>${ssrInterpolate(isLoggedIn.value ? "\u662F" : "\u5426")}</div><div class="font-bold">\u4EE4\u724C\u5B58\u5728:</div><div>${ssrInterpolate(hasToken.value ? "\u662F" : "\u5426")}</div><div class="font-bold">\u4EE4\u724C\u957F\u5EA6:</div><div>${ssrInterpolate(token.value ? token.value.length : 0)} \u5B57\u7B26</div><div class="font-bold">\u4EE4\u724C\u5F00\u5934:</div><div>${ssrInterpolate(tokenPreview.value)}</div></div><div class="mt-4"><h3 class="font-bold mb-2">\u7528\u6237\u4FE1\u606F:</h3><pre class="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">${ssrInterpolate(userJson.value)}</pre></div></div><div class="mb-6"><h3 class="font-bold mb-2">\u670D\u52A1\u5668\u8BA4\u8BC1\u72B6\u6001:</h3>`);
      if (loading.value) {
        _push(`<div>\u52A0\u8F7D\u4E2D...</div>`);
      } else if (error.value) {
        _push(`<div class="text-red-600">${ssrInterpolate(error.value)}</div>`);
      } else {
        _push(`<div><pre class="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">${ssrInterpolate(serverStatusJson.value)}</pre></div>`);
      }
      _push(`</div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded"> \u68C0\u67E5\u670D\u52A1\u5668\u72B6\u6001 </button><button class="bg-red-600 text-white px-4 py-2 rounded"> \u6E05\u9664\u8BA4\u8BC1 </button><button class="bg-green-600 text-white px-4 py-2 rounded"> \u53BB\u767B\u5F55 </button></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/content/debug.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=debug-DKPWVNYT.mjs.map
