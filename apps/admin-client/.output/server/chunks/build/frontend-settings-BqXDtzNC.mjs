import { ref, computed, unref, useSSRContext } from 'vue';
import { ssrR<PERSON>Att<PERSON>, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderClass } from 'vue/server-renderer';
import { d as useNuxtApp, g as useAdminState } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';

const _sfc_main = {
  __name: "frontend-settings",
  __ssrInlineRender: true,
  setup(__props) {
    const { $axios } = useNuxtApp();
    const { adminUser } = useAdminState();
    const debugMode = ref(true);
    const isSuperAdmin = computed(() => {
      console.log("\u68C0\u67E5\u8D85\u7EA7\u7BA1\u7406\u5458\u6743\u9650:", adminUser.value);
      if (!adminUser.value) return false;
      return adminUser.value.role === "superadmin";
    });
    ref(false);
    ref("");
    const categories = ref(["general", "contact", "links", "copyright", "about", "home"]);
    const categoryDisplayNames = {
      general: "\u901A\u7528\u8BBE\u7F6E",
      contact: "\u8054\u7CFB\u6211\u4EEC",
      links: "\u53CB\u60C5\u94FE\u63A5",
      copyright: "\u7248\u6743\u9875\u9762",
      about: "\u5173\u4E8E\u6211\u4EEC",
      home: "\u9996\u9875\u7ED3\u6784"
    };
    const selectedCategory = ref("general");
    const currentSettings = ref([]);
    const settingValues = ref({});
    const jsonErrors = ref({});
    const isSaving = ref(false);
    const message = ref(null);
    ref([]);
    ref(null);
    computed(() => {
      return Object.values(jsonErrors.value).some((error) => error !== null);
    });
    const isJsonObject = (value) => {
      return typeof value === "object" && value !== null && !Array.isArray(value);
    };
    const isJsonArray = (value) => {
      return Array.isArray(value);
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-e9c0c1b0>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="ml-64 p-6" data-v-e9c0c1b0><h1 class="text-2xl font-bold mb-6" data-v-e9c0c1b0>\u524D\u53F0\u8BBE\u7F6E\u7BA1\u7406</h1>`);
      if (debugMode.value) {
        _push(`<div class="mb-4 p-3 bg-gray-100 rounded text-sm" data-v-e9c0c1b0><p data-v-e9c0c1b0>\u7528\u6237\u89D2\u8272: ${ssrInterpolate(((_a = unref(adminUser)) == null ? void 0 : _a.role) || "\u672A\u77E5")}</p><p data-v-e9c0c1b0>\u662F\u5426\u8D85\u7EA7\u7BA1\u7406\u5458: ${ssrInterpolate(isSuperAdmin.value ? "\u662F" : "\u5426")}</p><p data-v-e9c0c1b0>\u5DF2\u52A0\u8F7D\u8BBE\u7F6E\u6570: ${ssrInterpolate(currentSettings.value.length)}</p><div class="mt-2 flex space-x-2" data-v-e9c0c1b0><button class="px-2 py-1 bg-blue-500 text-white rounded text-xs" data-v-e9c0c1b0> \u624B\u52A8\u5237\u65B0\u8BBE\u7F6E </button><button class="px-2 py-1 bg-green-500 text-white rounded text-xs" data-v-e9c0c1b0> \u68C0\u67E5API\u8FDE\u63A5 </button><button class="px-2 py-1 bg-gray-500 text-white rounded text-xs" data-v-e9c0c1b0> \u9690\u85CF\u8C03\u8BD5\u4FE1\u606F </button></div></div>`);
      } else {
        _push(`<div class="mb-4" data-v-e9c0c1b0><button class="text-xs text-gray-500 hover:text-gray-700" data-v-e9c0c1b0> \u663E\u793A\u8C03\u8BD5\u4FE1\u606F </button></div>`);
      }
      _push(`<div class="mb-6" data-v-e9c0c1b0><label class="block text-sm font-medium text-gray-700 mb-2" data-v-e9c0c1b0>\u8BBE\u7F6E\u7C7B\u522B</label><div class="flex space-x-2" data-v-e9c0c1b0><select class="w-full p-2 border border-gray-300 rounded" data-v-e9c0c1b0><!--[-->`);
      ssrRenderList(categories.value, (category) => {
        _push(`<option${ssrRenderAttr("value", category)} data-v-e9c0c1b0${ssrIncludeBooleanAttr(Array.isArray(selectedCategory.value) ? ssrLooseContain(selectedCategory.value, category) : ssrLooseEqual(selectedCategory.value, category)) ? " selected" : ""}>${ssrInterpolate(categoryDisplayNames[category] || category)}</option>`);
      });
      _push(`<!--]--></select><button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded" title="\u5237\u65B0\u5F53\u524D\u7C7B\u522B\u8BBE\u7F6E" data-v-e9c0c1b0><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-e9c0c1b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" data-v-e9c0c1b0></path></svg></button></div></div>`);
      if (selectedCategory.value) {
        _push(`<div class="mb-6" data-v-e9c0c1b0><div class="flex justify-between mb-4" data-v-e9c0c1b0><h2 class="text-xl font-semibold" data-v-e9c0c1b0>${ssrInterpolate(categoryDisplayNames[selectedCategory.value] || selectedCategory.value)}</h2><button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"${ssrIncludeBooleanAttr(isSaving.value) ? " disabled" : ""} data-v-e9c0c1b0>${ssrInterpolate(isSaving.value ? "\u4FDD\u5B58\u4E2D..." : "\u4FDD\u5B58\u6240\u6709\u8BBE\u7F6E")}</button></div>`);
        if (currentSettings.value.length > 0) {
          _push(`<div data-v-e9c0c1b0><!--[-->`);
          ssrRenderList(currentSettings.value, (setting, index) => {
            _push(`<div class="mb-6 p-4 border border-gray-200 rounded" data-v-e9c0c1b0><div class="mb-4" data-v-e9c0c1b0><label class="block text-sm font-medium text-gray-700 mb-2" data-v-e9c0c1b0>\u8BBE\u7F6E\u952E\u540D</label><input${ssrRenderAttr("value", setting.key)} class="w-full p-2 border border-gray-300 rounded"${ssrIncludeBooleanAttr(setting.id) ? " disabled" : ""} placeholder="\u8BF7\u8F93\u5165\u8BBE\u7F6E\u952E\u540D" data-v-e9c0c1b0></div><div class="mb-4" data-v-e9c0c1b0><label class="block text-sm font-medium text-gray-700 mb-2" data-v-e9c0c1b0>\u8BBE\u7F6E\u63CF\u8FF0</label><input${ssrRenderAttr("value", setting.description)} class="w-full p-2 border border-gray-300 rounded" placeholder="\u8BF7\u8F93\u5165\u8BBE\u7F6E\u63CF\u8FF0" data-v-e9c0c1b0></div><div class="mb-4" data-v-e9c0c1b0><label class="block text-sm font-medium text-gray-700 mb-2" data-v-e9c0c1b0>\u8BBE\u7F6E\u503C</label>`);
            if (isJsonObject(setting.value)) {
              _push(`<div data-v-e9c0c1b0><textarea class="w-full p-2 border border-gray-300 rounded font-mono h-40" placeholder="\u8BF7\u8F93\u5165JSON\u5BF9\u8C61" data-v-e9c0c1b0>${ssrInterpolate(settingValues.value[setting.key] || "")}</textarea>`);
              if (jsonErrors.value[setting.key]) {
                _push(`<p class="text-red-500 text-sm mt-1" data-v-e9c0c1b0>${ssrInterpolate(jsonErrors.value[setting.key])}</p>`);
              } else {
                _push(`<!---->`);
              }
              _push(`</div>`);
            } else if (isJsonArray(setting.value)) {
              _push(`<div data-v-e9c0c1b0><textarea class="w-full p-2 border border-gray-300 rounded font-mono h-40" placeholder="\u8BF7\u8F93\u5165JSON\u6570\u7EC4" data-v-e9c0c1b0>${ssrInterpolate(settingValues.value[setting.key] || "")}</textarea>`);
              if (jsonErrors.value[setting.key]) {
                _push(`<p class="text-red-500 text-sm mt-1" data-v-e9c0c1b0>${ssrInterpolate(jsonErrors.value[setting.key])}</p>`);
              } else {
                _push(`<!---->`);
              }
              _push(`</div>`);
            } else {
              _push(`<div data-v-e9c0c1b0><input${ssrRenderAttr("value", settingValues.value[setting.key] || "")} class="w-full p-2 border border-gray-300 rounded" placeholder="\u8BF7\u8F93\u5165\u8BBE\u7F6E\u503C" data-v-e9c0c1b0></div>`);
            }
            _push(`</div><div class="flex justify-end" data-v-e9c0c1b0><button class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded" data-v-e9c0c1b0> \u5220\u9664 </button></div></div>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<div class="text-gray-500 italic" data-v-e9c0c1b0>\u8BE5\u7C7B\u522B\u4E0B\u6682\u65E0\u8BBE\u7F6E</div>`);
        }
        _push(`<button class="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded" data-v-e9c0c1b0> \u6DFB\u52A0\u65B0\u8BBE\u7F6E </button></div>`);
      } else {
        _push(`<!---->`);
      }
      if (message.value) {
        _push(`<div class="${ssrRenderClass([
          "p-4 rounded mb-4",
          message.value.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
        ])}" data-v-e9c0c1b0>${ssrInterpolate(message.value.text)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/frontend-settings.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const frontendSettings = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-e9c0c1b0"]]);

export { frontendSettings as default };
//# sourceMappingURL=frontend-settings-BqXDtzNC.mjs.map
