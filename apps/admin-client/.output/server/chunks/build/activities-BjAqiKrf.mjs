import { defineComponent, ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "activities",
  __ssrInlineRender: true,
  setup(__props) {
    const activities = ref([]);
    const activityStats = ref(null);
    const loading = ref(true);
    const error = ref("");
    const activityTypeLabels = {
      "login": "\u767B\u5F55",
      "logout": "\u6CE8\u9500",
      "course_create": "\u521B\u5EFA\u8BFE\u7A0B",
      "course_update": "\u66F4\u65B0\u8BFE\u7A0B",
      "course_delete": "\u5220\u9664\u8BFE\u7A0B",
      "vocabulary_add": "\u6DFB\u52A0\u8BCD\u6C47",
      "vocabulary_update": "\u66F4\u65B0\u8BCD\u6C47",
      "vocabulary_delete": "\u5220\u9664\u8BCD\u6C47",
      "statement_create": "\u521B\u5EFA\u53E5\u5B50",
      "statement_update": "\u66F4\u65B0\u53E5\u5B50",
      "statement_delete": "\u5220\u9664\u53E5\u5B50"
    };
    function getActivityTypeLabel(type) {
      return activityTypeLabels[type] || type;
    }
    function getActivityTypeClass(type) {
      const typeColorMap = {
        "login": "text-green-600",
        "logout": "text-red-600",
        "course_create": "text-blue-600",
        "course_update": "text-yellow-600",
        "course_delete": "text-red-600",
        "vocabulary_add": "text-purple-600",
        "vocabulary_update": "text-indigo-600",
        "vocabulary_delete": "text-pink-600"
      };
      return typeColorMap[type] || "text-gray-600";
    }
    function formatTime(timestamp) {
      return new Date(timestamp).toLocaleString();
    }
    function getActivityDescription(activity) {
      var _a, _b, _c;
      switch (activity.type) {
        case "login":
          return `\u7528\u6237 ${(_a = activity.user) == null ? void 0 : _a.username} \u767B\u5F55`;
        case "course_create":
          return `\u521B\u5EFA\u4E86\u8BFE\u7A0B\uFF1A${(_b = activity.details) == null ? void 0 : _b.courseTitle}`;
        case "vocabulary_add":
          return `\u6DFB\u52A0\u4E86\u8BCD\u6C47\uFF1A${(_c = activity.details) == null ? void 0 : _c.word}`;
        default:
          return JSON.stringify(activity.details);
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 ml-64 p-8 flex flex-col"><h1 class="text-3xl font-bold mb-6 text-gray-800 text-center w-full">\u7CFB\u7EDF\u6D3B\u52A8</h1>`);
      if (error.value) {
        _push(`<div class="mb-6 p-4 bg-red-100 border border-red-300 text-red-700 rounded-lg w-full max-w-4xl self-center"><p>${ssrInterpolate(error.value)}</p><button class="mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"> \u91CD\u8BD5 </button></div>`);
      } else {
        _push(`<!---->`);
      }
      if (loading.value) {
        _push(`<div class="mb-6 p-4 bg-blue-100 border border-blue-300 text-blue-700 rounded-lg w-full max-w-4xl self-center"><p>\u6B63\u5728\u52A0\u8F7D\u6D3B\u52A8\u6570\u636E...</p></div>`);
      } else {
        _push(`<!---->`);
      }
      if (!loading.value && !error.value) {
        _push(`<div class="w-full max-w-6xl self-center"><div class="bg-white shadow-md rounded-lg overflow-hidden mb-6"><table class="w-full"><thead class="bg-gray-100 border-b"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u6D3B\u52A8ID</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u6D3B\u52A8\u7C7B\u578B</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u7528\u6237</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u65F6\u95F4</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">\u8BE6\u60C5</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
        ssrRenderList(activities.value, (activity) => {
          var _a;
          _push(`<tr><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(activity.id)}</td><td class="px-6 py-4 whitespace-nowrap"><span class="${ssrRenderClass(getActivityTypeClass(activity.type))}">${ssrInterpolate(getActivityTypeLabel(activity.type))}</span></td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(((_a = activity.user) == null ? void 0 : _a.username) || "\u7CFB\u7EDF")}</td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(formatTime(activity.timestamp))}</td><td class="px-6 py-4 whitespace-nowrap">${ssrInterpolate(getActivityDescription(activity))}</td></tr>`);
        });
        _push(`<!--]--></tbody></table></div>`);
        if (activityStats.value) {
          _push(`<div class="bg-white shadow-md rounded-lg p-6"><h2 class="text-xl font-bold mb-4 text-gray-800">\u6D3B\u52A8\u7EDF\u8BA1</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"><!--[-->`);
          ssrRenderList(activityStats.value.typeStats, (stat) => {
            _push(`<div class="bg-gray-100 p-4 rounded-lg"><h3 class="font-semibold text-gray-700">${ssrInterpolate(getActivityTypeLabel(stat._id))}</h3><p class="text-2xl font-bold text-blue-600">${ssrInterpolate(stat.count)}</p></div>`);
          });
          _push(`<!--]--></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/activities.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=activities-BjAqiKrf.mjs.map
