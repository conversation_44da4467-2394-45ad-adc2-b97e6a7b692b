import { ref, reactive, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "id",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const route = useRoute();
    useToast();
    route.params.id;
    const loading = ref(true);
    const error = ref("");
    const autoSpeak = ref(false);
    const speechRate = ref("1");
    ref(false);
    const content = reactive({
      id: "",
      title: "",
      type: "",
      body: "",
      categories: [],
      annotations: null,
      createdAt: "",
      updatedAt: ""
    });
    const processedContent = computed(() => {
      if (!content.body) return "";
      if (content.annotations && Array.isArray(content.annotations) && content.annotations.length > 0) {
        console.log("\u5904\u7406\u6CE8\u89E3\uFF0C\u5171\u6709\u6CE8\u89E3\u6570\u91CF:", content.annotations.length);
        const tempDiv = (void 0).createElement("div");
        tempDiv.innerHTML = content.body;
        const textContent = tempDiv.textContent || tempDiv.innerText;
        console.log("\u63D0\u53D6\u7684\u7EAF\u6587\u672C\u5185\u5BB9\u957F\u5EA6:", textContent.length);
        let newHtml = content.body;
        const sortedAnnotations = [...content.annotations].sort(
          (a, b) => b.word.length - a.word.length
        );
        sortedAnnotations.forEach((annotation) => {
          try {
            const { word, pronunciation } = annotation;
            console.log(`\u5904\u7406\u5355\u8BCD: "${word}", \u97F3\u6807: "${pronunciation}"`);
            const safeWord = word.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
            const safePronunciation = pronunciation.replace(/&/g, "&amp;").replace(/"/g, "&quot;").replace(/'/g, "&#39;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
            const annotatedSpan = `<span class="pronunciation-word" data-pronunciation="${safePronunciation}">${safeWord}</span>`;
            const wordRegex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "g");
            newHtml = newHtml.replace(wordRegex, annotatedSpan);
          } catch (e) {
            console.error(`\u5904\u7406\u5355\u8BCD ${annotation.word} \u65F6\u51FA\u9519:`, e);
          }
        });
        const tempDiv2 = (void 0).createElement("div");
        tempDiv2.innerHTML = newHtml;
        const spans = tempDiv2.querySelectorAll("span[data-pronunciation]");
        spans.forEach((span) => {
          if (!span.classList.contains("pronunciation-word")) {
            span.textContent;
            span.getAttribute("data-pronunciation");
            span.classList.add("pronunciation-word");
          }
        });
        console.log("\u5904\u7406\u5B8C\u6210\uFF0C\u8FD4\u56DE\u65B0\u7684HTML\u5185\u5BB9");
        return tempDiv2.innerHTML;
      }
      return content.body;
    });
    const formatContentType = (type) => {
      const typeMap = {
        ARTICLE: "\u6587\u7AE0",
        LESSON: "\u8BFE\u7A0B",
        EXERCISE: "\u7EC3\u4E60",
        NOTE: "\u7B14\u8BB0"
      };
      return typeMap[type] || type;
    };
    const formatDate = (date) => {
      if (!date) return "\u672A\u77E5";
      return new Date(date).toLocaleString();
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6"><div class="flex justify-between items-center mb-6"><h1 class="text-2xl font-bold">\u5185\u5BB9\u9884\u89C8</h1><div class="flex space-x-3"><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"> \u7F16\u8F91 </button><button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"> \u8FD4\u56DE\u5217\u8868 </button></div></div>`);
      if (loading.value) {
        _push(`<div class="text-center py-10"><p>\u52A0\u8F7D\u4E2D...</p></div>`);
      } else if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"><p class="font-bold">\u52A0\u8F7D\u5931\u8D25:</p><p>${ssrInterpolate(error.value)}</p><button class="mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"> \u91CD\u8BD5 </button></div>`);
      } else {
        _push(`<div><div class="bg-white shadow-md rounded p-6 mb-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><h3 class="text-sm font-medium text-gray-500">\u6807\u9898</h3><p class="mt-1 text-lg">${ssrInterpolate(content.title || "\u65E0\u6807\u9898")}</p></div><div><h3 class="text-sm font-medium text-gray-500">\u7C7B\u578B</h3><p class="mt-1">${ssrInterpolate(formatContentType(content.type))}</p></div><div><h3 class="text-sm font-medium text-gray-500">\u680F\u76EE</h3><div class="mt-1 flex flex-wrap gap-2"><!--[-->`);
        ssrRenderList(content.categories, (category, index) => {
          _push(`<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">${ssrInterpolate(category)}</span>`);
        });
        _push(`<!--]-->`);
        if (!content.categories || content.categories.length === 0) {
          _push(`<span class="text-gray-500"> \u65E0\u680F\u76EE </span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div><div><h3 class="text-sm font-medium text-gray-500">\u521B\u5EFA\u65F6\u95F4</h3><p class="mt-1">${ssrInterpolate(formatDate(content.createdAt))}</p></div></div></div><div class="bg-white shadow-md rounded p-6 mb-6"><h2 class="text-lg font-medium text-gray-700 mb-4">\u53D1\u97F3\u63A7\u5236</h2><div class="flex items-center space-x-4"><button class="${ssrRenderClass([autoSpeak.value ? "bg-green-500 hover:bg-green-600 text-white" : "bg-gray-200 hover:bg-gray-300 text-gray-700", "px-4 py-2 rounded"])}">${ssrInterpolate(autoSpeak.value ? "\u81EA\u52A8\u53D1\u97F3\u5DF2\u5F00\u542F" : "\u81EA\u52A8\u53D1\u97F3\u5DF2\u5173\u95ED")}</button><select class="border rounded px-3 py-2"><option value="0.8"${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "0.8") : ssrLooseEqual(speechRate.value, "0.8")) ? " selected" : ""}>\u6162\u901F (0.8x)</option><option value="1"${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "1") : ssrLooseEqual(speechRate.value, "1")) ? " selected" : ""}>\u6B63\u5E38 (1.0x)</option><option value="1.2"${ssrIncludeBooleanAttr(Array.isArray(speechRate.value) ? ssrLooseContain(speechRate.value, "1.2") : ssrLooseEqual(speechRate.value, "1.2")) ? " selected" : ""}>\u5FEB\u901F (1.2x)</option></select></div></div><div class="bg-white shadow-md rounded p-6"><h2 class="text-lg font-medium text-gray-700 mb-4">\u5185\u5BB9\u6B63\u6587</h2><div class="prose max-w-none quill-content">${(_a = processedContent.value) != null ? _a : ""}</div>`);
        if (!content.body || content.body.trim() === "") {
          _push(`<pre class="text-gray-500 mt-2">            \u65E0\u5185\u5BB9
          </pre>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      }
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/content/preview/id.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=id-DvMmEu8R.mjs.map
