const AudioReuseInfo_vue_vue_type_style_index_0_scoped_a74abc8f_lang = ".audio-reuse-info[data-v-a74abc8f]{margin-top:4px}audio[data-v-a74abc8f]{height:32px;min-height:32px;min-width:128px}.loading[data-v-a74abc8f]{animation:spin-a74abc8f .75s linear infinite;border:2px solid;border-radius:50%;border-right:2px solid transparent;display:inline-block;height:12px;width:12px}@keyframes spin-a74abc8f{0%{transform:rotate(0)}to{transform:rotate(1turn)}}";

export { AudioReuseInfo_vue_vue_type_style_index_0_scoped_a74abc8f_lang as A };
//# sourceMappingURL=AudioReuseInfo-styles-1.mjs-BEhd1HLG.mjs.map
