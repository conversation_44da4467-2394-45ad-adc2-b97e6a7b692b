const interopDefault = r => r.default || r || [];
const styles = {
  "node_modules/nuxt/dist/app/entry.js": () => import('./entry-styles.DDp2ngwh.mjs').then(interopDefault),
  "app.vue": () => import('./app-styles.D2EQsZbE.mjs').then(interopDefault),
  "pages/messages/index.vue": () => import('./index-styles.D3cLos7K.mjs').then(interopDefault),
  "pages/admin/content/new.vue": () => import('./new-styles.6XS38LV2.mjs').then(interopDefault),
  "pages/admin/unauthorized.vue": () => import('./unauthorized-styles.D2HIuuRe.mjs').then(interopDefault),
  "pages/admin/messages/index.vue": () => import('./index-styles.DniIkGwF.mjs').then(interopDefault),
  "pages/admin/frontend-settings.vue": () => import('./frontend-settings-styles.Zh4SoSw4.mjs').then(interopDefault),
  "pages/admin/content/preview/id.vue": () => import('./id-styles.CrqgQEJ9.mjs').then(interopDefault),
  "app.vue?vue&type=style&index=0&lang.css": () => import('./app-styles.D_OVlFaz.mjs').then(interopDefault),
  "pages/messages/index.vue?vue&type=style&index=0&scoped=12633087&lang.css": () => import('./index-styles.CgWNJ285.mjs').then(interopDefault),
  "pages/admin/content/new.vue?vue&type=style&index=0&lang.css": () => import('./new-styles.gvnLE06H.mjs').then(interopDefault),
  "pages/admin/unauthorized.vue?vue&type=style&index=0&scoped=1d6706ba&lang.css": () => import('./unauthorized-styles.RVSXM-xW.mjs').then(interopDefault),
  "pages/admin/messages/index.vue?vue&type=style&index=0&scoped=7ccde3e3&lang.css": () => import('./index-styles.CwpCAQjh.mjs').then(interopDefault),
  "pages/admin/logs.vue": () => import('./logs-styles.DCSOYPn9.mjs').then(interopDefault),
  "pages/admin/users/index.vue": () => import('./index-styles.B9BrjQL1.mjs').then(interopDefault),
  "pages/admin/courses.vue": () => import('./courses-styles.DFqep_oR.mjs').then(interopDefault),
  "pages/admin/logs.vue?vue&type=style&index=0&scoped=0cb5022d&lang.css": () => import('./logs-styles.BtXQ6FGA.mjs').then(interopDefault),
  "pages/admin/users/index.vue?vue&type=style&index=0&scoped=4ac8eb83&lang.css": () => import('./index-styles.CZGjkpT8.mjs').then(interopDefault),
  "pages/admin/courses.vue?vue&type=style&index=0&scoped=817f246d&lang.css": () => import('./courses-styles.tHkezySH.mjs').then(interopDefault),
  "pages/admin/frontend-settings.vue?vue&type=style&index=0&scoped=e9c0c1b0&lang.css": () => import('./frontend-settings-styles.BkTvbC24.mjs').then(interopDefault),
  "pages/admin/content/preview/[id].vue": () => import('./_id_-styles.jjuZrMXe.mjs').then(interopDefault),
  "plugins/toast.ts": () => import('./toast-styles.7XoLHQ3g.mjs').then(interopDefault),
  "pages/admin/content/preview/id.vue?vue&type=style&index=0&lang.css": () => import('./id-styles.O2Vt82rf.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue": () => import('./error-404-styles.CKJK0xyQ.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue": () => import('./error-500-styles.DH4SuYyS.mjs').then(interopDefault),
  "pages/admin/content/preview/[id].vue?vue&type=style&index=0&lang.css": () => import('./_id_-styles.D-jB04go.mjs').then(interopDefault),
  "pages/admin/content/preview/[id].vue?vue&type=style&index=1&scoped=874cd033&lang.css": () => import('./_id_-styles.B8e8sBfD.mjs').then(interopDefault),
  "components/QuillEditor.vue": () => import('./QuillEditor-styles.CLkXpc87.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue?vue&type=style&index=0&scoped=06403dcb&lang.css": () => import('./error-404-styles.CFLMZFZX.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue?vue&type=style&index=0&scoped=4b6f0a29&lang.css": () => import('./error-500-styles.D5zYv3n5.mjs').then(interopDefault),
  "components/AdminSidebar.vue": () => import('./AdminSidebar-styles.DDTo62_n.mjs').then(interopDefault),
  "components/AdminSidebar.vue?vue&type=style&index=0&scoped=a68a38ec&lang.css": () => import('./AdminSidebar-styles.o8653Xzk.mjs').then(interopDefault),
  "components/QuillEditor.vue?vue&type=style&index=0&scoped=8a8c5be6&lang.css": () => import('./QuillEditor-styles.AE5K2mfF.mjs').then(interopDefault),
  "layouts/admin.vue": () => import('./admin-styles.f-tEy-I-.mjs').then(interopDefault),
  "layouts/admin.vue?vue&type=style&index=0&scoped=6d2cbca8&lang.css": () => import('./admin-styles.gw_PNA7K.mjs').then(interopDefault),
  "components/AudioManagementModal.vue": () => import('./AudioManagementModal-styles.rABmd5dM.mjs').then(interopDefault),
  "components/AudioManagementModal.vue?vue&type=style&index=0&scoped=271c84ae&lang.css": () => import('./AudioManagementModal-styles.Drd93Aw5.mjs').then(interopDefault),
  "components/LoadingSpinner.vue": () => import('./LoadingSpinner-styles.BN4VJUd4.mjs').then(interopDefault),
  "components/LoadingSpinner.vue?vue&type=style&index=0&scoped=9ef416d4&lang.css": () => import('./LoadingSpinner-styles.3RcPmsql.mjs').then(interopDefault),
  "components/SimpleAudioControls.vue": () => import('./SimpleAudioControls-styles.BR92EWt_.mjs').then(interopDefault),
  "components/AudioControls.vue": () => import('./AudioControls-styles.Cs-_wYJh.mjs').then(interopDefault),
  "components/SimpleAudioControls.vue?vue&type=style&index=0&scoped=9bf431db&lang.css": () => import('./SimpleAudioControls-styles.owZF53w8.mjs').then(interopDefault),
  "components/AudioControls.vue?vue&type=style&index=0&scoped=3a3dfcc4&lang.css": () => import('./AudioControls-styles.DozaO4VT.mjs').then(interopDefault),
  "components/AudioReuseInfo.vue": () => import('./AudioReuseInfo-styles.MvRse4b4.mjs').then(interopDefault),
  "components/AudioReuseInfo.vue?vue&type=style&index=0&scoped=a74abc8f&lang.css": () => import('./AudioReuseInfo-styles.DzChycCc.mjs').then(interopDefault)
};

export { styles as default };
//# sourceMappingURL=styles.mjs.map
