import { j as defineNuxtRouteMiddleware, d as useNuxtApp, n as navigateTo } from './server.mjs';
import { computed } from 'vue';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'vue/server-renderer';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const contentPermission = defineNuxtRouteMiddleware((to) => {
  const { $auth } = useNuxtApp();
  const hasPermission = computed(() => {
    var _a, _b;
    const userRole = (_b = (_a = $auth.user) == null ? void 0 : _a.role) == null ? void 0 : _b.toUpperCase();
    const allowedRoles = ["SUPERADMIN", "ADMIN", "EDITOR"];
    return allowedRoles.includes(userRole);
  });
  if (!hasPermission.value) {
    return navigateTo("/unauthorized");
  }
});

export { contentPermission as default };
//# sourceMappingURL=content-permission-ppF1tzCr.mjs.map
