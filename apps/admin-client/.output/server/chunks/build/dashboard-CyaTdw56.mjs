import { defineComponent, defineAsyncComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "dashboard",
  __ssrInlineRender: true,
  setup(__props) {
    const StatCard = defineAsyncComponent(() => import('./StatCard-CAyiuimE.mjs'));
    const dashboardStats = ref({
      userCount: 0,
      coursePackCount: 0,
      courseCount: 0,
      vocabularyCount: 0,
      messageCount: 0,
      articleCount: 0,
      generatedAudioCount: 0,
      reusedAudioCount: 0
    });
    const loading = ref(true);
    const error = ref("");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="p-8"><h1 class="text-3xl font-bold mb-6 text-gray-800 text-center w-full">\u7BA1\u7406\u540E\u53F0\u4EEA\u8868\u76D8</h1>`);
      if (error.value) {
        _push(`<div class="mb-6 p-4 bg-red-100 border border-red-300 text-red-700 rounded-lg w-full max-w-4xl"><p>${ssrInterpolate(error.value)}</p><button class="mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"> \u91CD\u8BD5 </button></div>`);
      } else {
        _push(`<!---->`);
      }
      if (loading.value) {
        _push(`<div class="mb-6 p-4 bg-blue-100 border border-blue-300 text-blue-700 rounded-lg w-full max-w-4xl"><p>\u6B63\u5728\u52A0\u8F7D\u6570\u636E...</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6 w-full max-w-6xl">`);
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u7528\u6237\u603B\u6570",
        value: dashboardStats.value.userCount,
        icon: "\u{1F464}",
        color: "blue"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u8BFE\u7A0B\u5305\u6570\u91CF",
        value: dashboardStats.value.coursePackCount,
        icon: "\u{1F4DA}",
        color: "green"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u8BFE\u7A0B\u603B\u6570",
        value: dashboardStats.value.courseCount,
        icon: "\u{1F4D6}",
        color: "purple"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u8BCD\u6C47\u603B\u6570",
        value: dashboardStats.value.vocabularyCount,
        icon: "\u{1F524}",
        color: "orange"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u7559\u8A00\u603B\u6570",
        value: dashboardStats.value.messageCount,
        icon: "\u{1F4AC}",
        color: "red"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u6587\u7AE0\u6570\u91CF",
        value: dashboardStats.value.articleCount,
        icon: "\u{1F4DD}",
        color: "yellow"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u751F\u6210\u97F3\u9891",
        value: dashboardStats.value.generatedAudioCount,
        icon: "\u{1F3B5}",
        color: "indigo"
      }, null, _parent));
      _push(ssrRenderComponent(unref(StatCard), {
        title: "\u590D\u7528\u97F3\u9891",
        value: dashboardStats.value.reusedAudioCount,
        icon: "\u{1F504}",
        color: "pink"
      }, null, _parent));
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/dashboard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=dashboard-CyaTdw56.mjs.map
