import { defineComponent, ref, watch, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass, ssrRenderDynamicModel } from 'vue/server-renderer';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "users",
  __ssrInlineRender: true,
  setup(__props) {
    const users = ref([]);
    const loading = ref(true);
    const error = ref(null);
    const searchQuery = ref("");
    const statusFilter = ref("all");
    ref(0);
    const passwordVisible = ref(false);
    const showCreateUserModal = ref(false);
    const creating = ref(false);
    const newUser = ref({
      username: "",
      email: "",
      password: "",
      role: "user",
      isActive: true
    });
    const showEditUserModal = ref(false);
    const updating = ref(false);
    const editingUser = ref({});
    const showDeleteConfirmModal = ref(false);
    const deleting = ref(false);
    const userToDelete = ref(null);
    const pagination = ref({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    });
    useToast();
    watch(users, (newUsers) => {
      console.log("\u7528\u6237\u5217\u8868\u5DF2\u66F4\u65B0:", newUsers);
    }, { deep: true });
    watch(showCreateUserModal, (isVisible) => {
      if (isVisible) {
        newUser.value = {
          username: "",
          email: "",
          password: "",
          role: "user",
          isActive: true
        };
        passwordVisible.value = false;
      }
    });
    const paginationPages = computed(() => {
      const totalPages = pagination.value.totalPages || 1;
      const currentPage = pagination.value.page || 1;
      if (totalPages <= 5) {
        return Array.from({ length: totalPages }, (_, i2) => i2 + 1);
      }
      let pages = [currentPage];
      let i = 1;
      while (pages.length < 5 && currentPage - i > 0) {
        pages.unshift(currentPage - i);
        i++;
      }
      i = 1;
      while (pages.length < 5 && currentPage + i <= totalPages) {
        pages.push(currentPage + i);
        i++;
      }
      while (pages.length < 5) {
        if (pages[0] > 1) {
          pages.unshift(pages[0] - 1);
        } else if (pages[pages.length - 1] < totalPages) {
          pages.push(pages[pages.length - 1] + 1);
        } else {
          break;
        }
      }
      return pages;
    });
    const filteredUsers = computed(() => {
      let result = users.value;
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(
          (user) => user.username.toLowerCase().includes(query) || user.email.toLowerCase().includes(query)
        );
      }
      if (statusFilter.value !== "all") {
        const isActive = statusFilter.value === "active";
        result = result.filter((user) => user.isActive === isActive);
      }
      return result;
    });
    function formatDate(dateString) {
      if (!dateString) return "\u672A\u77E5";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return new Intl.DateTimeFormat("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      }).format(date);
    }
    function formatUserRole(role) {
      console.log("\u683C\u5F0F\u5316\u89D2\u8272:", role);
      if (!role) return "student";
      const lowerRole = role.toLowerCase();
      if (lowerRole === "admin") return "admin";
      if (lowerRole === "superadmin") return "superadmin";
      if (lowerRole === "user") return "student";
      return role;
    }
    function getUserRoleClass(role) {
      if (!role) return "bg-blue-100 text-blue-800";
      const lowerRole = role.toLowerCase();
      if (lowerRole === "admin") return "bg-purple-100 text-purple-800";
      if (lowerRole === "superadmin") return "bg-red-100 text-red-800";
      return "bg-blue-100 text-blue-800";
    }
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex min-h-screen bg-gray-100 overflow-x-hidden" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex flex-col items-center flex-1"><div class="w-full max-w-6xl p-2"><h1 class="text-2xl font-bold mb-8 text-center">\u7528\u6237\u7BA1\u7406</h1>`);
      if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"><p>${ssrInterpolate(error.value)}</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="mb-6 text-right"><button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 ml-auto"><span class="mr-2">+</span> \u521B\u5EFA\u65B0\u7528\u6237 </button></div><div class="bg-white p-4 rounded-lg shadow mb-6"><div class="flex flex-wrap gap-4"><div class="flex-1 min-w-[200px]"><input${ssrRenderAttr("value", searchQuery.value)} type="text" placeholder="\u641C\u7D22\u7528\u6237..." class="w-full px-4 py-2 border rounded-lg"></div><div class="w-auto"><select class="w-full px-4 py-2 border rounded-lg"><option value="all"${ssrIncludeBooleanAttr(Array.isArray(statusFilter.value) ? ssrLooseContain(statusFilter.value, "all") : ssrLooseEqual(statusFilter.value, "all")) ? " selected" : ""}>\u6240\u6709\u72B6\u6001</option><option value="active"${ssrIncludeBooleanAttr(Array.isArray(statusFilter.value) ? ssrLooseContain(statusFilter.value, "active") : ssrLooseEqual(statusFilter.value, "active")) ? " selected" : ""}>\u6FC0\u6D3B</option><option value="inactive"${ssrIncludeBooleanAttr(Array.isArray(statusFilter.value) ? ssrLooseContain(statusFilter.value, "inactive") : ssrLooseEqual(statusFilter.value, "inactive")) ? " selected" : ""}>\u672A\u6FC0\u6D3B</option></select></div></div></div>`);
      if (loading.value) {
        _push(`<div class="w-full flex justify-center py-12"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div></div>`);
      } else if (users.value && users.value.length > 0) {
        _push(`<div class="bg-white rounded-lg shadow overflow-hidden"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> ID </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u7528\u6237\u540D </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u90AE\u7BB1 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u89D2\u8272 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u72B6\u6001 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u521B\u5EFA\u65F6\u95F4 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u64CD\u4F5C </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
        ssrRenderList(filteredUsers.value, (user, index) => {
          _push(`<tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(user.id.slice(0, 8))}... </td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${ssrInterpolate(user.username)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(user.email)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><span class="${ssrRenderClass([getUserRoleClass(user.role), "px-2 py-1 text-xs rounded-full"])}">${ssrInterpolate(formatUserRole(user.role))}</span></td><td class="px-6 py-4 whitespace-nowrap"><button class="${ssrRenderClass([Boolean(user.isActive) ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800", "px-3 py-1 rounded-full text-xs font-semibold"])}">${ssrInterpolate(Boolean(user.isActive) ? "\u6FC0\u6D3B" : "\u672A\u6FC0\u6D3B")}</button></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(formatDate(user.createdAt))}</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><div class="flex space-x-2"><button class="text-indigo-600 hover:text-indigo-900 mr-2"> \u7F16\u8F91 </button><button class="text-red-600 hover:text-red-900"> \u5220\u9664 </button></div></td></tr>`);
        });
        _push(`<!--]--></tbody></table><div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"><div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"><div><p class="text-sm text-gray-700"> \u663E\u793A <span class="font-medium">${ssrInterpolate((pagination.value.page - 1) * pagination.value.pageSize + 1)}</span> \u81F3 <span class="font-medium">${ssrInterpolate(Math.min(pagination.value.page * pagination.value.pageSize, pagination.value.total))}</span> \u6761\uFF0C\u5171 <span class="font-medium">${ssrInterpolate(pagination.value.total)}</span> \u6761 </p></div><div><nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="\u5206\u9875"><button${ssrIncludeBooleanAttr(pagination.value.page <= 1) ? " disabled" : ""} class="${ssrRenderClass([pagination.value.page <= 1 ? "opacity-50 cursor-not-allowed" : "", "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0A\u4E00\u9875</span> \xAB </button><!--[-->`);
        ssrRenderList(paginationPages.value, (page) => {
          _push(`<span class="${ssrRenderClass([page === pagination.value.page ? "bg-blue-50 text-blue-600 z-10 border-blue-500" : "text-gray-500 hover:bg-gray-50", "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium cursor-pointer"])}">${ssrInterpolate(page)}</span>`);
        });
        _push(`<!--]--><button${ssrIncludeBooleanAttr(pagination.value.page >= pagination.value.totalPages) ? " disabled" : ""} class="${ssrRenderClass([pagination.value.page >= pagination.value.totalPages ? "opacity-50 cursor-not-allowed" : "", "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0B\u4E00\u9875</span> \xBB </button></nav></div></div></div></div>`);
      } else {
        _push(`<div class="bg-white p-8 rounded-lg shadow text-center"><p class="text-gray-500">\u6682\u65E0\u7528\u6237\u6570\u636E</p></div>`);
      }
      _push(`</div></div>`);
      if (showCreateUserModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u521B\u5EFA\u65B0\u7528\u6237</h2><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="name"> \u7528\u6237\u540D </label><input${ssrRenderAttr("value", newUser.value.username)} id="name" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="\u8BF7\u8F93\u5165\u7528\u6237\u540D"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="email"> \u90AE\u7BB1 </label><input${ssrRenderAttr("value", newUser.value.email)} id="email" type="email" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="\u8BF7\u8F93\u5165\u90AE\u7BB1" autocomplete="off"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="password"> \u5BC6\u7801 </label><div class="relative"><input${ssrRenderDynamicModel(passwordVisible.value ? "text" : "password", newUser.value.password, null)} id="password"${ssrRenderAttr("type", passwordVisible.value ? "text" : "password")} class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline pr-10" placeholder="\u8BF7\u8F93\u5165\u5BC6\u7801" autocomplete="new-password"><button type="button" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none" aria-label="\u5207\u6362\u5BC6\u7801\u53EF\u89C1\u6027">`);
        if (!passwordVisible.value) {
          _push(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>`);
        } else {
          _push(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.781-1.781zm4.261 4.262l1.514 1.514a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path><path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.742L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.064 7 9.542 7 .847 0 1.67-.105 2.454-.303z"></path></svg>`);
        }
        _push(`</button></div></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="role"> \u89D2\u8272 </label><select id="role" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><option value="user"${ssrIncludeBooleanAttr(Array.isArray(newUser.value.role) ? ssrLooseContain(newUser.value.role, "user") : ssrLooseEqual(newUser.value.role, "user")) ? " selected" : ""}>\u5B66\u751F</option><option value="ADMIN"${ssrIncludeBooleanAttr(Array.isArray(newUser.value.role) ? ssrLooseContain(newUser.value.role, "ADMIN") : ssrLooseEqual(newUser.value.role, "ADMIN")) ? " selected" : ""}>\u7BA1\u7406\u5458</option><option value="superadmin"${ssrIncludeBooleanAttr(Array.isArray(newUser.value.role) ? ssrLooseContain(newUser.value.role, "superadmin") : ssrLooseEqual(newUser.value.role, "superadmin")) ? " selected" : ""}>\u8D85\u7EA7\u7BA1\u7406\u5458</option></select></div><div class="flex items-center justify-between"><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(creating.value) ? " disabled" : ""}>${ssrInterpolate(creating.value ? "\u521B\u5EFA\u4E2D..." : "\u521B\u5EFA")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showEditUserModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u7F16\u8F91\u7528\u6237</h2><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-name"> \u7528\u6237\u540D </label><input${ssrRenderAttr("value", editingUser.value.username)} id="edit-name" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-email"> \u90AE\u7BB1 </label><input${ssrRenderAttr("value", editingUser.value.email)} id="edit-email" type="email" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-role"> \u89D2\u8272 </label><select id="edit-role" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><option value="user"${ssrIncludeBooleanAttr(Array.isArray(editingUser.value.role) ? ssrLooseContain(editingUser.value.role, "user") : ssrLooseEqual(editingUser.value.role, "user")) ? " selected" : ""}>\u5B66\u751F</option><option value="ADMIN"${ssrIncludeBooleanAttr(Array.isArray(editingUser.value.role) ? ssrLooseContain(editingUser.value.role, "ADMIN") : ssrLooseEqual(editingUser.value.role, "ADMIN")) ? " selected" : ""}>\u7BA1\u7406\u5458</option><option value="superadmin"${ssrIncludeBooleanAttr(Array.isArray(editingUser.value.role) ? ssrLooseContain(editingUser.value.role, "superadmin") : ssrLooseEqual(editingUser.value.role, "superadmin")) ? " selected" : ""}>\u8D85\u7EA7\u7BA1\u7406\u5458</option></select></div><div class="flex items-center justify-between"><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(updating.value) ? " disabled" : ""}>${ssrInterpolate(updating.value ? "\u66F4\u65B0\u4E2D..." : "\u66F4\u65B0")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showDeleteConfirmModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u786E\u8BA4\u5220\u9664</h2><p class="mb-6">\u786E\u5B9A\u8981\u5220\u9664\u7528\u6237 &quot;${ssrInterpolate((_a = userToDelete.value) == null ? void 0 : _a.username)}&quot; \u5417\uFF1F\u6B64\u64CD\u4F5C\u65E0\u6CD5\u64A4\u9500\u3002</p><div class="flex items-center justify-between"><button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(deleting.value) ? " disabled" : ""}>${ssrInterpolate(deleting.value ? "\u5220\u9664\u4E2D..." : "\u786E\u8BA4\u5220\u9664")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/users.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=users-DJh7WWWg.mjs.map
