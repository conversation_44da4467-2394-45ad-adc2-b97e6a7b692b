import { ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate } from 'vue/server-renderer';

const _sfc_main = {
  __name: "reset",
  __ssrInlineRender: true,
  setup(__props) {
    const isLoggedIn = ref(false);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-red-50" }, _attrs))}><div class="w-full max-w-md"><div class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4"><div class="mb-6 text-center"><h1 class="text-3xl font-bold text-red-600">\u767B\u5F55\u72B6\u6001\u91CD\u7F6E</h1><p class="text-gray-600 mt-2">\u6E05\u9664\u6240\u6709\u7BA1\u7406\u540E\u53F0\u767B\u5F55\u72B6\u6001</p></div><div class="mb-4 p-4 bg-gray-100 rounded"><h2 class="font-bold mb-2">\u5F53\u524D\u72B6\u6001:</h2><p>\u767B\u5F55\u72B6\u6001: <strong>${ssrInterpolate(isLoggedIn.value ? "\u5DF2\u767B\u5F55" : "\u672A\u767B\u5F55")}</strong></p></div><div class="flex flex-col space-y-2 mt-6"><button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"> \u5F7B\u5E95\u91CD\u7F6E\u767B\u5F55\u72B6\u6001 </button><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4"> \u8FD4\u56DE\u767B\u5F55\u9875 </button></div><div class="mt-6 text-sm text-gray-600"><p>\u5982\u679C\u4F60\u9047\u5230\u767B\u5F55\u95EE\u9898\uFF0C\u6B64\u5DE5\u5177\u53EF\u4EE5\u5F7B\u5E95\u6E05\u9664\u767B\u5F55\u72B6\u6001</p></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/reset.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=reset-CYa2SC-i.mjs.map
