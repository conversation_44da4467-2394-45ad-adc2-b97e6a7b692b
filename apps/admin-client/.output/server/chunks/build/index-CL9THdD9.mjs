import { defineComponent, computed, ref, resolveComponent, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderAttr, ssrRenderClass, ssrRenderList, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    const route = useRoute();
    computed(() => route.params.id);
    const user = ref({});
    const isLoading = ref(true);
    const error = ref("");
    const tabs = [
      { id: "profile", name: "\u57FA\u672C\u4FE1\u606F" },
      { id: "courses", name: "\u8BFE\u7A0B\u8BB0\u5F55" },
      { id: "actions", name: "\u8D26\u6237\u64CD\u4F5C" }
    ];
    const activeTab = ref("profile");
    const userCourses = ref([]);
    const showConfirmModal = ref(false);
    const confirmModalTitle = ref("");
    const confirmModalContent = ref("");
    const confirmModalClass = ref("");
    const pendingAction = ref("");
    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    }
    async function handleConfirmAction() {
      try {
        if (pendingAction.value === "toggleStatus") {
          const newStatus = !user.value.isActive;
          const response = await fetch(`/api/admin/users/${user.value.id}`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ...user.value,
              isActive: newStatus
            })
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          user.value.isActive = newStatus;
          console.log(`\u7528\u6237 ${user.value.id} \u72B6\u6001\u5DF2\u66F4\u65B0\u4E3A: ${user.value.isActive ? "\u6D3B\u8DC3" : "\u672A\u6FC0\u6D3B"}`);
        } else if (pendingAction.value === "resetPassword") {
          const response = await fetch(`/api/admin/users/${user.value.id}/reset-password`, {
            method: "POST"
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          console.log(`\u5DF2\u91CD\u7F6E\u7528\u6237 ${user.value.id} \u7684\u5BC6\u7801`);
        } else if (pendingAction.value === "deleteUser") {
          const response = await fetch(`/api/admin/users/${user.value.id}`, {
            method: "DELETE"
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          console.log(`\u5DF2\u5220\u9664\u7528\u6237 ${user.value.id}`);
          router.push("/admin/users");
        }
      } catch (err) {
        console.error("\u64CD\u4F5C\u5931\u8D25:", err);
      } finally {
        showConfirmModal.value = false;
        pendingAction.value = "";
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_MainMessageBox = resolveComponent("MainMessageBox");
      _push(`<div${ssrRenderAttrs(_attrs)}><div class="mb-6 flex items-center"><button class="mr-3 inline-flex items-center text-sm font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"><span class="i-ph-arrow-left-bold mr-1 h-5 w-5"></span> \u8FD4\u56DE </button><h1 class="text-2xl font-bold">\u7528\u6237\u8BE6\u60C5</h1></div>`);
      if (isLoading.value) {
        _push(`<div><div class="flex h-64 items-center justify-center"><span class="i-ph-spinner animate-spin h-10 w-10 text-purple-600"></span></div></div>`);
      } else if (error.value) {
        _push(`<div class="rounded-md bg-red-50 p-4 dark:bg-red-900/30"><div class="flex"><div class="flex-shrink-0"><span class="i-ph-warning-circle h-5 w-5 text-red-400" aria-hidden="true"></span></div><div class="ml-3"><h3 class="text-sm font-medium text-red-800 dark:text-red-200">${ssrInterpolate(error.value)}</h3></div></div></div>`);
      } else {
        _push(`<div><div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800"><div class="px-6 py-5"><div class="flex items-center"><div class="h-20 w-20 flex-shrink-0"><img class="h-20 w-20 rounded-full"${ssrRenderAttr("src", user.value.avatar)} alt="\u7528\u6237\u5934\u50CF"></div><div class="ml-5 flex-1"><h2 class="text-xl font-bold text-gray-900 dark:text-white">${ssrInterpolate(user.value.username)}</h2><p class="text-sm text-gray-500 dark:text-gray-400"><span class="i-ph-envelope mr-1 inline-block h-4 w-4 align-text-bottom"></span> ${ssrInterpolate(user.value.email)}</p><div class="mt-2 flex items-center space-x-3"><span class="${ssrRenderClass([user.value.isActive ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400", "inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium"])}">${ssrInterpolate(user.value.isActive ? "\u6D3B\u8DC3" : "\u672A\u6FC0\u6D3B")}</span><span class="inline-flex rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">${ssrInterpolate(user.value.isNewUser ? "\u65B0\u7528\u6237" : "\u8001\u7528\u6237")}</span></div></div><div><button class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:ring-gray-600 dark:hover:bg-gray-600"><span class="i-ph-pencil-simple-bold mr-1 h-4 w-4"></span> \u7F16\u8F91 </button></div></div></div><div class="border-t border-gray-200 dark:border-gray-700"><div class="flex border-b border-gray-200 dark:border-gray-700"><!--[-->`);
        ssrRenderList(tabs, (tab) => {
          _push(`<button class="${ssrRenderClass([[
            activeTab.value === tab.id ? "border-b-2 border-purple-500 text-purple-600 dark:text-purple-400" : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          ], "px-4 py-3 text-sm font-medium"])}">${ssrInterpolate(tab.name)}</button>`);
        });
        _push(`<!--]--></div>`);
        if (activeTab.value === "profile") {
          _push(`<div class="px-6 py-5"><div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2"><div><dt class="text-sm font-medium text-gray-500 dark:text-gray-400">\u7528\u6237ID</dt><dd class="mt-1 text-sm text-gray-900 dark:text-white">${ssrInterpolate(user.value.id)}</dd></div><div><dt class="text-sm font-medium text-gray-500 dark:text-gray-400">\u6CE8\u518C\u65F6\u95F4</dt><dd class="mt-1 text-sm text-gray-900 dark:text-white">${ssrInterpolate(formatDate(user.value.createdAt))}</dd></div><div><dt class="text-sm font-medium text-gray-500 dark:text-gray-400">\u6700\u540E\u767B\u5F55</dt><dd class="mt-1 text-sm text-gray-900 dark:text-white">${ssrInterpolate(user.value.lastLoginAt ? formatDate(user.value.lastLoginAt) : "\u4ECE\u672A\u767B\u5F55")}</dd></div><div><dt class="text-sm font-medium text-gray-500 dark:text-gray-400">\u8D26\u6237\u72B6\u6001</dt><dd class="mt-1 text-sm"><span class="${ssrRenderClass([user.value.isActive ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400", "inline-flex rounded-full px-2 py-0.5 text-xs font-medium"])}">${ssrInterpolate(user.value.isActive ? "\u6D3B\u8DC3" : "\u672A\u6FC0\u6D3B")}</span></dd></div><div><dt class="text-sm font-medium text-gray-500 dark:text-gray-400">\u6700\u540E\u66F4\u65B0</dt><dd class="mt-1 text-sm text-gray-900 dark:text-white">${ssrInterpolate(formatDate(user.value.updatedAt))}</dd></div></div></div>`);
        } else if (activeTab.value === "courses") {
          _push(`<div class="px-6 py-5">`);
          if (userCourses.value.length > 0) {
            _push(`<div><ul class="divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`);
            ssrRenderList(userCourses.value, (course) => {
              _push(`<li class="py-4"><div class="flex items-center"><div class="h-10 w-10 flex-shrink-0 overflow-hidden rounded-md bg-gray-200 dark:bg-gray-700">`);
              if (course.cover) {
                _push(`<img${ssrRenderAttr("src", course.cover)} alt="\u8BFE\u7A0B\u5C01\u9762" class="h-full w-full object-cover">`);
              } else {
                _push(`<span class="i-ph-book-open h-full w-full p-2 text-gray-500 dark:text-gray-400"></span>`);
              }
              _push(`</div><div class="ml-4 flex-1"><div class="text-sm font-medium text-gray-900 dark:text-white">${ssrInterpolate(course.title)}</div><div class="text-xs text-gray-500 dark:text-gray-400"> \u8FDB\u5EA6: ${ssrInterpolate(course.progress)}% <span class="ml-3">\u6700\u540E\u5B66\u4E60: ${ssrInterpolate(formatDate(course.lastStudied))}</span></div></div></div></li>`);
            });
            _push(`<!--]--></ul></div>`);
          } else {
            _push(`<div class="py-4 text-center text-sm text-gray-500 dark:text-gray-400"> \u8BE5\u7528\u6237\u5C1A\u672A\u5B66\u4E60\u4EFB\u4F55\u8BFE\u7A0B </div>`);
          }
          _push(`</div>`);
        } else if (activeTab.value === "actions") {
          _push(`<div class="px-6 py-5"><div class="space-y-4"><div><button class="${ssrRenderClass([user.value.isActive ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50" : "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50", "inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-medium shadow-sm"])}"><span class="${ssrRenderClass([user.value.isActive ? "i-ph-prohibit-bold" : "i-ph-check-circle-bold", "mr-2 h-5 w-5"])}"></span> ${ssrInterpolate(user.value.isActive ? "\u7981\u7528\u6B64\u8D26\u6237" : "\u542F\u7528\u6B64\u8D26\u6237")}</button></div><div><button class="inline-flex w-full items-center justify-center rounded-md bg-yellow-100 px-3 py-2 text-sm font-medium text-yellow-700 shadow-sm hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"><span class="i-ph-key-bold mr-2 h-5 w-5"></span> \u91CD\u7F6E\u5BC6\u7801 </button></div><div><button class="inline-flex w-full items-center justify-center rounded-md bg-red-100 px-3 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"><span class="i-ph-trash-bold mr-2 h-5 w-5"></span> \u5220\u9664\u6B64\u8D26\u6237 </button></div></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div></div>`);
      }
      _push(ssrRenderComponent(_component_MainMessageBox, {
        "show-modal": showConfirmModal.value,
        "onUpdate:showModal": ($event) => showConfirmModal.value = $event,
        title: confirmModalTitle.value,
        content: confirmModalContent.value,
        "confirm-btn-text": "\u786E\u8BA4",
        "confirm-btn-class": confirmModalClass.value,
        onConfirm: handleConfirmAction
      }, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/users/[id]/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-CL9THdD9.mjs.map
