import { defineComponent, ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const userList = ref([]);
    const searchQuery = ref("");
    const roleFilter = ref("");
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalUsers = ref(0);
    const loading = ref(true);
    const errorMessage = ref("");
    computed(() => {
      return userList.value.filter((user) => {
        const matchesSearch = !searchQuery.value || (user.name || "").toLowerCase().includes(searchQuery.value.toLowerCase()) || (user.email || "").toLowerCase().includes(searchQuery.value.toLowerCase());
        const matchesRole = !roleFilter.value || user.role === roleFilter.value;
        return matchesSearch && matchesRole;
      });
    });
    const totalPages = computed(() => {
      return Math.ceil(totalUsers.value / pageSize.value);
    });
    computed(() => {
      const total = totalPages.value;
      const current = currentPage.value;
      const delta = 2;
      if (total <= 7) {
        return Array.from({ length: total }, (_, i) => i + 1);
      }
      const left = current - delta;
      const right = current + delta;
      const range = [];
      const rangeWithDots = [];
      let l;
      for (let i = 1; i <= total; i++) {
        if (i === 1 || i === total || i >= left && i <= right) {
          range.push(i);
        }
      }
      for (let i of range) {
        if (l) {
          if (i - l === 2) {
            rangeWithDots.push(l + 1);
          } else if (i - l !== 1) {
            rangeWithDots.push("...");
          }
        }
        rangeWithDots.push(i);
        l = i;
      }
      return rangeWithDots;
    });
    function getRoleLabel(role) {
      switch (role) {
        case "superadmin":
          return "\u8D85\u7EA7\u7BA1\u7406\u5458";
        case "ADMIN":
          return "\u7BA1\u7406\u5458";
        case "user":
          return "\u666E\u901A\u7528\u6237";
        default:
          return "\u672A\u77E5\u89D2\u8272";
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))} data-v-4ac8eb83>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6" data-v-4ac8eb83><div class="debug-info bg-yellow-100 p-4 mb-4 rounded" data-v-4ac8eb83><h3 class="font-bold mb-2" data-v-4ac8eb83>\u{1F50D} \u6E32\u67D3\u8C03\u8BD5\u4FE1\u606F</h3><p data-v-4ac8eb83>\u7528\u6237\u6570\u7EC4\u957F\u5EA6: ${ssrInterpolate(userList.value.length)}</p><p data-v-4ac8eb83>\u7528\u6237\u6570\u7EC4\u7C7B\u578B: ${ssrInterpolate(typeof userList.value)}</p><pre data-v-4ac8eb83>\u7528\u6237\u6570\u636E\u8BE6\u60C5: ${ssrInterpolate(JSON.stringify(userList.value, null, 2))}</pre></div><div class="flex justify-between items-center mb-6" data-v-4ac8eb83><h1 class="text-2xl font-bold" data-v-4ac8eb83>\u7528\u6237\u7BA1\u7406</h1><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded" data-v-4ac8eb83> \u6DFB\u52A0\u7528\u6237 </button></div><div class="flex justify-between mb-6" data-v-4ac8eb83><div class="flex space-x-2" data-v-4ac8eb83><div class="relative" data-v-4ac8eb83><input${ssrRenderAttr("value", searchQuery.value)} type="text" placeholder="\u641C\u7D22\u7528\u6237\u540D\u6216\u90AE\u7BB1..." class="border rounded px-3 py-2 pr-8" data-v-4ac8eb83>`);
      if (searchQuery.value) {
        _push(`<span class="absolute right-2 top-2 cursor-pointer text-gray-500" data-v-4ac8eb83> \xD7 </span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><select class="border rounded px-3 py-2" data-v-4ac8eb83><option value="" data-v-4ac8eb83${ssrIncludeBooleanAttr(Array.isArray(roleFilter.value) ? ssrLooseContain(roleFilter.value, "") : ssrLooseEqual(roleFilter.value, "")) ? " selected" : ""}>\u6240\u6709\u89D2\u8272</option><option value="admin" data-v-4ac8eb83${ssrIncludeBooleanAttr(Array.isArray(roleFilter.value) ? ssrLooseContain(roleFilter.value, "admin") : ssrLooseEqual(roleFilter.value, "admin")) ? " selected" : ""}>\u7BA1\u7406\u5458</option><option value="user" data-v-4ac8eb83${ssrIncludeBooleanAttr(Array.isArray(roleFilter.value) ? ssrLooseContain(roleFilter.value, "user") : ssrLooseEqual(roleFilter.value, "user")) ? " selected" : ""}>\u666E\u901A\u7528\u6237</option></select></div></div>`);
      if (loading.value) {
        _push(`<div class="text-center py-4" data-v-4ac8eb83>\u52A0\u8F7D\u4E2D...</div>`);
      } else if (errorMessage.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" data-v-4ac8eb83>${ssrInterpolate(errorMessage.value)}</div>`);
      } else {
        _push(`<div data-v-4ac8eb83>`);
        if (userList.value.length === 0) {
          _push(`<div class="text-center py-4" data-v-4ac8eb83> \u6CA1\u6709\u627E\u5230\u7528\u6237 </div>`);
        } else {
          _push(`<div class="user-list grid gap-4" data-v-4ac8eb83><!--[-->`);
          ssrRenderList(userList.value, (user) => {
            _push(`<div class="user-card border rounded p-4 flex justify-between items-center" data-v-4ac8eb83><div data-v-4ac8eb83><h3 class="font-bold" data-v-4ac8eb83>${ssrInterpolate(user.name)}</h3><p class="text-gray-600" data-v-4ac8eb83>${ssrInterpolate(user.email)}</p><div class="flex gap-2 mt-2" data-v-4ac8eb83><span class="${ssrRenderClass([{
              "bg-blue-100 text-blue-800": user.role === "superadmin" || user.role === "ADMIN",
              "bg-green-100 text-green-800": user.role === "user"
            }, "user-role px-2 py-1 rounded text-xs"])}" data-v-4ac8eb83>${ssrInterpolate(getRoleLabel(user.role))}</span><span class="${ssrRenderClass([{
              "bg-green-100 text-green-800": user.isActive,
              "bg-red-100 text-red-800": !user.isActive
            }, "user-status px-2 py-1 rounded text-xs"])}" data-v-4ac8eb83>${ssrInterpolate(user.isActive ? "\u6D3B\u8DC3" : "\u7981\u7528")}</span></div></div><div class="actions flex gap-2" data-v-4ac8eb83><button class="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600" data-v-4ac8eb83> \u7F16\u8F91 </button><button class="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600" data-v-4ac8eb83> \u5220\u9664 </button></div></div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div>`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/users/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-4ac8eb83"]]);

export { index as default };
//# sourceMappingURL=index-BqBrd0Lz.mjs.map
