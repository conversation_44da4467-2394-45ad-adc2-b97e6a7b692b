import { a as useToast, _ as __nuxt_component_0$1 } from './server.mjs';
import { ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrRenderDynamicModel, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = {
  __name: "login",
  __ssrInlineRender: true,
  setup(__props) {
    const email = ref("");
    const password = ref("");
    const errorMessage = ref("");
    const passwordVisible = ref(false);
    const isLoading = ref(false);
    useToast();
    ref(false);
    return (_ctx, _push, _parent, _attrs) => {
      const _component_client_only = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600" }, _attrs))}><div class="w-full max-w-md"><div class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4"><div class="mb-6 text-center"><h1 class="text-3xl font-bold text-gray-800">\u7BA1\u7406\u540E\u53F0</h1><p class="text-gray-600 mt-2">ZeroBase \u7BA1\u7406\u7CFB\u7EDF</p></div>`);
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      if (errorMessage.value) {
        _push(`<div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-center">${ssrInterpolate(errorMessage.value)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<form><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="email"> \u90AE\u7BB1 </label><input${ssrRenderAttr("value", email.value)} type="email" required class="shadow-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="\u8BF7\u8F93\u5165\u7BA1\u7406\u5458\u90AE\u7BB1"></div><div class="mb-6 relative"><label class="block text-gray-700 text-sm font-bold mb-2" for="password"> \u5BC6\u7801 </label><div class="relative"><input${ssrRenderAttr("type", passwordVisible.value ? "text" : "password")}${ssrRenderDynamicModel(passwordVisible.value ? "text" : "password", password.value, null)} required class="shadow-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10" placeholder="\u8BF7\u8F93\u5165\u5BC6\u7801"><button type="button" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none" aria-label="\u5207\u6362\u5BC6\u7801\u53EF\u89C1\u6027">`);
      if (!passwordVisible.value) {
        _push(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>`);
      } else {
        _push(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.781-1.781zm4.261 4.262l1.514 1.514a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path><path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.742L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.064 7 9.542 7 .847 0 1.67-.105 2.454-.303z"></path></svg>`);
      }
      _push(`</button></div></div><div class="flex items-center justify-center"><button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed"${ssrIncludeBooleanAttr(isLoading.value) ? " disabled" : ""}>`);
      if (!isLoading.value) {
        _push(`<span>\u767B\u5F55</span>`);
      } else {
        _push(`<span class="flex items-center justify-center"><svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> \u6B63\u5728\u767B\u5F55... </span>`);
      }
      _push(`</button></div></form></div><div class="text-center text-white"><p>\xA9 2023 ZeroBase \u7BA1\u7406\u7CFB\u7EDF. \u4FDD\u7559\u6240\u6709\u6743\u5229.</p></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/login.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=login-D3d9Z6IS.mjs.map
