import { defineComponent, ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "course-packages",
  __ssrInlineRender: true,
  setup(__props) {
    useToast();
    function formatShareLevel(shareLevel) {
      if (!shareLevel) return "\u79C1\u6709";
      const levelMap = {
        "PRIVATE": "\u79C1\u6709",
        "PUBLIC": "\u516C\u5F00",
        "FOUNDER_ONLY": "\u4EC5\u521B\u59CB\u4EBA"
      };
      return levelMap[shareLevel] || shareLevel;
    }
    const packages = ref([]);
    const loading = ref(true);
    const error = ref(null);
    const searchQuery = ref("");
    const priceFilter = ref("all");
    const shareLevelFilter = ref("all");
    const pagination = ref({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    });
    const showCreatePackageModal = ref(false);
    const creating = ref(false);
    const newPackage = ref({
      title: "",
      description: "",
      cover: "",
      isFree: false,
      order: 1,
      // 添加默认值
      shareLevel: "PRIVATE"
    });
    const showEditPackageModal = ref(false);
    const updating = ref(false);
    const editingPackage = ref({});
    const showDeleteConfirmModal = ref(false);
    const deleting = ref(false);
    const packageToDelete = ref(null);
    const paginationPages = computed(() => {
      const totalPages = pagination.value.totalPages || 1;
      const currentPage = pagination.value.page || 1;
      if (totalPages <= 5) {
        return Array.from({ length: totalPages }, (_, i2) => i2 + 1);
      }
      let pages = [currentPage];
      let i = 1;
      while (pages.length < 5 && currentPage - i > 0) {
        pages.unshift(currentPage - i);
        i++;
      }
      i = 1;
      while (pages.length < 5 && currentPage + i <= totalPages) {
        pages.push(currentPage + i);
        i++;
      }
      while (pages.length < 5) {
        if (pages[0] > 1) {
          pages.unshift(pages[0] - 1);
        } else if (pages[pages.length - 1] < totalPages) {
          pages.push(pages[pages.length - 1] + 1);
        } else {
          break;
        }
      }
      return pages;
    });
    const filteredPackages = computed(() => {
      let result = packages.value;
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(
          (pkg) => {
            var _a;
            return pkg.title.toLowerCase().includes(query) || ((_a = pkg.description) == null ? void 0 : _a.toLowerCase().includes(query));
          }
        );
      }
      if (priceFilter.value !== "all") {
        const isFree = priceFilter.value === "free";
        result = result.filter((pkg) => pkg.isFree === isFree);
      }
      if (shareLevelFilter.value !== "all") {
        result = result.filter((pkg) => pkg.shareLevel === shareLevelFilter.value);
      }
      return result;
    });
    function formatDate(dateString) {
      if (!dateString) return "\u672A\u77E5";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return new Intl.DateTimeFormat("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      }).format(date);
    }
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex min-h-screen bg-gray-100 overflow-x-hidden" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1"><div class="p-8"><div class="w-full max-w-6xl mx-auto"><h1 class="text-2xl font-bold mb-8 text-center">\u8BFE\u7A0B\u5305\u7BA1\u7406</h1>`);
      if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"><p>${ssrInterpolate(error.value)}</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="mb-6 text-right"><button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 ml-auto"><span class="mr-2">+</span> \u521B\u5EFA\u65B0\u8BFE\u7A0B\u5305 </button></div><div class="bg-white p-4 rounded-lg shadow mb-6"><div class="flex flex-wrap gap-4"><div class="flex-1 min-w-[200px]"><input${ssrRenderAttr("value", searchQuery.value)} type="text" placeholder="\u641C\u7D22\u8BFE\u7A0B\u5305..." class="w-full px-4 py-2 border rounded-lg"></div><div class="w-auto"><select class="w-full px-4 py-2 border rounded-lg"><option value="all"${ssrIncludeBooleanAttr(Array.isArray(priceFilter.value) ? ssrLooseContain(priceFilter.value, "all") : ssrLooseEqual(priceFilter.value, "all")) ? " selected" : ""}>\u6240\u6709\u4EF7\u683C</option><option value="free"${ssrIncludeBooleanAttr(Array.isArray(priceFilter.value) ? ssrLooseContain(priceFilter.value, "free") : ssrLooseEqual(priceFilter.value, "free")) ? " selected" : ""}>\u514D\u8D39</option><option value="paid"${ssrIncludeBooleanAttr(Array.isArray(priceFilter.value) ? ssrLooseContain(priceFilter.value, "paid") : ssrLooseEqual(priceFilter.value, "paid")) ? " selected" : ""}>\u4ED8\u8D39</option></select></div><div class="w-auto"><select class="w-full px-4 py-2 border rounded-lg"><option value="all"${ssrIncludeBooleanAttr(Array.isArray(shareLevelFilter.value) ? ssrLooseContain(shareLevelFilter.value, "all") : ssrLooseEqual(shareLevelFilter.value, "all")) ? " selected" : ""}>\u6240\u6709\u5206\u4EAB\u7EA7\u522B</option><option value="PRIVATE"${ssrIncludeBooleanAttr(Array.isArray(shareLevelFilter.value) ? ssrLooseContain(shareLevelFilter.value, "PRIVATE") : ssrLooseEqual(shareLevelFilter.value, "PRIVATE")) ? " selected" : ""}>\u79C1\u6709</option><option value="PUBLIC"${ssrIncludeBooleanAttr(Array.isArray(shareLevelFilter.value) ? ssrLooseContain(shareLevelFilter.value, "PUBLIC") : ssrLooseEqual(shareLevelFilter.value, "PUBLIC")) ? " selected" : ""}>\u516C\u5F00</option><option value="FOUNDER_ONLY"${ssrIncludeBooleanAttr(Array.isArray(shareLevelFilter.value) ? ssrLooseContain(shareLevelFilter.value, "FOUNDER_ONLY") : ssrLooseEqual(shareLevelFilter.value, "FOUNDER_ONLY")) ? " selected" : ""}>\u4EC5\u521B\u59CB\u4EBA</option></select></div></div></div>`);
      if (loading.value) {
        _push(`<div class="w-full flex justify-center py-12"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div></div>`);
      } else if (packages.value && packages.value.length > 0) {
        _push(`<div class="bg-white rounded-lg shadow overflow-hidden"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> ID </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u5C01\u9762 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u6807\u9898 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u8BFE\u7A0B\u6570\u91CF </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u4EF7\u683C </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u5206\u4EAB\u7EA7\u522B </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u521B\u5EFA\u65F6\u95F4 </th><th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> \u64CD\u4F5C </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`);
        ssrRenderList(filteredPackages.value, (pkg) => {
          var _a2;
          _push(`<tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(pkg.id.slice(0, 8))}... </td><td class="px-6 py-4 whitespace-nowrap"><img${ssrRenderAttr("src", pkg.cover || "https://via.placeholder.com/50")} alt="Cover" class="h-10 w-16 object-cover rounded"></td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${ssrInterpolate(pkg.title)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(((_a2 = pkg.courses) == null ? void 0 : _a2.length) || 0)}</td><td class="px-6 py-4 whitespace-nowrap"><button class="${ssrRenderClass([pkg.isFree ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800", "px-3 py-1 rounded-full text-xs font-semibold"])}">${ssrInterpolate(pkg.isFree ? "\u514D\u8D39" : "\u4ED8\u8D39")}</button></td><td class="px-6 py-4 whitespace-nowrap"><span class="${ssrRenderClass([{
            "bg-red-100 text-red-800": pkg.shareLevel === "PRIVATE",
            "bg-green-100 text-green-800": pkg.shareLevel === "PUBLIC",
            "bg-yellow-100 text-yellow-800": pkg.shareLevel === "FOUNDER_ONLY"
          }, "px-3 py-1 rounded-full text-xs font-semibold"])}">${ssrInterpolate(formatShareLevel(pkg.shareLevel))}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${ssrInterpolate(formatDate(pkg.createdAt))}</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><div class="flex space-x-2"><button class="text-indigo-600 hover:text-indigo-900 mr-2"> \u7F16\u8F91 </button><button class="text-red-600 hover:text-red-900"> \u5220\u9664 </button></div></td></tr>`);
        });
        _push(`<!--]--></tbody></table><div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"><div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"><div><p class="text-sm text-gray-700"> \u663E\u793A <span class="font-medium">${ssrInterpolate((pagination.value.page - 1) * pagination.value.pageSize + 1)}</span> \u81F3 <span class="font-medium">${ssrInterpolate(Math.min(pagination.value.page * pagination.value.pageSize, pagination.value.total))}</span> \u6761\uFF0C\u5171 <span class="font-medium">${ssrInterpolate(pagination.value.total)}</span> \u6761 </p></div><div><nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="\u5206\u9875"><button${ssrIncludeBooleanAttr(pagination.value.page <= 1) ? " disabled" : ""} class="${ssrRenderClass([pagination.value.page <= 1 ? "opacity-50 cursor-not-allowed" : "", "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0A\u4E00\u9875</span> \xAB </button><!--[-->`);
        ssrRenderList(paginationPages.value, (page) => {
          _push(`<span class="${ssrRenderClass([page === pagination.value.page ? "bg-blue-50 text-blue-600 z-10 border-blue-500" : "text-gray-500 hover:bg-gray-50", "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium cursor-pointer"])}">${ssrInterpolate(page)}</span>`);
        });
        _push(`<!--]--><button${ssrIncludeBooleanAttr(pagination.value.page >= pagination.value.totalPages) ? " disabled" : ""} class="${ssrRenderClass([pagination.value.page >= pagination.value.totalPages ? "opacity-50 cursor-not-allowed" : "", "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0B\u4E00\u9875</span> \xBB </button></nav></div></div></div></div>`);
      } else {
        _push(`<div class="bg-white p-8 rounded-lg shadow text-center"><p class="text-gray-500">\u6682\u65E0\u8BFE\u7A0B\u5305\u6570\u636E</p></div>`);
      }
      _push(`</div></div></div>`);
      if (showCreatePackageModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u521B\u5EFA\u65B0\u8BFE\u7A0B\u5305</h2><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="title"> \u6807\u9898 </label><input${ssrRenderAttr("value", newPackage.value.title)} id="title" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="description"> \u63CF\u8FF0 </label><textarea id="description" rows="3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">${ssrInterpolate(newPackage.value.description)}</textarea></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="cover"> \u5C01\u9762URL </label><input${ssrRenderAttr("value", newPackage.value.cover)} id="cover" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="\u53EF\u586B\u5199\u7F51\u7EDC\u56FE\u7247URL"><input type="file" accept="image/*" class="mt-2">`);
        if (newPackage.value.cover) {
          _push(`<div class="mt-2"><img${ssrRenderAttr("src", newPackage.value.cover)} alt="Cover Preview" class="h-20 object-cover rounded"></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="shareLevel"> \u5206\u4EAB\u7EA7\u522B </label><select id="shareLevel" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><option value="PRIVATE"${ssrIncludeBooleanAttr(Array.isArray(newPackage.value.shareLevel) ? ssrLooseContain(newPackage.value.shareLevel, "PRIVATE") : ssrLooseEqual(newPackage.value.shareLevel, "PRIVATE")) ? " selected" : ""}>\u79C1\u6709</option><option value="PUBLIC"${ssrIncludeBooleanAttr(Array.isArray(newPackage.value.shareLevel) ? ssrLooseContain(newPackage.value.shareLevel, "PUBLIC") : ssrLooseEqual(newPackage.value.shareLevel, "PUBLIC")) ? " selected" : ""}>\u516C\u5F00</option><option value="FOUNDER_ONLY"${ssrIncludeBooleanAttr(Array.isArray(newPackage.value.shareLevel) ? ssrLooseContain(newPackage.value.shareLevel, "FOUNDER_ONLY") : ssrLooseEqual(newPackage.value.shareLevel, "FOUNDER_ONLY")) ? " selected" : ""}>\u4EC5\u521B\u59CB\u4EBA</option></select></div><div class="mb-4 flex items-center"><input${ssrIncludeBooleanAttr(Array.isArray(newPackage.value.isFree) ? ssrLooseContain(newPackage.value.isFree, null) : newPackage.value.isFree) ? " checked" : ""} id="isFree" type="checkbox" class="mr-2"><label class="text-gray-700 text-sm font-bold" for="isFree"> \u514D\u8D39\u8BFE\u7A0B\u5305 </label></div><div class="flex items-center justify-between"><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(creating.value) ? " disabled" : ""}>${ssrInterpolate(creating.value ? "\u521B\u5EFA\u4E2D..." : "\u521B\u5EFA")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showEditPackageModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u7F16\u8F91\u8BFE\u7A0B\u5305</h2><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-title"> \u6807\u9898 </label><input${ssrRenderAttr("value", editingPackage.value.title)} id="edit-title" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-description"> \u63CF\u8FF0 </label><textarea id="edit-description" rows="3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">${ssrInterpolate(editingPackage.value.description)}</textarea></div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-cover"> \u5C01\u9762URL </label><input${ssrRenderAttr("value", editingPackage.value.cover)} id="edit-cover" type="text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="\u53EF\u586B\u5199\u7F51\u7EDC\u56FE\u7247URL"><input type="file" accept="image/*" class="mt-2">`);
        if (editingPackage.value.cover) {
          _push(`<div class="mt-2"><img${ssrRenderAttr("src", editingPackage.value.cover)} alt="Cover Preview" class="h-20 object-cover rounded"></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2" for="edit-shareLevel"> \u5206\u4EAB\u7EA7\u522B </label><select id="edit-shareLevel" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><option value="PRIVATE"${ssrIncludeBooleanAttr(Array.isArray(editingPackage.value.shareLevel) ? ssrLooseContain(editingPackage.value.shareLevel, "PRIVATE") : ssrLooseEqual(editingPackage.value.shareLevel, "PRIVATE")) ? " selected" : ""}>\u79C1\u6709</option><option value="PUBLIC"${ssrIncludeBooleanAttr(Array.isArray(editingPackage.value.shareLevel) ? ssrLooseContain(editingPackage.value.shareLevel, "PUBLIC") : ssrLooseEqual(editingPackage.value.shareLevel, "PUBLIC")) ? " selected" : ""}>\u516C\u5F00</option><option value="FOUNDER_ONLY"${ssrIncludeBooleanAttr(Array.isArray(editingPackage.value.shareLevel) ? ssrLooseContain(editingPackage.value.shareLevel, "FOUNDER_ONLY") : ssrLooseEqual(editingPackage.value.shareLevel, "FOUNDER_ONLY")) ? " selected" : ""}>\u4EC5\u521B\u59CB\u4EBA</option></select></div><div class="mb-4 flex items-center"><input${ssrIncludeBooleanAttr(Array.isArray(editingPackage.value.isFree) ? ssrLooseContain(editingPackage.value.isFree, null) : editingPackage.value.isFree) ? " checked" : ""} id="edit-isFree" type="checkbox" class="mr-2"><label class="text-gray-700 text-sm font-bold" for="edit-isFree"> \u514D\u8D39\u8BFE\u7A0B\u5305 </label></div><div class="flex items-center justify-between"><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(updating.value) ? " disabled" : ""}>${ssrInterpolate(updating.value ? "\u66F4\u65B0\u4E2D..." : "\u66F4\u65B0")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showDeleteConfirmModal.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><h2 class="text-xl font-bold mb-4">\u786E\u8BA4\u5220\u9664</h2><p class="mb-6">\u786E\u5B9A\u8981\u5220\u9664\u8BFE\u7A0B\u5305 &quot;${ssrInterpolate((_a = packageToDelete.value) == null ? void 0 : _a.title)}&quot; \u5417\uFF1F\u6B64\u64CD\u4F5C\u65E0\u6CD5\u64A4\u9500\u3002</p><div class="flex items-center justify-between"><button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"${ssrIncludeBooleanAttr(deleting.value) ? " disabled" : ""}>${ssrInterpolate(deleting.value ? "\u5220\u9664\u4E2D..." : "\u786E\u8BA4\u5220\u9664")}</button><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"> \u53D6\u6D88 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/course-packages.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=course-packages-t0GPmscY.mjs.map
