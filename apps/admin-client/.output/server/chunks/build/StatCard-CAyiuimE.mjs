import { defineComponent, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderClass, ssrInterpolate } from 'vue/server-renderer';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "StatCard",
  __ssrInlineRender: true,
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: Number,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: "blue",
      validator: (value) => ["blue", "green", "purple", "red", "yellow", "orange", "indigo", "pink"].includes(value)
    }
  },
  setup(__props) {
    const props = __props;
    const iconColorClasses = {
      blue: "text-blue-500",
      green: "text-green-500",
      purple: "text-purple-500",
      red: "text-red-500",
      yellow: "text-yellow-500",
      orange: "text-orange-500",
      indigo: "text-indigo-500",
      pink: "text-pink-500"
    };
    const formattedValue = computed(() => {
      const value = props.value || 0;
      if (value >= 1e6) {
        return `${(value / 1e6).toFixed(1)}M`;
      }
      if (value >= 1e3) {
        return `${(value / 1e3).toFixed(1)}K`;
      }
      return value.toString();
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-white rounded-lg shadow-md p-6 flex items-center border border-gray-200" }, _attrs))}><div class="flex-shrink-0 mr-4"><div class="${ssrRenderClass([`${iconColorClasses[__props.color]}`, "text-4xl"])}">${ssrInterpolate(__props.icon)}</div></div><div><h3 class="text-lg font-semibold text-gray-800 mb-2">${ssrInterpolate(__props.title)}</h3><p class="text-3xl font-bold text-gray-900">${ssrInterpolate(formattedValue.value)}</p></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/dashboard/StatCard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=StatCard-CAyiuimE.mjs.map
