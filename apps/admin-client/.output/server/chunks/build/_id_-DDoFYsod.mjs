import { ref, reactive, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { u as useContentStore } from './content-urLiV7FI.mjs';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { Q as QuillEditor } from './QuillEditor-DzFX0Zx0.mjs';
import './api-BjJAe0gw.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const route = useRoute();
    useContentStore();
    const toast = useToast();
    route.params.id;
    const loading = ref(true);
    const saving = ref(false);
    const error = ref("");
    const saveError = ref("");
    const content = reactive({
      id: "",
      title: "",
      type: "",
      body: "",
      categories: [],
      annotations: []
    });
    const handleAddPronunciation = ({ text, range, quill }) => {
      const word = text.trim();
      if (!word) {
        toast.warning("\u8BF7\u9009\u62E9\u8981\u6DFB\u52A0\u97F3\u6807\u7684\u5355\u8BCD");
        return;
      }
      const existingAnnotation = content.annotations.find(
        (a) => a.word.toLowerCase() === word.toLowerCase()
      );
      if (existingAnnotation) {
        const pronunciation = existingAnnotation.pronunciation;
        quill.deleteText(range.index, range.length);
        quill.insertText(range.index, word, {
          "pronunciation": pronunciation
        });
        toast.success(`\u5DF2\u4E3A "${word}" \u6DFB\u52A0\u97F3\u6807: ${pronunciation}`);
      } else {
        const newAnnotation = { word, pronunciation: "" };
        content.annotations.push(newAnnotation);
        toast.info(`\u5DF2\u6DFB\u52A0\u5355\u8BCD "${word}" \u5230\u6CE8\u89E3\u5217\u8868\uFF0C\u8BF7\u5728\u4E0B\u65B9\u8865\u5145\u97F3\u6807\u4FE1\u606F`);
        setTimeout(() => {
          (void 0).querySelector(".col-span-5:last-child input").focus();
        }, 100);
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6"><div class="flex justify-between items-center mb-6"><h1 class="text-2xl font-bold">\u7F16\u8F91\u5185\u5BB9</h1><div class="flex space-x-3"><button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"> \u9884\u89C8 </button><button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"> \u8FD4\u56DE\u5217\u8868 </button></div></div>`);
      if (loading.value) {
        _push(`<div class="text-center py-10"><p>\u52A0\u8F7D\u4E2D...</p></div>`);
      } else if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"><p class="font-bold">\u52A0\u8F7D\u5931\u8D25:</p><p>${ssrInterpolate(error.value)}</p><button class="mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"> \u91CD\u8BD5 </button></div>`);
      } else {
        _push(`<div class="bg-white shadow-md rounded p-6"><form><div class="mb-4"><label for="title" class="block text-sm font-medium text-gray-700 mb-1">\u6807\u9898</label><input id="title"${ssrRenderAttr("value", content.title)} type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="\u8F93\u5165\u5185\u5BB9\u6807\u9898" required></div><div class="mb-4"><label for="type" class="block text-sm font-medium text-gray-700 mb-1">\u5185\u5BB9\u7C7B\u578B</label><select id="type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required><option value=""${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "") : ssrLooseEqual(content.type, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u7C7B\u578B</option><option value="ARTICLE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "ARTICLE") : ssrLooseEqual(content.type, "ARTICLE")) ? " selected" : ""}>\u6587\u7AE0</option><option value="LESSON"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "LESSON") : ssrLooseEqual(content.type, "LESSON")) ? " selected" : ""}>\u8BFE\u7A0B</option><option value="EXERCISE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "EXERCISE") : ssrLooseEqual(content.type, "EXERCISE")) ? " selected" : ""}>\u7EC3\u4E60</option><option value="NOTE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "NOTE") : ssrLooseEqual(content.type, "NOTE")) ? " selected" : ""}>\u7B14\u8BB0</option></select></div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u680F\u76EE</label><div class="flex flex-wrap gap-2"><!--[-->`);
        ssrRenderList(content.categories, (category, index) => {
          _push(`<div class="flex items-center"><input${ssrRenderAttr("value", content.categories[index])} type="text" class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 mr-2" placeholder="\u680F\u76EE\u540D\u79F0"><button type="button" class="text-red-500 hover:text-red-700"> \u5220\u9664 </button></div>`);
        });
        _push(`<!--]--><button type="button" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"> \u6DFB\u52A0\u680F\u76EE </button></div></div><div class="mb-6"><label for="body" class="block text-sm font-medium text-gray-700 mb-1">\u5185\u5BB9\u6B63\u6587</label>`);
        _push(ssrRenderComponent(QuillEditor, {
          content: content.body,
          "onUpdate:content": ($event) => content.body = $event,
          placeholder: "\u8F93\u5165\u5185\u5BB9\u6B63\u6587",
          class: "w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",
          onAddPronunciation: handleAddPronunciation
        }, null, _parent));
        _push(`</div><div class="mb-6"><div class="flex justify-between items-center mb-2"><label class="block text-sm font-medium text-gray-700">\u5355\u8BCD\u6CE8\u89E3</label><button type="button" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"> \u6DFB\u52A0\u6CE8\u89E3 </button></div><div class="mb-2"><p class="text-sm text-gray-500">\u6DFB\u52A0\u5355\u8BCD\u7684\u53D1\u97F3\u6CE8\u89E3\uFF0C\u5728\u9884\u89C8\u65F6\u70B9\u51FB\u5355\u8BCD\u53EF\u4EE5\u542C\u5230\u53D1\u97F3\u3002\u97F3\u6807\u683C\u5F0F\u793A\u4F8B\uFF1A/h\u0259\u02C8lo\u028A/\u3001/\u0261\u028Ad/\u3001/no\u028A/</p></div>`);
        if (content.annotations && content.annotations.length > 0) {
          _push(`<div class="border rounded-md overflow-hidden"><div class="grid grid-cols-12 bg-gray-50 border-b p-2"><div class="col-span-5 font-medium text-sm text-gray-700">\u5355\u8BCD</div><div class="col-span-5 font-medium text-sm text-gray-700">\u97F3\u6807</div><div class="col-span-2 font-medium text-sm text-gray-700">\u64CD\u4F5C</div></div><!--[-->`);
          ssrRenderList(content.annotations, (annotation, index) => {
            _push(`<div class="grid grid-cols-12 border-b p-2 last:border-b-0"><div class="col-span-5"><input${ssrRenderAttr("value", annotation.word)} type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="\u5355\u8BCD" required></div><div class="col-span-5"><input${ssrRenderAttr("value", annotation.pronunciation)} type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="\u97F3\u6807 (\u4F8B\u5982: /h\u0259\u02C8lo\u028A/)" required></div><div class="col-span-2 flex items-center"><button type="button" class="text-red-500 hover:text-red-700 ml-2"> \u5220\u9664 </button></div></div>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<div class="text-gray-500 text-sm"> \u6682\u65E0\u5355\u8BCD\u6CE8\u89E3\uFF0C\u70B9\u51FB&quot;\u6DFB\u52A0\u6CE8\u89E3&quot;\u6309\u94AE\u6DFB\u52A0 </div>`);
        }
        _push(`</div><div class="flex justify-end"><button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"${ssrIncludeBooleanAttr(saving.value) ? " disabled" : ""}>`);
        if (saving.value) {
          _push(`<span>\u4FDD\u5B58\u4E2D...</span>`);
        } else {
          _push(`<span>\u4FDD\u5B58\u5185\u5BB9</span>`);
        }
        _push(`</button></div></form></div>`);
      }
      if (saveError.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">${ssrInterpolate(saveError.value)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/content/edit/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-DDoFYsod.mjs.map
