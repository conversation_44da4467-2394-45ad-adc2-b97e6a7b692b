import { h as defineStore } from './server.mjs';
import { a as api } from './api-BjJAe0gw.mjs';

const useContentStore = defineStore("content", {
  state: () => ({
    contents: [],
    currentContent: null,
    loading: false,
    error: null
  }),
  actions: {
    async fetchContents(params) {
      this.loading = true;
      this.error = null;
      try {
        const queryParams = new URLSearchParams();
        if (params == null ? void 0 : params.page) {
          queryParams.append("page", params.page.toString());
        }
        if (params == null ? void 0 : params.pageSize) {
          queryParams.append("pageSize", params.pageSize.toString());
        }
        if (params == null ? void 0 : params.category) {
          queryParams.append("category", params.category);
        }
        if (params == null ? void 0 : params.type) {
          queryParams.append("type", params.type);
        }
        const queryString = queryParams.toString();
        const url = `/api/admin/content${queryString ? `?${queryString}` : ""}`;
        const response = await api.get(url);
        if (response && response.contents && response.pagination) {
          this.contents = response.contents;
          return {
            contents: response.contents,
            pagination: response.pagination
          };
        } else {
          this.contents = response;
          return {
            contents: response,
            pagination: null
          };
        }
      } catch (error) {
        this.error = error instanceof Error ? error.message : "\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u5931\u8D25";
        console.error("\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u5931\u8D25", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async createContent(content) {
      this.loading = true;
      this.error = null;
      try {
        const createdContent = await api.post("/api/admin/content", content);
        this.contents.push(createdContent);
        this.currentContent = createdContent;
        return createdContent;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "\u521B\u5EFA\u5185\u5BB9\u5931\u8D25";
        console.error("\u521B\u5EFA\u5185\u5BB9\u5931\u8D25", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async deleteContent(contentId) {
      this.loading = true;
      this.error = null;
      try {
        await api.delete(`/api/admin/content/${contentId}`);
        this.contents = this.contents.filter((c) => c.id !== contentId);
      } catch (error) {
        this.error = error instanceof Error ? error.message : "\u5220\u9664\u5185\u5BB9\u5931\u8D25";
        console.error("\u5220\u9664\u5185\u5BB9\u5931\u8D25", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async fetchContentByCategory(category, type) {
      this.loading = true;
      this.error = null;
      try {
        const url = `/api/admin/content/category?category=${encodeURIComponent(category)}&type=${encodeURIComponent(type)}`;
        this.contents = await api.get(url);
      } catch (error) {
        this.error = error instanceof Error ? error.message : "\u83B7\u53D6\u5185\u5BB9\u5931\u8D25";
        console.error("\u83B7\u53D6\u5185\u5BB9\u5931\u8D25", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async updateContent(contentId, content) {
      var _a, _b, _c;
      this.loading = true;
      this.error = null;
      try {
        console.log(`\u51C6\u5907\u66F4\u65B0\u5185\u5BB9 ${contentId}`, {
          type: content.type,
          title: ((_a = content.title) == null ? void 0 : _a.substring(0, 20)) + "...",
          categoriesCount: ((_b = content.categories) == null ? void 0 : _b.length) || 0,
          bodyPreview: ((_c = content.body) == null ? void 0 : _c.substring(0, 30)) + "..."
        });
        const endpoint = `/api/admin/content/${contentId}`;
        console.log(`\u53D1\u9001PUT\u8BF7\u6C42\u5230: ${endpoint}`);
        const updatedContent = await api.put(endpoint, content);
        const index = this.contents.findIndex((c) => c.id === contentId);
        if (index !== -1) {
          this.contents[index] = updatedContent;
        }
        this.currentContent = updatedContent;
        console.log("\u5185\u5BB9\u66F4\u65B0\u6210\u529F:", updatedContent.id);
        return updatedContent;
      } catch (error) {
        console.error("\u66F4\u65B0\u5185\u5BB9\u5931\u8D25:", error);
        let errorMessage = "\u66F4\u65B0\u5185\u5BB9\u5931\u8D25";
        if (error instanceof Error) {
          errorMessage = error.message;
          console.error("\u9519\u8BEF\u8BE6\u60C5:", {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
        } else {
          console.error("\u672A\u77E5\u9519\u8BEF\u7C7B\u578B:", error);
        }
        this.error = errorMessage;
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
});

export { useContentStore as u };
//# sourceMappingURL=content-urLiV7FI.mjs.map
