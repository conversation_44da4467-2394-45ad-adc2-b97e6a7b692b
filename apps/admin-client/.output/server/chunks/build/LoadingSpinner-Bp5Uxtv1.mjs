import { defineComponent, watch, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderStyle } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "LoadingSpinner",
  __ssrInlineRender: true,
  props: {
    show: { type: Boolean, default: false },
    title: { default: "\u7A0B\u5E8F\u5728\u52A0\u8F7D\u4E2D" },
    message: { default: "\u8BF7\u7A0D\u5019" },
    fullscreen: { type: Boolean, default: true },
    showProgress: { type: Boolean, default: false },
    progress: { default: 0 }
  },
  setup(__props) {
    const props = __props;
    watch(() => props.show, (newValue, oldValue) => {
      if (newValue) {
        console.log(`\u2705 LoadingSpinner \u663E\u793A`);
      } else {
        console.log(`\u274C LoadingSpinner \u9690\u85CF`);
      }
    }, { immediate: true });
    watch(() => props.showProgress, (newValue) => {
    }, { immediate: true });
    return (_ctx, _push, _parent, _attrs) => {
      if (_ctx.show) {
        _push(`<div${ssrRenderAttrs(mergeProps({
          class: ["loading-overlay", { "loading-overlay--fullscreen": _ctx.fullscreen }]
        }, _attrs))} data-v-9ef416d4><div class="loading-container" data-v-9ef416d4><div class="loading-spinner" data-v-9ef416d4><div class="spinner-ring" data-v-9ef416d4><div data-v-9ef416d4></div><div data-v-9ef416d4></div><div data-v-9ef416d4></div><div data-v-9ef416d4></div></div></div><div class="loading-text" data-v-9ef416d4><h3 class="loading-title" data-v-9ef416d4>${ssrInterpolate(_ctx.title)}</h3><p class="loading-message" data-v-9ef416d4>${ssrInterpolate(_ctx.message)}</p><div class="loading-dots" data-v-9ef416d4><span class="dot dot-1" data-v-9ef416d4>.</span><span class="dot dot-2" data-v-9ef416d4>.</span><span class="dot dot-3" data-v-9ef416d4>.</span><span class="dot dot-4" data-v-9ef416d4>.</span><span class="dot dot-5" data-v-9ef416d4>.</span></div></div>`);
        if (_ctx.showProgress) {
          _push(`<div class="loading-progress" data-v-9ef416d4><div class="progress-bar" data-v-9ef416d4><div class="progress-fill" style="${ssrRenderStyle({ width: `${_ctx.progress}%` })}" data-v-9ef416d4></div></div><div class="progress-text" data-v-9ef416d4>${ssrInterpolate(Math.round(_ctx.progress))}%</div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/LoadingSpinner.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const LoadingSpinner = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-9ef416d4"]]);

export { LoadingSpinner as L };
//# sourceMappingURL=LoadingSpinner-Bp5Uxtv1.mjs.map
