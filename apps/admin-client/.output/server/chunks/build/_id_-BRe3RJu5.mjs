import { computed, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { EditorContent } from '@tiptap/vue-3';
import { Mark, mergeAttributes } from '@tiptap/core';

Mark.create({
  name: "pronunciation",
  addOptions() {
    return {
      HTMLAttributes: {}
    };
  },
  addAttributes() {
    return {
      pronunciation: {
        default: null,
        parseHTML: (element) => element.getAttribute("data-pronunciation"),
        renderHTML: (attributes) => {
          if (!attributes.pronunciation) {
            return {};
          }
          return {
            "data-pronunciation": attributes.pronunciation,
            title: attributes.pronunciation
          };
        }
      }
    };
  },
  parseHTML() {
    return [
      {
        tag: "span[data-pronunciation]"
      }
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ["span", mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
      class: "pronunciation-mark",
      style: "background-color: #fef3c7; padding: 1px 2px; border-radius: 2px; cursor: help;"
    }), 0];
  },
  addCommands() {
    return {
      setPronunciation: (attributes) => ({ commands }) => {
        return commands.setMark(this.name, attributes);
      },
      togglePronunciation: (attributes) => ({ commands }) => {
        return commands.toggleMark(this.name, attributes);
      },
      unsetPronunciation: () => ({ commands }) => {
        return commands.unsetMark(this.name);
      }
    };
  }
});
const _sfc_main = {
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const route = useRoute();
    const contentId = computed(() => route.params.id);
    const isNewContent = computed(() => contentId.value === "new");
    const content = ref({
      title: "",
      type: "article",
      body: "",
      categories: [],
      annotations: []
    });
    const editor = ref(null);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-6" }, _attrs))}><h1 class="text-2xl font-bold mb-6">${ssrInterpolate(isNewContent.value ? "\u521B\u5EFA\u5185\u5BB9" : "\u7F16\u8F91\u5185\u5BB9")}</h1><div class="bg-white shadow rounded-lg p-6"><form><div class="mb-4"><label class="block text-gray-700 mb-2">\u6807\u9898</label><input${ssrRenderAttr("value", content.value.title)} class="w-full border rounded px-3 py-2" required></div><div class="mb-4"><label class="block text-gray-700 mb-2">\u7C7B\u578B</label><select class="w-full border rounded px-3 py-2" required><option value="article"${ssrIncludeBooleanAttr(Array.isArray(content.value.type) ? ssrLooseContain(content.value.type, "article") : ssrLooseEqual(content.value.type, "article")) ? " selected" : ""}>\u6587\u7AE0</option><option value="lesson"${ssrIncludeBooleanAttr(Array.isArray(content.value.type) ? ssrLooseContain(content.value.type, "lesson") : ssrLooseEqual(content.value.type, "lesson")) ? " selected" : ""}>\u8BFE\u7A0B</option><option value="exercise"${ssrIncludeBooleanAttr(Array.isArray(content.value.type) ? ssrLooseContain(content.value.type, "exercise") : ssrLooseEqual(content.value.type, "exercise")) ? " selected" : ""}>\u7EC3\u4E60</option><option value="note"${ssrIncludeBooleanAttr(Array.isArray(content.value.type) ? ssrLooseContain(content.value.type, "note") : ssrLooseEqual(content.value.type, "note")) ? " selected" : ""}>\u7B14\u8BB0</option></select></div><div class="mb-4"><label class="block text-gray-700 mb-2">\u680F\u76EE\u5206\u7C7B</label><select multiple class="w-full border rounded px-3 py-2" required><option value="reading"${ssrIncludeBooleanAttr(Array.isArray(content.value.categories) ? ssrLooseContain(content.value.categories, "reading") : ssrLooseEqual(content.value.categories, "reading")) ? " selected" : ""}>\u9605\u8BFB</option><option value="listening"${ssrIncludeBooleanAttr(Array.isArray(content.value.categories) ? ssrLooseContain(content.value.categories, "listening") : ssrLooseEqual(content.value.categories, "listening")) ? " selected" : ""}>\u542C\u529B</option><option value="speaking"${ssrIncludeBooleanAttr(Array.isArray(content.value.categories) ? ssrLooseContain(content.value.categories, "speaking") : ssrLooseEqual(content.value.categories, "speaking")) ? " selected" : ""}>\u53E3\u8BED</option><option value="writing"${ssrIncludeBooleanAttr(Array.isArray(content.value.categories) ? ssrLooseContain(content.value.categories, "writing") : ssrLooseEqual(content.value.categories, "writing")) ? " selected" : ""}>\u5199\u4F5C</option></select><small class="text-gray-500">\u6309\u4F4FCtrl\u952E\u53EF\u591A\u9009</small></div><div class="mb-6"><label class="block text-gray-700 mb-2">\u5185\u5BB9</label><div class="border rounded">`);
      _push(ssrRenderComponent(unref(EditorContent), { editor: editor.value }, null, _parent));
      _push(`</div></div><div class="flex justify-end"><button type="button" class="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"> \u53D6\u6D88 </button><button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded"> \u4FDD\u5B58 </button></div></form></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/content/edit/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-BRe3RJu5.mjs.map
