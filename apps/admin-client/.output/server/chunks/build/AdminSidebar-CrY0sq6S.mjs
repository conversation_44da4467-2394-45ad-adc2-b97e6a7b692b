import { _ as __nuxt_component_0 } from './nuxt-link-0Ezu_WKY.mjs';
import { g as useAdminState, _ as __nuxt_component_0$1 } from './server.mjs';
import { defineComponent, computed, mergeProps, withCtx, createBlock, createTextVNode, openBlock, createVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AdminSidebar",
  __ssrInlineRender: true,
  setup(__props) {
    const { adminUser } = useAdminState();
    computed(() => {
      var _a;
      if (!adminUser.value) return false;
      return ((_a = adminUser.value.role) == null ? void 0 : _a.toUpperCase()) === "SUPERADMIN";
    });
    const hasContentManagePermission = computed(() => {
      var _a;
      if (!adminUser.value) return false;
      const userRole = (_a = adminUser.value.role) == null ? void 0 : _a.toUpperCase();
      const allowedRoles = ["SUPERADMIN", "ADMIN", "EDITOR"];
      return allowedRoles.includes(userRole);
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_nuxt_link = __nuxt_component_0;
      const _component_client_only = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed left-0 top-0 h-full w-64 bg-gray-800 text-white shadow-lg" }, _attrs))} data-v-a68a38ec><div class="p-6 border-b border-gray-700" data-v-a68a38ec><h2 class="text-2xl font-bold" data-v-a68a38ec>\u7BA1\u7406\u540E\u53F0</h2></div><nav class="mt-6" data-v-a68a38ec><ul data-v-a68a38ec><li data-v-a68a38ec>`);
      _push(ssrRenderComponent(_component_nuxt_link, {
        to: "/admin/dashboard",
        class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" data-v-a68a38ec${_scopeId}></path></svg> \u4EEA\u8868\u76D8 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                class: "h-5 w-5 mr-3",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                })
              ])),
              createTextVNode(" \u4EEA\u8868\u76D8 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li>`);
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      _push(`<li data-v-a68a38ec>`);
      _push(ssrRenderComponent(_component_nuxt_link, {
        to: "/admin/course-packages",
        class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" data-v-a68a38ec${_scopeId}></path></svg> \u8BFE\u7A0B\u5305\u7BA1\u7406 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                class: "h-5 w-5 mr-3",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                })
              ])),
              createTextVNode(" \u8BFE\u7A0B\u5305\u7BA1\u7406 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li data-v-a68a38ec>`);
      _push(ssrRenderComponent(_component_nuxt_link, {
        to: "/admin/courses",
        class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" data-v-a68a38ec${_scopeId}></path></svg> \u8BFE\u7A0B\u7BA1\u7406 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                class: "h-5 w-5 mr-3",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                })
              ])),
              createTextVNode(" \u8BFE\u7A0B\u7BA1\u7406 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li>`);
      if (hasContentManagePermission.value) {
        _push(`<li data-v-a68a38ec>`);
        _push(ssrRenderComponent(_component_nuxt_link, {
          to: "/admin/content",
          class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-a68a38ec${_scopeId}></path></svg> \u5185\u5BB9\u7BA1\u7406 `);
            } else {
              return [
                (openBlock(), createBlock("svg", {
                  xmlns: "http://www.w3.org/2000/svg",
                  class: "h-5 w-5 mr-3",
                  fill: "none",
                  viewBox: "0 0 24 24",
                  stroke: "currentColor"
                }, [
                  createVNode("path", {
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round",
                    "stroke-width": "2",
                    d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  })
                ])),
                createTextVNode(" \u5185\u5BB9\u7BA1\u7406 ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</li>`);
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      _push(`<li data-v-a68a38ec>`);
      _push(ssrRenderComponent(_component_nuxt_link, {
        to: "/admin/messages",
        class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" data-v-a68a38ec${_scopeId}></path></svg> \u6D88\u606F\u7BA1\u7406 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                class: "h-5 w-5 mr-3",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                })
              ])),
              createTextVNode(" \u6D88\u606F\u7BA1\u7406 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li data-v-a68a38ec>`);
      _push(ssrRenderComponent(_component_nuxt_link, {
        to: "/admin/logs",
        class: "block py-3 px-6 hover:bg-gray-700 transition duration-200 flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-a68a38ec${_scopeId}></path></svg> \u7BA1\u7406\u65E5\u5FD7 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                class: "h-5 w-5 mr-3",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                })
              ])),
              createTextVNode(" \u7BA1\u7406\u65E5\u5FD7 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li class="mt-auto border-t border-gray-700" data-v-a68a38ec><button class="w-full text-left block py-3 px-6 hover:bg-red-700 transition duration-200 flex items-center text-red-400 hover:text-white" data-v-a68a38ec><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-a68a38ec><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" data-v-a68a38ec></path></svg> \u9000\u51FA\u767B\u5F55 </button></li></ul></nav></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/AdminSidebar.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const AdminSidebar = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-a68a38ec"]]);

export { AdminSidebar as A };
//# sourceMappingURL=AdminSidebar-CrY0sq6S.mjs.map
