import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs } from 'vue/server-renderer';
import { useRouter } from 'vue-router';

const _sfc_main = {
  __name: "unauthorized",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex items-center justify-center min-h-screen" }, _attrs))}><div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full"><div class="text-center"><h1 class="text-red-600 text-4xl font-bold mb-4">\u672A\u6388\u6743\u8BBF\u95EE</h1><div class="text-red-500 text-6xl mb-6"><i class="fas fa-exclamation-circle"></i></div><p class="text-gray-700 mb-6"> \u60A8\u6CA1\u6709\u6743\u9650\u8BBF\u95EE\u8BF7\u6C42\u7684\u9875\u9762\u3002\u8BF7\u786E\u4FDD\u60A8\u5DF2\u767B\u5F55\u5E76\u5177\u6709\u9002\u5F53\u7684\u6743\u9650\u3002 </p><div class="flex flex-col space-y-3"><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"> \u8FD4\u56DE\u767B\u5F55\u9875 </button><button class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition-colors"> \u8FD4\u56DE\u9996\u9875 </button></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/unauthorized.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=unauthorized-X_r8m4QQ.mjs.map
