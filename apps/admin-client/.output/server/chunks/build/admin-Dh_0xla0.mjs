import { _ as __nuxt_component_0 } from './nuxt-link-0Ezu_WKY.mjs';
import { watch, mergeProps, withCtx, createTextVNode, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';
import { b as useGlobalLoading } from './server.mjs';
import { L as LoadingSpinner } from './LoadingSpinner-Bp5Uxtv1.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = {
  __name: "admin",
  __ssrInlineRender: true,
  setup(__props) {
    const loading = useGlobalLoading();
    const { isLoading, loadingTitle, loadingMessage, showProgress, progress } = loading;
    watch(isLoading, (newValue, oldValue) => {
      console.log(`\u{1F3D7}\uFE0F admin.vue isLoading\u53D8\u5316: ${oldValue} -> ${newValue}`);
      console.log(`\u{1F3D7}\uFE0F admin.vue \u52A0\u8F7D\u72B6\u6001:`, {
        isLoading: isLoading.value,
        title: loadingTitle.value,
        message: loadingMessage.value
      });
    }, { immediate: true });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_nuxt_link = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex h-screen" }, _attrs))} data-v-6d2cbca8><div class="w-64 bg-gray-800 text-white" data-v-6d2cbca8><div class="p-6" data-v-6d2cbca8><h1 class="text-2xl font-bold" data-v-6d2cbca8>\u7BA1\u7406\u540E\u53F0</h1></div><nav class="mt-6" data-v-6d2cbca8><ul data-v-6d2cbca8><li class="px-6 py-3 hover:bg-gray-700" data-v-6d2cbca8>`);
      _push(ssrRenderComponent(_component_nuxt_link, { to: "/admin/dashboard" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u4EEA\u8868\u76D8`);
          } else {
            return [
              createTextVNode("\u4EEA\u8868\u76D8")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li class="px-6 py-3 hover:bg-gray-700" data-v-6d2cbca8>`);
      _push(ssrRenderComponent(_component_nuxt_link, { to: "/admin/users" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u7528\u6237\u7BA1\u7406`);
          } else {
            return [
              createTextVNode("\u7528\u6237\u7BA1\u7406")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li class="px-6 py-3 hover:bg-gray-700" data-v-6d2cbca8>`);
      _push(ssrRenderComponent(_component_nuxt_link, { to: "/admin/courses" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u8BFE\u7A0B\u7BA1\u7406`);
          } else {
            return [
              createTextVNode("\u8BFE\u7A0B\u7BA1\u7406")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li class="px-6 py-3 hover:bg-gray-700" data-v-6d2cbca8>`);
      _push(ssrRenderComponent(_component_nuxt_link, { to: "/admin/audio" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u97F3\u9891\u7BA1\u7406`);
          } else {
            return [
              createTextVNode("\u97F3\u9891\u7BA1\u7406")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li class="px-6 py-3 hover:bg-gray-700" data-v-6d2cbca8><a href="#" data-v-6d2cbca8>\u9000\u51FA\u767B\u5F55</a></li></ul></nav></div><div class="flex-1 overflow-y-auto bg-gray-100" data-v-6d2cbca8>`);
      ssrRenderSlot(_ctx.$slots, "default", {}, null, _push, _parent);
      _push(`</div>`);
      _push(ssrRenderComponent(LoadingSpinner, {
        show: unref(isLoading),
        title: unref(loadingTitle),
        message: unref(loadingMessage),
        "show-progress": unref(showProgress),
        progress: unref(progress)
      }, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("layouts/admin.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const admin = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-6d2cbca8"]]);

export { admin as default };
//# sourceMappingURL=admin-Dh_0xla0.mjs.map
