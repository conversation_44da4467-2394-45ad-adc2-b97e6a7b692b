import { ref, reactive, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRender<PERSON>ist, ssrInterpolate } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { u as useContentStore } from './content-urLiV7FI.mjs';
import { a as useToast } from './server.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { Q as QuillEditor } from './QuillEditor-DzFX0Zx0.mjs';
import './api-BjJAe0gw.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "new",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    useContentStore();
    useToast();
    const loading = ref(false);
    const error = ref("");
    const content = reactive({
      title: "",
      type: "",
      body: "",
      categories: [],
      annotations: []
    });
    const showPronunciationDialog = ref(false);
    const selectedText = ref("");
    const pronunciationInput = ref("");
    const handleEditorChange = (event) => {
      event.quill;
      content.body = event.html;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6"><div class="flex justify-between items-center mb-6"><h1 class="text-2xl font-bold">\u521B\u5EFA\u5185\u5BB9</h1><button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"> \u8FD4\u56DE\u5217\u8868 </button></div><div class="bg-white shadow-md rounded p-6"><form><div class="mb-4"><label for="title" class="block text-sm font-medium text-gray-700 mb-1">\u6807\u9898</label><input id="title"${ssrRenderAttr("value", content.title)} type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="\u8F93\u5165\u5185\u5BB9\u6807\u9898" required></div><div class="mb-4"><label for="type" class="block text-sm font-medium text-gray-700 mb-1">\u5185\u5BB9\u7C7B\u578B</label><select id="type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required><option value=""${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "") : ssrLooseEqual(content.type, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u7C7B\u578B</option><option value="ARTICLE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "ARTICLE") : ssrLooseEqual(content.type, "ARTICLE")) ? " selected" : ""}>\u6587\u7AE0</option><option value="LESSON"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "LESSON") : ssrLooseEqual(content.type, "LESSON")) ? " selected" : ""}>\u8BFE\u7A0B</option><option value="EXERCISE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "EXERCISE") : ssrLooseEqual(content.type, "EXERCISE")) ? " selected" : ""}>\u7EC3\u4E60</option><option value="NOTE"${ssrIncludeBooleanAttr(Array.isArray(content.type) ? ssrLooseContain(content.type, "NOTE") : ssrLooseEqual(content.type, "NOTE")) ? " selected" : ""}>\u7B14\u8BB0</option></select></div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-1">\u680F\u76EE</label><div class="flex flex-wrap gap-2"><!--[-->`);
      ssrRenderList(content.categories, (category, index) => {
        _push(`<div class="flex items-center"><input${ssrRenderAttr("value", content.categories[index])} type="text" class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 mr-2" placeholder="\u680F\u76EE\u540D\u79F0"><button type="button" class="text-red-500 hover:text-red-700"> \u5220\u9664 </button></div>`);
      });
      _push(`<!--]--><button type="button" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm"> \u6DFB\u52A0\u680F\u76EE </button></div></div><div class="mb-6"><label class="block text-sm font-medium text-gray-700 mb-1">\u5185\u5BB9\u6B63\u6587</label>`);
      _push(ssrRenderComponent(QuillEditor, {
        content: content.body,
        "onUpdate:content": ($event) => content.body = $event,
        placeholder: "\u5728\u6B64\u8F93\u5165\u5185\u5BB9...",
        class: "w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",
        onEditorChange: handleEditorChange
      }, null, _parent));
      _push(`</div><div class="flex justify-end"><button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"${ssrIncludeBooleanAttr(loading.value) ? " disabled" : ""}>`);
      if (loading.value) {
        _push(`<span>\u4FDD\u5B58\u4E2D...</span>`);
      } else {
        _push(`<span>\u4FDD\u5B58\u5185\u5BB9</span>`);
      }
      _push(`</button></div></form></div>`);
      if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">${ssrInterpolate(error.value)}</div>`);
      } else {
        _push(`<!---->`);
      }
      if (showPronunciationDialog.value) {
        _push(`<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full"><h3 class="text-xl font-bold mb-4">\u6DFB\u52A0\u97F3\u6807\u6807\u6CE8</h3><p class="mb-2">\u9009\u4E2D\u5355\u8BCD: <strong>${ssrInterpolate(selectedText.value)}</strong></p><div class="mb-4"><label for="pronunciation" class="block text-sm font-medium text-gray-700 mb-1">\u97F3\u6807</label><input id="pronunciation"${ssrRenderAttr("value", pronunciationInput.value)} type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="\u8F93\u5165\u97F3\u6807\uFF0C\u5982 /h\u0259\u02C8l\u0259\u028A/"></div><div class="flex justify-end space-x-3"><button class="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"> \u53D6\u6D88 </button><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"> \u786E\u8BA4 </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/content/new.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=new-B1izHtlP.mjs.map
