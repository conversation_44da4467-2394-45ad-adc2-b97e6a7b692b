import { ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAtt<PERSON>, ssrRenderComponent, ssrRenderList, ssrInterpolate, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { a as useToast } from './useApi-Dd_IPgYv.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './nuxt-link-0Ezu_WKY.mjs';

const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const messages = ref([]);
    const currentPage = ref(1);
    ref(10);
    const totalPages = ref(1);
    const filters = ref({
      type: "",
      status: ""
    });
    const stats = ref({
      total: 0,
      unread: 0,
      processing: 0,
      processed: 0,
      typeDistribution: []
    });
    const statsCards = computed(() => [
      {
        label: "\u603B\u6D88\u606F\u6570",
        value: stats.value.total,
        color: "text-blue-600"
      },
      {
        label: "\u672A\u8BFB\u6D88\u606F",
        value: stats.value.unread,
        color: "text-red-600"
      },
      {
        label: "\u5904\u7406\u4E2D",
        value: stats.value.processing,
        color: "text-yellow-600"
      },
      {
        label: "\u5DF2\u5904\u7406",
        value: stats.value.processed,
        color: "text-green-600"
      }
    ]);
    useToast();
    function formatType(type) {
      const typeMap = {
        "FEEDBACK": "\u53CD\u9988",
        "SUGGESTION": "\u5EFA\u8BAE",
        "ACTIVITY_SIGNUP": "\u6D3B\u52A8\u62A5\u540D"
      };
      return typeMap[type] || type;
    }
    function formatStatus(status) {
      const statusMap = {
        "UNREAD": "\u672A\u8BFB",
        "READ": "\u5DF2\u8BFB",
        "PROCESSED": "\u5DF2\u5904\u7406"
      };
      return statusMap[status] || status;
    }
    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString();
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))} data-v-7ccde3e3>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6 bg-gray-100 min-h-screen ml-64" data-v-7ccde3e3><div class="message-management bg-white p-6 rounded-lg shadow" data-v-7ccde3e3><h1 class="text-2xl font-bold mb-6" data-v-7ccde3e3>\u7528\u6237\u7559\u8A00\u7BA1\u7406</h1><div class="grid grid-cols-4 gap-4 mb-6" data-v-7ccde3e3><!--[-->`);
      ssrRenderList(statsCards.value, (stat, key) => {
        _push(`<div class="bg-gray-50 p-4 rounded-lg shadow-sm" data-v-7ccde3e3><div class="text-sm text-gray-500" data-v-7ccde3e3>${ssrInterpolate(stat.label)}</div><div class="text-2xl font-bold mt-2" data-v-7ccde3e3>${ssrInterpolate(stat.value)}</div></div>`);
      });
      _push(`<!--]--></div><div class="filters mb-4 flex space-x-4" data-v-7ccde3e3><select class="p-2 border rounded" data-v-7ccde3e3><option value="" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "") : ssrLooseEqual(filters.value.type, "")) ? " selected" : ""}>\u5168\u90E8\u7C7B\u578B</option><option value="FEEDBACK" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "FEEDBACK") : ssrLooseEqual(filters.value.type, "FEEDBACK")) ? " selected" : ""}>\u53CD\u9988</option><option value="SUGGESTION" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "SUGGESTION") : ssrLooseEqual(filters.value.type, "SUGGESTION")) ? " selected" : ""}>\u5EFA\u8BAE</option><option value="ACTIVITY_SIGNUP" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "ACTIVITY_SIGNUP") : ssrLooseEqual(filters.value.type, "ACTIVITY_SIGNUP")) ? " selected" : ""}>\u6D3B\u52A8\u62A5\u540D</option></select><select class="p-2 border rounded" data-v-7ccde3e3><option value="" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "") : ssrLooseEqual(filters.value.status, "")) ? " selected" : ""}>\u5168\u90E8\u72B6\u6001</option><option value="UNREAD" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "UNREAD") : ssrLooseEqual(filters.value.status, "UNREAD")) ? " selected" : ""}>\u672A\u8BFB</option><option value="READ" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "READ") : ssrLooseEqual(filters.value.status, "READ")) ? " selected" : ""}>\u5DF2\u8BFB</option><option value="PROCESSED" data-v-7ccde3e3${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "PROCESSED") : ssrLooseEqual(filters.value.status, "PROCESSED")) ? " selected" : ""}>\u5DF2\u5904\u7406</option></select><button class="bg-blue-500 text-white px-4 py-2 rounded" data-v-7ccde3e3> \u7B5B\u9009 </button></div><table class="w-full border-collapse" data-v-7ccde3e3><thead data-v-7ccde3e3><tr class="bg-gray-100" data-v-7ccde3e3><th class="border p-2" data-v-7ccde3e3>\u7C7B\u578B</th><th class="border p-2" data-v-7ccde3e3>\u5185\u5BB9</th><th class="border p-2" data-v-7ccde3e3>\u63D0\u4EA4\u65F6\u95F4</th><th class="border p-2" data-v-7ccde3e3>\u72B6\u6001</th><th class="border p-2" data-v-7ccde3e3>\u64CD\u4F5C</th></tr></thead><tbody data-v-7ccde3e3><!--[-->`);
      ssrRenderList(messages.value, (message) => {
        _push(`<tr class="hover:bg-gray-50" data-v-7ccde3e3><td class="border p-2" data-v-7ccde3e3>${ssrInterpolate(formatType(message.type))}</td><td class="border p-2 truncate max-w-xs" data-v-7ccde3e3>${ssrInterpolate(message.content)}</td><td class="border p-2" data-v-7ccde3e3>${ssrInterpolate(formatDate(message.createdAt))}</td><td class="border p-2" data-v-7ccde3e3>${ssrInterpolate(formatStatus(message.status))}</td><td class="border p-2 space-x-2" data-v-7ccde3e3>`);
        if (message.status === "UNREAD") {
          _push(`<button class="bg-green-500 text-white px-2 py-1 rounded text-sm" data-v-7ccde3e3> \u6807\u8BB0\u5DF2\u8BFB </button>`);
        } else {
          _push(`<!---->`);
        }
        if (message.status !== "PROCESSED") {
          _push(`<button class="bg-blue-500 text-white px-2 py-1 rounded text-sm" data-v-7ccde3e3> \u6807\u8BB0\u5904\u7406 </button>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<button class="bg-red-500 text-white px-2 py-1 rounded text-sm" data-v-7ccde3e3> \u5220\u9664 </button></td></tr>`);
      });
      _push(`<!--]--></tbody></table><div class="pagination mt-4 flex justify-between items-center" data-v-7ccde3e3><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="bg-gray-200 px-4 py-2 rounded disabled:opacity-50" data-v-7ccde3e3> \u4E0A\u4E00\u9875 </button><span data-v-7ccde3e3>\u7B2C ${ssrInterpolate(currentPage.value)} \u9875\uFF0C\u5171 ${ssrInterpolate(totalPages.value)} \u9875</span><button${ssrIncludeBooleanAttr(currentPage.value >= totalPages.value) ? " disabled" : ""} class="bg-gray-200 px-4 py-2 rounded disabled:opacity-50" data-v-7ccde3e3> \u4E0B\u4E00\u9875 </button></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/messages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-7ccde3e3"]]);

export { index as default };
//# sourceMappingURL=index-B9KI9N73.mjs.map
