import { computed, ref, watch, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrInterpolate, ssrRenderClass } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { u as useContentStore } from './content-urLiV7FI.mjs';
import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './api-BjJAe0gw.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const contentStore = useContentStore();
    const contents = computed(() => contentStore.contents);
    const categorySearch = ref("");
    const filterType = ref("");
    ref(null);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalItems = ref(0);
    const pagination = ref(null);
    const fetchContents = async () => {
      try {
        const result = await contentStore.fetchContents({
          page: currentPage.value,
          pageSize: pageSize.value,
          category: categorySearch.value || void 0,
          type: filterType.value || void 0
        });
        console.log("\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u7ED3\u679C:", result);
        if (result == null ? void 0 : result.pagination) {
          pagination.value = result.pagination;
          totalItems.value = result.pagination.total;
          console.log("\u5206\u9875\u4FE1\u606F:", pagination.value);
          console.log("\u603B\u6761\u6570:", totalItems.value);
          console.log("\u603B\u9875\u6570:", Math.ceil(totalItems.value / pageSize.value));
        } else {
          console.warn("\u672A\u83B7\u53D6\u5230\u5206\u9875\u4FE1\u606F");
        }
      } catch (error) {
        console.error("\u83B7\u53D6\u5185\u5BB9\u5217\u8868\u5931\u8D25", error);
      }
    };
    const totalPages = computed(() => {
      return pagination.value ? pagination.value.pageCount : 1;
    });
    const displayedPages = computed(() => {
      const total = totalPages.value;
      const current = currentPage.value;
      if (total <= 7) {
        return Array.from({ length: total }, (_, i) => i + 1);
      }
      const pages = [];
      pages.push(1);
      if (current <= 3) {
        pages.push(2, 3, 4, "...", total - 1, total);
      } else if (current >= total - 2) {
        pages.push("...", total - 4, total - 3, total - 2, total - 1, total);
      } else {
        pages.push("...", current - 1, current, current + 1, "...", total);
      }
      return pages;
    });
    const formatContentType = (type) => {
      const typeMap = {
        ARTICLE: "\u6587\u7AE0",
        LESSON: "\u8BFE\u7A0B",
        EXERCISE: "\u7EC3\u4E60",
        NOTE: "\u7B14\u8BB0"
      };
      return typeMap[type] || type;
    };
    const formatDate = (date) => {
      return new Date(date).toLocaleString();
    };
    watch([currentPage, filterType, categorySearch], () => {
      fetchContents();
    }, { deep: true });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex" }, _attrs))}>`);
      _push(ssrRenderComponent(AdminSidebar, null, null, _parent));
      _push(`<div class="flex-1 p-6"><div class="flex justify-between items-center mb-6"><h1 class="text-2xl font-bold">\u5185\u5BB9\u7BA1\u7406</h1><button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"> \u521B\u5EFA\u5185\u5BB9 </button></div><div class="flex justify-between mb-6"><div class="flex space-x-2"><div class="relative"><input${ssrRenderAttr("value", categorySearch.value)} type="text" placeholder="\u641C\u7D22\u680F\u76EE..." class="border rounded px-3 py-2 pr-8">`);
      if (categorySearch.value) {
        _push(`<span class="absolute right-2 top-2 cursor-pointer text-gray-500"> \xD7 </span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><select class="border rounded px-3 py-2"><option value=""${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "") : ssrLooseEqual(filterType.value, "")) ? " selected" : ""}>\u5168\u90E8\u7C7B\u578B</option><option value="ARTICLE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "ARTICLE") : ssrLooseEqual(filterType.value, "ARTICLE")) ? " selected" : ""}>\u6587\u7AE0</option><option value="LESSON"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "LESSON") : ssrLooseEqual(filterType.value, "LESSON")) ? " selected" : ""}>\u8BFE\u7A0B</option><option value="EXERCISE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "EXERCISE") : ssrLooseEqual(filterType.value, "EXERCISE")) ? " selected" : ""}>\u7EC3\u4E60</option><option value="NOTE"${ssrIncludeBooleanAttr(Array.isArray(filterType.value) ? ssrLooseContain(filterType.value, "NOTE") : ssrLooseEqual(filterType.value, "NOTE")) ? " selected" : ""}>\u7B14\u8BB0</option></select></div></div>`);
      if (unref(contentStore).loading) {
        _push(`<div class="text-center"> \u52A0\u8F7D\u4E2D... </div>`);
      } else if (contents.value.length === 0) {
        _push(`<div class="text-center text-gray-500"> \u6682\u65E0\u5185\u5BB9 </div>`);
      } else {
        _push(`<div class="bg-white shadow-md rounded"><table class="w-full"><thead><tr class="bg-gray-100 border-b"><th class="p-3 text-left">\u6807\u9898</th><th class="p-3 text-left">\u7C7B\u578B</th><th class="p-3 text-left">\u680F\u76EE</th><th class="p-3 text-left">\u4F5C\u8005</th><th class="p-3 text-left">\u521B\u5EFA\u65F6\u95F4</th><th class="p-3 text-left">\u64CD\u4F5C</th></tr></thead><tbody><!--[-->`);
        ssrRenderList(contents.value, (content) => {
          var _a;
          _push(`<tr class="border-b hover:bg-gray-50"><td class="p-3">${ssrInterpolate(content.title || "\u65E0\u6807\u9898")}</td><td class="p-3">${ssrInterpolate(formatContentType(content.type))}</td><td class="p-3">${ssrInterpolate(content.categories ? content.categories.join(", ") : "-")}</td><td class="p-3">${ssrInterpolate(((_a = content.user) == null ? void 0 : _a.username) || "\u672A\u77E5")}</td><td class="p-3">${ssrInterpolate(formatDate(content.createdAt))}</td><td class="p-3"><div class="flex space-x-2"><button class="text-green-500 hover:text-green-700"> \u9884\u89C8 </button><button class="text-blue-500 hover:text-blue-700"> \u7F16\u8F91 </button><button class="text-red-500 hover:text-red-700"> \u5220\u9664 </button></div></td></tr>`);
        });
        _push(`<!--]--></tbody></table>`);
        if (pagination.value && totalItems.value > 0) {
          _push(`<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200"><div class="flex-1 flex justify-between sm:hidden"><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"])}"> \u4E0A\u4E00\u9875 </button><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"])}"> \u4E0B\u4E00\u9875 </button></div><div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"><div><p class="text-sm text-gray-700"> \u663E\u793A\u7B2C <span class="font-medium">${ssrInterpolate(contents.value.length > 0 ? (currentPage.value - 1) * pageSize.value + 1 : 0)}</span> \u81F3 <span class="font-medium">${ssrInterpolate(Math.min(currentPage.value * pageSize.value, totalItems.value))}</span> \u6761\uFF0C\u5171 <span class="font-medium">${ssrInterpolate(totalItems.value)}</span> \u6761 </p></div><div><nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination"><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u9996\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></button><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === 1 }, "relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0A\u4E00\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></button><!--[-->`);
          ssrRenderList(displayedPages.value, (page) => {
            _push(`<!--[-->`);
            if (page !== "...") {
              _push(`<button class="${ssrRenderClass([
                currentPage.value === page ? "z-10 bg-blue-50 border-blue-500 text-blue-600" : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50",
                "relative inline-flex items-center px-4 py-2 border text-sm font-medium"
              ])}">${ssrInterpolate(page)}</button>`);
            } else {
              _push(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"> ... </span>`);
            }
            _push(`<!--]-->`);
          });
          _push(`<!--]--><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u4E0B\u4E00\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg></button><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([{ "opacity-50 cursor-not-allowed": currentPage.value === totalPages.value }, "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"])}"><span class="sr-only">\u5C3E\u9875</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg></button></nav></div></div></div>`);
        } else {
          _push(`<div class="bg-white px-4 py-3 text-center text-sm text-gray-500 border-t border-gray-200">${ssrInterpolate(pagination.value ? `\u53EA\u6709\u4E00\u9875\u6570\u636E\uFF0C\u5171${totalItems.value}\u6761` : "\u672A\u83B7\u53D6\u5230\u5206\u9875\u4FE1\u606F")}</div>`);
        }
        _push(`</div>`);
      }
      if (unref(contentStore).error) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">${ssrInterpolate(unref(contentStore).error)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/content/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-AtkGcp7W.mjs.map
