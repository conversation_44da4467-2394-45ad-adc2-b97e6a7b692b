import { A as AdminSidebar } from './AdminSidebar-CrY0sq6S.mjs';
import { defineComponent, ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { _ as _sfc_main$1, a as _sfc_main$2, b as _sfc_main$7 } from './CreateCourseModal-CREXwZJc.mjs';
import { a as api } from './api-BjJAe0gw.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-0Ezu_WKY.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';
import './LoadingSpinner-Bp5Uxtv1.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "courses",
  __ssrInlineRender: true,
  setup(__props) {
    const coursePacks = ref([]);
    const selectedCourse = ref(null);
    const showCreateModal = ref(false);
    const showCoverModal = ref(false);
    const selectedCourseForCover = ref(null);
    const error = ref("");
    const loading = ref(false);
    const searchQuery = ref("");
    const expandedPacks = ref([]);
    const searching = ref(false);
    const searchResults = ref([]);
    const showSearchResults = ref(false);
    const searchResultGroups = computed(() => {
      const groups = {};
      searchResults.value.forEach((result) => {
        const key = result.courseId;
        if (!groups[key]) {
          groups[key] = {
            courseId: result.courseId,
            courseTitle: result.courseTitle,
            coursePackId: result.coursePackId,
            coursePackTitle: result.coursePackTitle,
            count: 0
          };
        }
        groups[key].count++;
      });
      return Object.values(groups);
    });
    const fetchCoursePacks = async () => {
      loading.value = true;
      error.value = "";
      try {
        console.log("\u5F00\u59CB\u83B7\u53D6\u8BFE\u7A0B\u5305\u6570\u636E...");
        const data = await api.get("/api/admin/course-packages");
        coursePacks.value = data && data.packages ? data.packages : Array.isArray(data) ? data : [];
        console.log("\u83B7\u53D6\u5230\u8BFE\u7A0B\u5305\u6570\u636E:", coursePacks.value.length);
        if (coursePacks.value.length > 0 && expandedPacks.value.length === 0) {
          expandedPacks.value = [coursePacks.value[0].id];
        }
      } catch (err) {
        console.error("\u83B7\u53D6\u8BFE\u7A0B\u5305\u51FA\u9519:", err);
        error.value = "\u83B7\u53D6\u8BFE\u7A0B\u5305\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5";
      } finally {
        loading.value = false;
      }
    };
    const refresh = () => fetchCoursePacks();
    return (_ctx, _push, _parent, _attrs) => {
      const _component_AdminSidebar = AdminSidebar;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-gray-100" }, _attrs))} data-v-817f246d>`);
      _push(ssrRenderComponent(_component_AdminSidebar, null, null, _parent));
      _push(`<div class="ml-64" data-v-817f246d><div class="flex-1 p-8" data-v-817f246d><div class="w-full max-w-6xl mx-auto" data-v-817f246d><h1 class="text-2xl font-bold mb-8 text-center" data-v-817f246d>\u8BFE\u7A0B\u7BA1\u7406</h1>`);
      if (error.value) {
        _push(`<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" data-v-817f246d><p data-v-817f246d>${ssrInterpolate(error.value)}</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="mb-6 text-right" data-v-817f246d><button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg shadow transition" data-v-817f246d><span class="mr-2" data-v-817f246d>+</span> \u521B\u5EFA\u65B0\u8BFE\u7A0B </button></div><div class="bg-gray-50 p-4 rounded-lg shadow mb-6 flex flex-wrap gap-4 items-center" data-v-817f246d><input${ssrRenderAttr("value", searchQuery.value)} type="text" placeholder="\u641C\u7D22\u8BFE\u7A0B\u3001\u8BED\u53E5..." class="flex-1 min-w-[200px] px-4 py-2 border rounded-lg shadow-sm focus:ring focus:border-blue-300" data-v-817f246d><button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg shadow transition" data-v-817f246d> \u641C\u7D22 </button></div>`);
      if (showSearchResults.value) {
        _push(`<div class="bg-white p-4 rounded-lg shadow mb-6" data-v-817f246d><div class="flex justify-between items-center mb-4" data-v-817f246d><h3 class="text-lg font-bold" data-v-817f246d>\u641C\u7D22\u7ED3\u679C</h3><button class="text-gray-500 hover:text-gray-700" data-v-817f246d><span class="mr-1" data-v-817f246d>\xD7</span> \u5173\u95ED </button></div>`);
        if (searching.value) {
          _push(`<div class="text-center py-4" data-v-817f246d><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto" data-v-817f246d></div><p class="mt-2" data-v-817f246d>\u641C\u7D22\u4E2D...</p></div>`);
        } else if (searchResults.value.length === 0) {
          _push(`<div class="text-center py-4" data-v-817f246d><p data-v-817f246d>\u672A\u627E\u5230\u4E0E&quot;${ssrInterpolate(searchQuery.value)}&quot;\u76F8\u5173\u7684\u5185\u5BB9</p></div>`);
        } else if (searchResults.value.length === 1) {
          _push(`<div class="border rounded-lg overflow-hidden" data-v-817f246d><div class="bg-gray-50 p-3 font-bold border-b" data-v-817f246d>${ssrInterpolate(searchResults.value[0].coursePackTitle)} - ${ssrInterpolate(searchResults.value[0].courseTitle)}</div><div class="p-4" data-v-817f246d><div class="mb-2" data-v-817f246d><span class="font-bold" data-v-817f246d>\u4E2D\u6587\uFF1A</span>${ssrInterpolate(searchResults.value[0].chinese)}</div><div class="mb-2" data-v-817f246d><span class="font-bold" data-v-817f246d>\u82F1\u6587\uFF1A</span>${ssrInterpolate(searchResults.value[0].english)}</div><div data-v-817f246d><span class="font-bold" data-v-817f246d>\u97F3\u6807\uFF1A</span>${ssrInterpolate(searchResults.value[0].soundmark)}</div><button class="mt-3 bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-3 rounded text-sm" data-v-817f246d> \u67E5\u770B\u8BFE\u7A0B </button></div></div>`);
        } else {
          _push(`<div data-v-817f246d><p class="mb-3" data-v-817f246d>\u5BF9&quot;${ssrInterpolate(searchQuery.value)}&quot;\u7684\u641C\u7D22\u7ED3\u679C\u6709 ${ssrInterpolate(searchResults.value.length)} \u4E2A\uFF0C\u5206\u522B\u5728\uFF1A</p><ul class="list-disc pl-5 mb-4" data-v-817f246d><!--[-->`);
          ssrRenderList(searchResultGroups.value, (group, index) => {
            _push(`<li class="mb-1" data-v-817f246d>${ssrInterpolate(group.coursePackTitle)} - ${ssrInterpolate(group.courseTitle)} (${ssrInterpolate(group.count)}\u4E2A\u7ED3\u679C) <button class="ml-2 bg-blue-100 hover:bg-blue-200 text-blue-700 py-0.5 px-2 rounded text-xs" data-v-817f246d> \u67E5\u770B\u8BFE\u7A0B </button></li>`);
          });
          _push(`<!--]--></ul></div>`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (loading.value && !searching.value) {
        _push(`<div class="w-full flex justify-center py-12" data-v-817f246d><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" data-v-817f246d></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex gap-8" data-v-817f246d><aside class="w-64 min-h-[600px] bg-white border-r border-gray-200 rounded-lg shadow-sm p-4 flex-shrink-0" data-v-817f246d>`);
      if (!coursePacks.value || coursePacks.value.length === 0) {
        _push(`<div class="text-gray-400 text-center py-8" data-v-817f246d> \u6682\u65E0\u8BFE\u7A0B\u5305\u6570\u636E </div>`);
      } else {
        _push(`<!--[-->`);
        ssrRenderList(coursePacks.value, (pack) => {
          _push(`<div class="mb-6" data-v-817f246d><div class="font-bold text-gray-700 mb-2 flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded" data-v-817f246d><span data-v-817f246d>${ssrInterpolate(pack.title || "\u672A\u547D\u540D\u8BFE\u7A0B\u5305")}</span><span class="text-gray-500" data-v-817f246d><svg xmlns="http://www.w3.org/2000/svg" class="${ssrRenderClass([expandedPacks.value.includes(pack.id) ? "transform rotate-180" : "", "h-4 w-4 transition-transform"])}" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-817f246d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" data-v-817f246d></path></svg></span></div>`);
          if (expandedPacks.value.includes(pack.id)) {
            _push(`<ul class="pl-2 border-l-2 border-gray-100 ml-2" data-v-817f246d>`);
            if (pack.courses && Array.isArray(pack.courses) && pack.courses.length > 0) {
              _push(`<!--[-->`);
              ssrRenderList(pack.courses, (course) => {
                _push(`<li class="pl-2 flex items-center justify-between hover:bg-blue-50 rounded transition mb-1 py-1" data-v-817f246d><span class="cursor-pointer hover:text-blue-600 flex-1" data-v-817f246d>${ssrInterpolate(course.title || "\u672A\u547D\u540D\u8BFE\u7A0B")}</span><div class="flex space-x-2" data-v-817f246d><button class="text-xs text-blue-500 hover:underline" data-v-817f246d>\u7F16\u8F91\u4FE1\u606F</button><button class="text-xs text-red-500 hover:underline" data-v-817f246d>\u5220\u9664</button></div></li>`);
              });
              _push(`<!--]-->`);
            } else {
              _push(`<li class="text-sm text-gray-400 italic pl-2 py-1" data-v-817f246d> \u65E0\u8BFE\u7A0B </li>`);
            }
            _push(`</ul>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        });
        _push(`<!--]-->`);
      }
      _push(`<button class="mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg shadow" data-v-817f246d>\u521B\u5EFA\u65B0\u8BFE\u7A0B</button></aside><main class="flex-1 min-h-[600px] bg-white rounded-lg shadow-lg p-8" data-v-817f246d>`);
      if (selectedCourse.value) {
        _push(ssrRenderComponent(_sfc_main$1, {
          course: selectedCourse.value,
          coursePacks: coursePacks.value
        }, null, _parent));
      } else {
        _push(`<div class="text-gray-400 text-center py-32" data-v-817f246d>\u8BF7\u9009\u62E9\u5DE6\u4FA7\u8BFE\u7A0B\u67E5\u770B\u8BE6\u60C5</div>`);
      }
      _push(`</main></div></div></div></div>`);
      if (showCreateModal.value) {
        _push(ssrRenderComponent(_sfc_main$2, {
          onClose: ($event) => showCreateModal.value = false,
          onCreated: refresh,
          coursePacks: coursePacks.value
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (showCoverModal.value && selectedCourseForCover.value) {
        _push(ssrRenderComponent(_sfc_main$7, {
          onClose: ($event) => showCoverModal.value = false,
          onUpdated: refresh,
          course: selectedCourseForCover.value
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/admin/courses.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const courses = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-817f246d"]]);

export { courses as default };
//# sourceMappingURL=courses-WGhE39zx.mjs.map
