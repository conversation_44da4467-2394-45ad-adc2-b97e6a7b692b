import { ref, watch, mergeProps, useSSRContext } from 'vue';
import { ssr<PERSON><PERSON><PERSON><PERSON><PERSON>, ssrRenderList, ssrInterpolate, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { u as useApi, a as useToast } from './useApi-Dd_IPgYv.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/utils';
import 'unhead/plugins';
import 'vue-router';
import 'combined-stream';
import 'util';
import 'path';
import 'http';
import 'https';
import 'url';
import 'fs';
import 'stream';
import 'crypto';
import 'mime-types';
import 'asynckit';
import 'es-set-tostringtag';
import 'hasown';
import 'proxy-from-env';
import 'follow-redirects';
import 'zlib';
import 'events';

const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const messages = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalPages = ref(1);
    const filters = ref({
      type: "",
      status: ""
    });
    const messageCounts = ref({
      UNREAD: 0,
      READ: 0,
      PROCESSED: 0
    });
    const toast = useToast();
    async function fetchMessages() {
      try {
        const data = await useApi.get("/admin/messages", {
          params: {
            page: currentPage.value,
            pageSize: pageSize.value,
            type: filters.value.type || void 0,
            status: filters.value.status || void 0
          }
        });
        messages.value = data.messages;
        totalPages.value = Math.ceil(data.total / pageSize.value);
      } catch (error) {
        console.error("\u83B7\u53D6\u6D88\u606F\u5217\u8868\u5931\u8D25", error);
        toast.error("\u83B7\u53D6\u6D88\u606F\u5217\u8868\u5931\u8D25");
      }
    }
    function formatType(type) {
      const typeMap = {
        FEEDBACK: "\u53CD\u9988",
        SUGGESTION: "\u5EFA\u8BAE",
        ACTIVITY_SIGNUP: "\u6D3B\u52A8\u62A5\u540D"
      };
      return typeMap[type] || type;
    }
    function formatStatus(status) {
      const statusMap = {
        UNREAD: "\u672A\u8BFB\u6D88\u606F",
        READ: "\u5DF2\u8BFB\u6D88\u606F",
        PROCESSED: "\u5DF2\u5904\u7406\u6D88\u606F"
      };
      return statusMap[status] || status;
    }
    function formatDate(date) {
      return new Date(date).toLocaleString();
    }
    watch(filters, () => {
      currentPage.value = 1;
      fetchMessages();
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "message-management p-6" }, _attrs))} data-v-12633087><h1 class="text-2xl font-bold mb-6" data-v-12633087>\u7528\u6237\u7559\u8A00\u7BA1\u7406</h1><div class="grid grid-cols-3 gap-4 mb-6" data-v-12633087><!--[-->`);
      ssrRenderList(messageCounts.value, (count, status) => {
        _push(`<div class="bg-white shadow rounded-lg p-4 text-center" data-v-12633087><div class="text-xl font-bold" data-v-12633087>${ssrInterpolate(formatStatus(status))}</div><div class="text-3xl font-bold text-blue-600" data-v-12633087>${ssrInterpolate(count)}</div></div>`);
      });
      _push(`<!--]--></div><div class="filters mb-4 flex space-x-4" data-v-12633087><select class="p-2 border rounded" data-v-12633087><option value="" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "") : ssrLooseEqual(filters.value.type, "")) ? " selected" : ""}>\u5168\u90E8\u7C7B\u578B</option><option value="FEEDBACK" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "FEEDBACK") : ssrLooseEqual(filters.value.type, "FEEDBACK")) ? " selected" : ""}>\u53CD\u9988</option><option value="SUGGESTION" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "SUGGESTION") : ssrLooseEqual(filters.value.type, "SUGGESTION")) ? " selected" : ""}>\u5EFA\u8BAE</option><option value="ACTIVITY_SIGNUP" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.type) ? ssrLooseContain(filters.value.type, "ACTIVITY_SIGNUP") : ssrLooseEqual(filters.value.type, "ACTIVITY_SIGNUP")) ? " selected" : ""}>\u6D3B\u52A8\u62A5\u540D</option></select><select class="p-2 border rounded" data-v-12633087><option value="" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "") : ssrLooseEqual(filters.value.status, "")) ? " selected" : ""}>\u5168\u90E8\u72B6\u6001</option><option value="UNREAD" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "UNREAD") : ssrLooseEqual(filters.value.status, "UNREAD")) ? " selected" : ""}>\u672A\u8BFB</option><option value="READ" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "READ") : ssrLooseEqual(filters.value.status, "READ")) ? " selected" : ""}>\u5DF2\u8BFB</option><option value="PROCESSED" data-v-12633087${ssrIncludeBooleanAttr(Array.isArray(filters.value.status) ? ssrLooseContain(filters.value.status, "PROCESSED") : ssrLooseEqual(filters.value.status, "PROCESSED")) ? " selected" : ""}>\u5DF2\u5904\u7406</option></select><button class="bg-blue-500 text-white px-4 py-2 rounded" data-v-12633087> \u7B5B\u9009 </button></div><table class="w-full border-collapse" data-v-12633087><thead data-v-12633087><tr class="bg-gray-100" data-v-12633087><th class="border p-2" data-v-12633087>\u7C7B\u578B</th><th class="border p-2" data-v-12633087>\u5185\u5BB9</th><th class="border p-2" data-v-12633087>\u63D0\u4EA4\u65F6\u95F4</th><th class="border p-2" data-v-12633087>\u72B6\u6001</th><th class="border p-2" data-v-12633087>\u64CD\u4F5C</th></tr></thead><tbody data-v-12633087><!--[-->`);
      ssrRenderList(messages.value, (message) => {
        _push(`<tr class="hover:bg-gray-50" data-v-12633087><td class="border p-2" data-v-12633087>${ssrInterpolate(formatType(message.type))}</td><td class="border p-2 truncate max-w-xs" data-v-12633087>${ssrInterpolate(message.content)}</td><td class="border p-2" data-v-12633087>${ssrInterpolate(formatDate(message.createdAt))}</td><td class="border p-2" data-v-12633087>${ssrInterpolate(formatStatus(message.status))}</td><td class="border p-2 space-x-2" data-v-12633087>`);
        if (message.status === "UNREAD") {
          _push(`<button class="bg-green-500 text-white px-2 py-1 rounded text-sm" data-v-12633087> \u6807\u8BB0\u5DF2\u8BFB </button>`);
        } else {
          _push(`<!---->`);
        }
        if (message.status !== "PROCESSED") {
          _push(`<button class="bg-blue-500 text-white px-2 py-1 rounded text-sm" data-v-12633087> \u6807\u8BB0\u5904\u7406 </button>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<button class="bg-red-500 text-white px-2 py-1 rounded text-sm" data-v-12633087> \u5220\u9664 </button></td></tr>`);
      });
      _push(`<!--]--></tbody></table><div class="pagination mt-4 flex justify-between items-center" data-v-12633087><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="bg-gray-200 px-4 py-2 rounded disabled:opacity-50" data-v-12633087> \u4E0A\u4E00\u9875 </button><span data-v-12633087>\u7B2C ${ssrInterpolate(currentPage.value)} \u9875\uFF0C\u5171 ${ssrInterpolate(totalPages.value)} \u9875</span><button${ssrIncludeBooleanAttr(currentPage.value >= totalPages.value) ? " disabled" : ""} class="bg-gray-200 px-4 py-2 rounded disabled:opacity-50" data-v-12633087> \u4E0B\u4E00\u9875 </button></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/messages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-12633087"]]);

export { index as default };
//# sourceMappingURL=index-CW4HLFql.mjs.map
