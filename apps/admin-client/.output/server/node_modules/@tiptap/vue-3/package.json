{"name": "@tiptap/vue-3", "description": "Vue components for tiptap", "version": "2.26.1", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap vue components"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "umd": "dist/index.umd.js", "types": "dist/index.d.ts", "type": "module", "files": ["src", "dist"], "dependencies": {"@tiptap/extension-bubble-menu": "^2.26.1", "@tiptap/extension-floating-menu": "^2.26.1"}, "devDependencies": {"@tiptap/core": "^2.26.1", "@tiptap/pm": "^2.26.1", "vue": "^3.0.0"}, "peerDependencies": {"@tiptap/core": "^2.7.0", "@tiptap/pm": "^2.7.0", "vue": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/vue-3"}, "sideEffects": false, "scripts": {"clean": "rm -rf dist", "build": "npm run clean && rollup -c"}}