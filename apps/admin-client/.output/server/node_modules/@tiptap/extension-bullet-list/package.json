{"name": "@tiptap/extension-bullet-list", "description": "bullet list extension for tiptap", "version": "2.26.1", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap extension"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "umd": "dist/index.umd.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@tiptap/core": "^2.26.1"}, "peerDependencies": {"@tiptap/core": "^2.7.0"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/extension-bullet-list"}, "scripts": {"clean": "rm -rf dist", "build": "npm run clean && rollup -c"}}