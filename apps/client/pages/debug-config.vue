<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">运行时配置调试</h1>
    
    <div class="bg-gray-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">当前运行时配置:</h2>
      <pre>{{ JSON.stringify(config.public, null, 2) }}</pre>
    </div>
    
    <div class="bg-blue-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">环境信息:</h2>
      <p><strong>NODE_ENV:</strong> {{ process.env.NODE_ENV }}</p>
      <p><strong>当前URL:</strong> {{ currentUrl }}</p>
      <p><strong>是否客户端:</strong> {{ process.client }}</p>
    </div>
    
    <div class="bg-green-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">API测试:</h2>
      <button @click="testApi" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
        测试API连接
      </button>
      <div v-if="apiResult" class="mt-2">
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
const config = useRuntimeConfig()
const apiResult = ref(null)

const currentUrl = process.client ? window.location.href : 'SSR'

async function testApi() {
  try {
    const baseUrl = config.public.backendEndpoint || ''
    const testUrl = `${baseUrl}/frontend-settings/category/general`
    
    console.log('测试API URL:', testUrl)
    
    const response = await fetch(testUrl)
    const data = await response.json()
    
    apiResult.value = {
      success: true,
      url: testUrl,
      data: data
    }
  } catch (error) {
    apiResult.value = {
      success: false,
      error: error.message,
      url: `${config.public.backendEndpoint}/frontend-settings/category/general`
    }
  }
}
</script>
