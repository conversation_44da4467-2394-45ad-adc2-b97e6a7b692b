import { ref, reactive, computed } from 'vue';
import { useRuntimeConfig } from '#app';

// 定义设置缓存类型
interface SettingsCache {
  [key: string]: any;
}

// 定义设置类型
export type FrontendSetting = {
  key: string;
  value: any;
  category: string;
  description?: string;
}

// 缓存设置
const settingsCache: SettingsCache = reactive({});
let isLoading = ref(false);

// 解析设置值的实用函数
function parseSettingValue(value: string): any {
  try {
    // 尝试解析JSON
    return JSON.parse(value);
  } catch {
    // 如果不是JSON，返回原始字符串
    return value;
  }
}

// 确保 URL 正确拼接的函数
function joinUrl(baseUrl: string, path: string): string {
  // 移除 baseUrl 和 path 末尾的 '/'，然后重新拼接
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
  const cleanPath = path.replace(/^\/+/, '');
  return `${cleanBaseUrl}/${cleanPath}`;
}

// 获取设置
export async function getFrontendSetting(key: string, defaultValue: any = null) {
  try {
    const config = useRuntimeConfig();
    const baseUrl = config.public.backendEndpoint || '';

    const fullUrl = joinUrl(baseUrl, `frontend-settings/${key}`);
    console.log(`[公开设置] 请求URL: ${fullUrl}`);
    console.log(`[公开设置] 基础URL: ${baseUrl}`);

    const response = await fetch(fullUrl);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`[公开设置] 获取 ${key} 响应:`, data);
      
      // 严格检查返回值
      if (data && data.value !== null && data.value !== undefined) {
        const rawValue = data.value;
        // 只对明确为 JSON 字符串做解析
        if (typeof rawValue === 'string') {
          const trimmed = rawValue.trim();
          if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
            try {
              const parsedValue = JSON.parse(rawValue);
              console.log(`[公开设置] 解析 ${key}:`, parsedValue);
              return parsedValue;
            } catch (parseError) {
              console.warn(`[公开设置] 解析 ${key} 失败:`, parseError);
              return rawValue;
            }
          } else {
            // 普通字符串直接返回
            return rawValue;
          }
        } else {
          // 已经是对象/数组等，直接返回
          return rawValue;
        }
      } else {
        console.warn(`[公开设置] ${key} 值为空`);
        return null;
      }
    } else {
      console.error(`[公开设置] 获取 ${key} 失败: ${response.status}`);
      return defaultValue;
    }
  } catch (error) {
    console.error(`[公开设置] 网络请求失败:`, error);
    return defaultValue;
  }
}

// 获取分类下的所有设置
export async function getCategorySettings(category: string): Promise<FrontendSetting[]> {
  const config = useRuntimeConfig();
  const baseUrl = config.public.backendEndpoint || '';

  console.log(`[FrontendSettings] 当前基础URL: ${baseUrl}`);
  console.log(`[FrontendSettings] 运行时配置:`, config.public);
  const fullUrl = joinUrl(baseUrl, `frontend-settings/category/${category}`);
  console.log(`[FrontendSettings] 完整请求URL: ${fullUrl}`);

  // 添加更多日志和错误处理
  try {
    const response = await fetch(fullUrl);
    const data = await response.json();
    console.log(`[FrontendSettings] 响应数据:`, data);

    return data;
  } catch (error) {
    console.error(`[FrontendSettings] 获取 ${category} 类别设置失败:`, error);
    return [];
  }
}

// 预加载常用设置
export async function preloadCommonSettings() {
  console.log('[FrontendSettings] 预加载常用设置');
  try {
    const results = await Promise.allSettled([
      getCategorySettings('general'),
      getCategorySettings('home'),
      getCategorySettings('contact'),
      getCategorySettings('links'),
      getCategorySettings('about')
    ]);
    
    results.forEach((result, index) => {
      const categories = ['general', 'home', 'contact', 'links', 'about'];
      const category = categories[index];
      if (result.status === 'fulfilled') {
        console.log(`[FrontendSettings] 成功预加载 ${category} 类别设置`);
      } else {
        console.error(`[FrontendSettings] 预加载 ${category} 类别设置失败:`, result.reason);
      }
    });
  } catch (error) {
    console.error('[FrontendSettings] 预加载设置失败:', error);
  }
}

// 创建一个可复用的设置钩子
export function useFrontendSettings() {
  return {
    getSiteName: () => getFrontendSetting('site_name'),
    getSiteLogo: () => getFrontendSetting('site_logo'),
    getSiteDescription: () => getFrontendSetting('site_description'),
    getContactEmail: () => getFrontendSetting('contact_email'),
    getSocialLinks: () => getFrontendSetting('social_links'),
    getSiteAbout: () => getFrontendSetting('site_about'),
  };
}

export default {
  async getByKey(key: string) {
    try {
      const config = useRuntimeConfig();
      const baseUrl = config.public.backendEndpoint || '';
      const url = joinUrl(baseUrl, `frontend-settings/${key}`);
      
      const response = await fetch(url, {
        method: 'GET'
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取设置失败:', error);
      return null;
    }
  }
} 