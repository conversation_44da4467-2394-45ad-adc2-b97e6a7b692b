"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = require("bcryptjs");
const adminPrisma = new client_1.PrismaClient({
    log: [
        {
            emit: 'stdout',
            level: 'query',
        },
        {
            emit: 'stdout',
            level: 'error',
        },
        {
            emit: 'stdout',
            level: 'info',
        },
        {
            emit: 'stdout',
            level: 'warn',
        },
    ],
});
async function main() {
    try {
        console.log('\n=== 开始创建管理员账户 ===\n');
        console.log('正在检查管理员账户...');
        const adminEmail = '<EMAIL>';
        const adminUsername = 'admin';
        const existingAdmin = await adminPrisma.user.findFirst({
            where: {
                OR: [
                    { email: adminEmail },
                    { username: adminUsername }
                ]
            }
        });
        if (existingAdmin) {
            console.log('管理员账户已存在:', existingAdmin.email);
            console.log('如需重置，请先手动删除该账户');
            return;
        }
        const hashedPassword = await (0, bcryptjs_1.hash)('Admin123!', 10);
        const admin = await adminPrisma.user.create({
            data: {
                email: adminEmail,
                username: adminUsername,
                hashedPassword: hashedPassword,
                avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=admin',
                isNewUser: false,
                isActive: true,
            },
        });
        console.log('管理员账户创建成功:');
        console.log(`- 用户名: ${admin.username}`);
        console.log(`- 邮箱: ${admin.email}`);
        console.log('- 密码: Admin123!');
        console.log('请在首次登录后立即修改默认密码!');
    }
    catch (error) {
        console.error('创建管理员账户失败:', error);
        throw error;
    }
    finally {
        await adminPrisma.$disconnect();
    }
}
main()
    .catch((e) => {
    console.error('程序执行失败:', e);
    process.exit(1);
});
//# sourceMappingURL=admin-seed.js.map