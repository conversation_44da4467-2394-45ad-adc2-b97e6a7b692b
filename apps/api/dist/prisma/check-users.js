"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const checkPrisma = new client_1.PrismaClient();
async function checkUsers() {
    try {
        console.log('=== 查看当前用户信息 ===');
        const users = await checkPrisma.user.findMany({
            select: {
                id: true,
                email: true,
                username: true,
                isActive: true,
                isNewUser: true,
            }
        });
        console.log('当前用户列表:');
        console.table(users);
        console.log(`共 ${users.length} 个用户`);
    }
    catch (error) {
        console.error('查询失败:', error);
    }
    finally {
        await checkPrisma.$disconnect();
    }
}
checkUsers();
//# sourceMappingURL=check-users.js.map