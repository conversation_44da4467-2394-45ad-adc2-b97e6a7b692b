const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient({
    log: [
        {
            emit: 'stdout',
            level: 'query',
        },
        {
            emit: 'stdout',
            level: 'error',
        },
        {
            emit: 'stdout',
            level: 'info',
        },
        {
            emit: 'stdout',
            level: 'warn',
        },
    ],
    datasources: {
        db: {
            url: process.env.DATABASE_URL
        }
    }
});
async function main() {
    try {
        console.log('\n=== 开始初始化数据库 ===\n');
        console.log('正在检查测试用户...');
        const existingUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { email: '<EMAIL>' },
                    { username: 'testuser' }
                ]
            }
        });
        if (existingUser) {
            console.log('找到测试用户:', existingUser.email);
            console.log('正在删除测试用户...');
            const deleteResult = await prisma.user.delete({
                where: {
                    id: existingUser.id
                }
            });
            console.log('测试用户删除结果:', deleteResult);
        }
        else {
            console.log('未找到测试用户');
        }
        const verifyUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { email: '<EMAIL>' },
                    { username: 'testuser' }
                ]
            }
        });
        if (verifyUser) {
            console.log('警告：测试用户仍然存在！');
        }
        else {
            console.log('确认：测试用户已成功删除');
        }
        console.log('创建测试课程包...');
        const coursePack = await prisma.coursePack.upsert({
            where: { id: 'test-course-pack' },
            update: {},
            create: {
                id: 'test-course-pack',
                title: '测试课程包',
                description: '这是一个测试课程包',
                order: 1,
                creatorId: 'system',
                isFree: true,
                difficulty: 1,
                shareLevel: 'PUBLIC',
            },
        });
        console.log('课程包创建成功:', coursePack.title);
        console.log('创建测试课程...');
        const course = await prisma.course.upsert({
            where: { id: 'test-course' },
            update: {},
            create: {
                id: 'test-course',
                title: '测试课程',
                description: '这是一个测试课程',
                order: 1,
                coursePackId: coursePack.id,
            },
        });
        console.log('课程创建成功:', course.title);
        console.log('数据库初始化完成！');
    }
    catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
main()
    .catch((e) => {
    console.error('程序执行失败:', e);
    process.exit(1);
});
//# sourceMappingURL=seed.js.map