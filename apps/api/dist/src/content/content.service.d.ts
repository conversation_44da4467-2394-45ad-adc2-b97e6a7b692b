import { PrismaService } from '../prisma/prisma.service';
export declare class ContentService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(page?: number, pageSize?: number, type?: string): Promise<{
        contents: {
            id: string;
            createdAt: Date;
            type: string;
            title: string;
        }[];
        total: number;
    }>;
    findOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        type: string;
        title: string;
        body: string;
    }>;
    findAdjacentContent(id: string, type?: string): Promise<{
        prevContent: {
            id: string;
            title: string;
        };
        nextContent: {
            id: string;
            title: string;
        };
    }>;
}
