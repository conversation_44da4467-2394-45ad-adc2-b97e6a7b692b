"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ContentService = class ContentService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(page = 1, pageSize = 10, type) {
        const where = type ? { type: type.toUpperCase() } : {};
        const [contents, total] = await Promise.all([
            this.prisma.content.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' },
                select: {
                    id: true,
                    title: true,
                    type: true,
                    createdAt: true
                }
            }),
            this.prisma.content.count({ where })
        ]);
        return { contents, total };
    }
    async findOne(id) {
        const content = await this.prisma.content.findUnique({
            where: { id },
            select: {
                id: true,
                title: true,
                type: true,
                body: true,
                createdAt: true
            }
        });
        if (!content) {
            throw new common_1.NotFoundException('内容不存在');
        }
        return content;
    }
    async findAdjacentContent(id, type) {
        const currentContent = await this.prisma.content.findUnique({
            where: { id },
            select: { createdAt: true, type: true }
        });
        if (!currentContent) {
            throw new common_1.NotFoundException('内容不存在');
        }
        const contentType = type || currentContent.type;
        const where = contentType ? { type: contentType } : {};
        const prevContent = await this.prisma.content.findFirst({
            where: {
                ...where,
                createdAt: { gt: currentContent.createdAt }
            },
            orderBy: { createdAt: 'asc' },
            select: {
                id: true,
                title: true
            }
        });
        const nextContent = await this.prisma.content.findFirst({
            where: {
                ...where,
                createdAt: { lt: currentContent.createdAt }
            },
            orderBy: { createdAt: 'desc' },
            select: {
                id: true,
                title: true
            }
        });
        return { prevContent, nextContent };
    }
};
exports.ContentService = ContentService;
exports.ContentService = ContentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ContentService);
//# sourceMappingURL=content.service.js.map