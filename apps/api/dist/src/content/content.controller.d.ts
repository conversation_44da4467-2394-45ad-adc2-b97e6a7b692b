import { ContentService } from './content.service';
interface ContentWithAnnotations {
    id: string;
    createdAt: Date;
    type: string;
    title: string;
    body: string;
    annotations?: Array<{
        word: string;
        pronunciation: string;
    }>;
    [key: string]: any;
}
export declare class ContentController {
    private readonly contentService;
    constructor(contentService: ContentService);
    findAll(type?: string, page?: number, pageSize?: number): Promise<{
        contents: {
            id: string;
            createdAt: Date;
            type: string;
            title: string;
        }[];
        total: number;
        page: number;
        pageSize: number;
    }>;
    findOne(id: string): Promise<ContentWithAnnotations | {
        error: string;
        statusCode: number;
    }>;
    findAdjacentContent(id: string, type?: string): Promise<{
        prevContent: {
            id: string;
            title: string;
        };
        nextContent: {
            id: string;
            title: string;
        };
    } | {
        error: string;
        statusCode: number;
    }>;
    private processAnnotations;
    private processAnnotationsInTiptapJson;
    private extractAnnotations;
    private extractFromMarks;
}
export {};
