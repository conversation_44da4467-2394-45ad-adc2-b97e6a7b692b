"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentController = void 0;
const common_1 = require("@nestjs/common");
const content_service_1 = require("./content.service");
const public_decorator_1 = require("../auth/decorators/public.decorator");
let ContentController = class ContentController {
    constructor(contentService) {
        this.contentService = contentService;
    }
    async findAll(type, page = 1, pageSize = 10) {
        try {
            const { contents, total } = await this.contentService.findAll(page, pageSize, type);
            return {
                contents,
                total,
                page,
                pageSize
            };
        }
        catch (error) {
            console.error('获取内容列表失败:', error);
            return {
                contents: [],
                total: 0,
                page,
                pageSize
            };
        }
    }
    async findOne(id) {
        try {
            const content = await this.contentService.findOne(id);
            if (!content) {
                return {
                    error: '内容不存在',
                    statusCode: 404
                };
            }
            if (content && content.body) {
                content.body = this.processAnnotations(content.body);
                const extractedAnnotations = this.extractAnnotations(content.body);
                if (id === 'e870d038-4b4c-4a05-8ff6-cab198fa05a0' || content.title.includes('元音音标表')) {
                    console.log('检测到元音音标表文章，添加所有音标作为标注');
                    const vowelPhonetics = [
                        { word: 'see', pronunciation: '/iː/' },
                        { word: 'heat', pronunciation: '/iː/' },
                        { word: 'this', pronunciation: '/ɪ/' },
                        { word: 'sit', pronunciation: '/ɪ/' },
                        { word: 'bed', pronunciation: '/e/' },
                        { word: 'get', pronunciation: '/e/' },
                        { word: 'cat', pronunciation: '/æ/' },
                        { word: 'apple', pronunciation: '/æ/' },
                        { word: 'father', pronunciation: '/ɑː/' },
                        { word: 'car', pronunciation: '/ɑː/' },
                        { word: 'cup', pronunciation: '/ʌ/' },
                        { word: 'sun', pronunciation: '/ʌ/' },
                        { word: 'law', pronunciation: '/ɔː/' },
                        { word: 'saw', pronunciation: '/ɔː/' },
                        { word: 'book', pronunciation: '/ʊ/' },
                        { word: 'foot', pronunciation: '/ʊ/' },
                        { word: 'food', pronunciation: '/uː/' },
                        { word: 'blue', pronunciation: '/uː/' },
                        { word: 'sofa', pronunciation: '/ə/' },
                        { word: 'banana', pronunciation: '/ə/' },
                        { word: 'day', pronunciation: '/eɪ/' },
                        { word: 'make', pronunciation: '/eɪ/' },
                        { word: 'go', pronunciation: '/əʊ/' },
                        { word: 'no', pronunciation: '/əʊ/' },
                        { word: 'my', pronunciation: '/aɪ/' },
                        { word: 'find', pronunciation: '/aɪ/' },
                        { word: 'boy', pronunciation: '/ɔɪ/' },
                        { word: 'join', pronunciation: '/ɔɪ/' },
                        { word: 'now', pronunciation: '/aʊ/' },
                        { word: 'house', pronunciation: '/aʊ/' }
                    ];
                    const mergedAnnotations = [...extractedAnnotations];
                    vowelPhonetics.forEach(phonetic => {
                        const exists = mergedAnnotations.some(a => a.word.toLowerCase() === phonetic.word.toLowerCase());
                        if (!exists) {
                            mergedAnnotations.push(phonetic);
                        }
                    });
                    content.annotations = mergedAnnotations;
                    console.log(`合并后共有 ${content.annotations.length} 个标注`);
                }
                else {
                    content.annotations = extractedAnnotations;
                }
            }
            return content;
        }
        catch (error) {
            console.error('获取内容详情失败:', error);
            return {
                error: '获取内容详情失败',
                statusCode: 500
            };
        }
    }
    async findAdjacentContent(id, type) {
        try {
            const adjacentContent = await this.contentService.findAdjacentContent(id, type);
            return adjacentContent;
        }
        catch (error) {
            console.error('获取相邻内容失败:', error);
            return {
                error: '获取相邻内容失败',
                statusCode: 500
            };
        }
    }
    processAnnotations(body) {
        if (typeof body === 'string') {
            try {
                const parsedBody = JSON.parse(body);
                return this.processAnnotationsInTiptapJson(parsedBody);
            }
            catch {
                return body;
            }
        }
        if (typeof body === 'object' && body.type === 'doc') {
            return this.processAnnotationsInTiptapJson(body);
        }
        return JSON.stringify(body);
    }
    processAnnotationsInTiptapJson(doc) {
        const docCopy = JSON.parse(JSON.stringify(doc));
        const processNode = (node) => {
            if (!node)
                return node;
            if (node.type === 'text') {
                const pronunciationMark = node.marks?.find((mark) => mark.type === 'pronunciation');
                if (pronunciationMark) {
                    node.pronunciation = pronunciationMark.attrs?.pronunciation || node.text;
                    console.log(`处理音标标注: ${node.text} - ${node.pronunciation}`);
                }
            }
            if (node.content && Array.isArray(node.content)) {
                node.content = node.content.map(processNode);
            }
            return node;
        };
        const processedDoc = processNode(docCopy);
        return JSON.stringify(processedDoc);
    }
    extractAnnotations(body) {
        const annotations = [];
        let parsedBody;
        try {
            if (typeof body === 'string') {
                parsedBody = JSON.parse(body);
            }
            else {
                parsedBody = body;
            }
        }
        catch {
            return [];
        }
        console.log('开始提取音标标注，文档结构:', JSON.stringify(parsedBody).substring(0, 200) + '...');
        const extractFromNode = (node) => {
            if (!node)
                return;
            if (node.type === 'text' && node.pronunciation) {
                console.log(`发现标注: ${node.text} - ${node.pronunciation}`);
                annotations.push({
                    word: node.text,
                    pronunciation: node.pronunciation
                });
            }
            if (node.content && Array.isArray(node.content)) {
                node.content.forEach(extractFromNode);
            }
        };
        extractFromNode(parsedBody);
        console.log(`提取完成，共找到 ${annotations.length} 个标注`);
        if (annotations.length === 0) {
            console.log('尝试从marks中直接提取标注');
            this.extractFromMarks(parsedBody, annotations);
            console.log(`从marks中提取完成，共找到 ${annotations.length} 个标注`);
        }
        return annotations;
    }
    extractFromMarks(doc, annotations) {
        const processNode = (node) => {
            if (!node)
                return;
            if (node.type === 'text' && node.marks && Array.isArray(node.marks)) {
                const pronunciationMark = node.marks.find((mark) => mark.type === 'pronunciation');
                if (pronunciationMark && pronunciationMark.attrs && pronunciationMark.attrs.pronunciation) {
                    console.log(`从marks中发现标注: ${node.text} - ${pronunciationMark.attrs.pronunciation}`);
                    annotations.push({
                        word: node.text,
                        pronunciation: pronunciationMark.attrs.pronunciation
                    });
                }
            }
            if (node.content && Array.isArray(node.content)) {
                node.content.forEach(processNode);
            }
        };
        processNode(doc);
    }
};
exports.ContentController = ContentController;
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Query)('type')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/adjacent'),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ContentController.prototype, "findAdjacentContent", null);
exports.ContentController = ContentController = __decorate([
    (0, common_1.Controller)('content'),
    __metadata("design:paramtypes", [content_service_1.ContentService])
], ContentController);
//# sourceMappingURL=content.controller.js.map