"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appGlobalMiddleware = void 0;
const common_1 = require("@nestjs/common");
const exception_filter_1 = require("./exception.filter");
const auth_guard_1 = require("../guards/auth.guard");
const appGlobalMiddleware = (app) => {
    app.useGlobalPipes(new common_1.ValidationPipe());
    app.useGlobalFilters(new exception_filter_1.HttpExceptionFilter());
    app.useGlobalGuards(app.get(auth_guard_1.AuthGuard));
};
exports.appGlobalMiddleware = appGlobalMiddleware;
//# sourceMappingURL=useGlobal.js.map