"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const ioredis_1 = require("@nestjs-modules/ioredis");
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const auth_module_1 = require("../auth/auth.module");
const users_module_1 = require("../users/users.module");
const course_history_module_1 = require("../course-history/course-history.module");
const course_pack_module_1 = require("../course-pack/course-pack.module");
const course_module_1 = require("../course/course.module");
const cron_job_module_1 = require("../cron-job/cron-job.module");
const global_module_1 = require("../global/global.module");
const health_module_1 = require("../health/health.module");
const mastered_element_module_1 = require("../mastered-element/mastered-element.module");
const membership_module_1 = require("../membership/membership.module");
const prisma_module_1 = require("../prisma/prisma.module");
const rank_module_1 = require("../rank/rank.module");
const tool_module_1 = require("../tool/tool.module");
const user_course_progress_module_1 = require("../user-course-progress/user-course-progress.module");
const user_learning_activity_module_1 = require("../user-learning-activity/user-learning-activity.module");
const user_module_1 = require("../user/user.module");
const user_learn_record_module_1 = require("../user-learn-record/user-learn-record.module");
const packages_module_1 = require("../packages/packages.module");
const vocabulary_module_1 = require("../vocabulary/vocabulary.module");
const error_words_module_1 = require("../error-words/error-words.module");
const frontend_settings_module_1 = require("../frontend-settings/frontend-settings.module");
const messages_module_1 = require("../messages/messages.module");
const content_module_1 = require("../content/content.module");
const audio_module_1 = require("../modules/audio/audio.module");
const statement_module_1 = require("../statement/statement.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            global_module_1.GlobalModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            user_module_1.UserModule,
            course_pack_module_1.CoursePackModule,
            course_module_1.CourseModule,
            user_course_progress_module_1.UserCourseProgressModule,
            user_learning_activity_module_1.UserLearningActivityModule,
            user_learn_record_module_1.UserLearnRecordModule,
            tool_module_1.ToolModule,
            rank_module_1.RankModule,
            cron_job_module_1.CronJobModule,
            course_history_module_1.CourseHistoryModule,
            membership_module_1.MembershipModule,
            mastered_element_module_1.MasteredElementModule,
            prisma_module_1.PrismaModule,
            health_module_1.HealthModule,
            packages_module_1.PackagesModule,
            vocabulary_module_1.VocabularyModule,
            error_words_module_1.ErrorWordsModule,
            frontend_settings_module_1.FrontendSettingsModule,
            messages_module_1.MessagesModule,
            content_module_1.ContentModule,
            audio_module_1.AudioModule,
            statement_module_1.StatementModule,
            ioredis_1.RedisModule.forRootAsync({
                useFactory: () => ({
                    config: {
                        url: process.env.REDIS_URL,
                        password: process.env.REDIS_PASSWORD,
                        retryStrategy: (times) => {
                            const delay = Math.min(times * 500, 5000);
                            console.log(`Redis连接重试 (${times}次), 等待${delay}ms...`);
                            return delay;
                        },
                        connectTimeout: 10000,
                        enableAutoPipelining: true,
                        maxRetriesPerRequest: 5,
                        keepAlive: 5000,
                        reconnectOnError: (err) => {
                            const targetError = 'READONLY';
                            if (err.message.includes(targetError)) {
                                return 2;
                            }
                            return 1;
                        },
                    }
                }),
            }),
            schedule_1.ScheduleModule.forRoot(),
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map