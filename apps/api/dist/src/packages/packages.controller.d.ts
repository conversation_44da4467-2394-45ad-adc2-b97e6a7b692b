import { CoursePackService } from "../course-pack/course-pack.service";
import { PublicCoursePackResponse } from "../course-pack/types";
import { CoursePackWithDetails } from "../course-pack/types";
export declare class PackagesController {
    private readonly coursePackService;
    private readonly logger;
    constructor(coursePackService: CoursePackService);
    getPublicPackages(): Promise<PublicCoursePackResponse[]>;
    getPublicCoursePack(coursePackId: string): Promise<CoursePackWithDetails>;
}
