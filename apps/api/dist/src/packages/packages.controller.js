"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackagesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesController = void 0;
const common_1 = require("@nestjs/common");
const course_pack_service_1 = require("../course-pack/course-pack.service");
const public_decorator_1 = require("../auth/decorators/public.decorator");
let PackagesController = PackagesController_1 = class PackagesController {
    constructor(coursePackService) {
        this.coursePackService = coursePackService;
        this.logger = new common_1.Logger(PackagesController_1.name);
    }
    async getPublicPackages() {
        this.logger.log(`[PackagesController] 获取公开课程包列表`);
        try {
            const coursePacks = await this.coursePackService.findPublicCoursePacks();
            this.logger.log(`[PackagesController] 成功获取公开课程包列表，数量: ${coursePacks.length}`);
            return coursePacks;
        }
        catch (error) {
            this.logger.error(`[PackagesController] 获取公开课程包列表失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取公开课程包列表失败，请稍后重试',
                error: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPublicCoursePack(coursePackId) {
        this.logger.log(`[PackagesController] 获取公开课程包详情，ID: ${coursePackId}`);
        try {
            const result = await this.coursePackService.findCoursePackWithCourses(coursePackId, undefined);
            this.logger.log(`[PackagesController] 成功获取公开课程包详情，ID: ${result.id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`[PackagesController] 获取公开课程包详情失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                课程包ID: coursePackId,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取公开课程包详情失败，请稍后重试',
                error: error.message,
                coursePackId
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PackagesController = PackagesController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "getPublicPackages", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(":coursePackId"),
    __param(0, (0, common_1.Param)("coursePackId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "getPublicCoursePack", null);
exports.PackagesController = PackagesController = PackagesController_1 = __decorate([
    (0, common_1.Controller)("packages"),
    __metadata("design:paramtypes", [course_pack_service_1.CoursePackService])
], PackagesController);
//# sourceMappingURL=packages.controller.js.map