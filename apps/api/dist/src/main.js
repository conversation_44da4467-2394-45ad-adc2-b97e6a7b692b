"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const fs = require("fs");
const path = require("path");
const perf_hooks_1 = require("perf_hooks");
const app_module_1 = require("./app/app.module");
const useGlobal_1 = require("./app/useGlobal");
async function bootstrap() {
    const startTime = perf_hooks_1.performance.now();
    const logDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
    const logger = new common_1.Logger('Application');
    logger.log(`应用启动中... [${process.env.NODE_ENV || 'development'}]`);
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log'],
        bufferLogs: true,
        abortOnError: false,
        bodyParser: true,
        cors: {
            origin: true,
            credentials: true,
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            allowedHeaders: [
                'Content-Type',
                'Authorization',
                'Accept',
                'Origin',
                'X-Requested-With',
                'Access-Control-Allow-Origin',
                'Access-Control-Allow-Credentials',
                'Access-Control-Allow-Headers'
            ],
            exposedHeaders: ['Set-Cookie', 'Authorization'],
            maxAge: 86400
        }
    });
    (0, useGlobal_1.appGlobalMiddleware)(app);
    if (process.env.NODE_ENV !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle("ZeroBase Swagger")
            .setDescription("The ZeroBase API description")
            .setVersion("v1.0")
            .addBearerAuth()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup("/swagger", app, document);
    }
    app.useStaticAssets(path.join(__dirname, '../public'), {
        prefix: '/',
        maxAge: 86400000
    });
    app.setGlobalPrefix('api');
    app.flushLogs();
    const port = process.env.PORT || 3002;
    const host = process.env.HOST || '0.0.0.0';
    await app.listen(port, host);
    const endTime = perf_hooks_1.performance.now();
    logger.log(`✅ 应用启动成功 - 地址: ${host}:${port} (${(endTime - startTime).toFixed(0)}ms)`);
}
bootstrap().catch(err => {
    console.error('应用启动失败:', err);
    process.exit(1);
});
//# sourceMappingURL=main.js.map