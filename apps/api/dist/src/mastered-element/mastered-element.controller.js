"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MasteredElementController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasteredElementController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const mastered_element_service_1 = require("./mastered-element.service");
const add_mastered_element_dto_1 = require("./model/add-mastered-element.dto");
const remove_mastered_element_dto_1 = require("./model/remove-mastered-element.dto");
let MasteredElementController = MasteredElementController_1 = class MasteredElementController {
    constructor(masteredElementService) {
        this.masteredElementService = masteredElementService;
        this.logger = new common_1.Logger(MasteredElementController_1.name);
    }
    async addMasteredElement(user, addMasteredElementDto) {
        this.logger.log(`用户尝试添加掌握元素: userId=${user.userId}, content=${JSON.stringify(addMasteredElementDto.content)}`);
        try {
            const result = await this.masteredElementService.addMasteredElement(user.userId, addMasteredElementDto.content.english);
            this.logger.log(`掌握元素添加成功: userId=${user.userId}, english=${addMasteredElementDto.content.english}, id=${result.id}`);
            return {
                id: result.id,
                content: {
                    english: addMasteredElementDto.content.english
                },
                masteredAt: result.createdAt,
                userId: result.userId,
                elementId: result.elementId
            };
        }
        catch (error) {
            this.logger.error(`掌握元素添加失败: userId=${user.userId}, error=${error.message}`, error.stack);
            throw new common_1.HttpException('添加掌握元素时出错', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMasteredElements(user) {
        this.logger.log(`用户请求获取掌握元素列表: userId=${user.userId}`);
        try {
            const elements = await this.masteredElementService.getMasteredElements(user.userId);
            this.logger.log(`掌握元素列表获取成功: userId=${user.userId}, count=${elements.length}`);
            return elements;
        }
        catch (error) {
            this.logger.error(`掌握元素列表获取失败: userId=${user.userId}, error=${error.message}`, error.stack);
            throw new common_1.HttpException('获取掌握元素列表时出错', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeMasteredElement(user, removeMasteredElementDto) {
        this.logger.log(`用户尝试删除掌握元素: userId=${user.userId}, english=${removeMasteredElementDto.elementId}`);
        try {
            await this.masteredElementService.removeMasteredElement(user.userId, removeMasteredElementDto.elementId);
            this.logger.log(`掌握元素删除成功: userId=${user.userId}, english=${removeMasteredElementDto.elementId}`);
            return { success: true };
        }
        catch (error) {
            this.logger.error(`掌握元素删除失败: userId=${user.userId}, error=${error.message}`, error.stack);
            throw new common_1.HttpException('删除掌握元素时出错', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.MasteredElementController = MasteredElementController;
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_mastered_element_dto_1.AddMasteredElementDto]),
    __metadata("design:returntype", Promise)
], MasteredElementController.prototype, "addMasteredElement", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MasteredElementController.prototype, "getMasteredElements", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Delete)(":elementId"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, remove_mastered_element_dto_1.RemoveMasteredElementDto]),
    __metadata("design:returntype", Promise)
], MasteredElementController.prototype, "removeMasteredElement", null);
exports.MasteredElementController = MasteredElementController = MasteredElementController_1 = __decorate([
    (0, common_1.Controller)("mastered-elements"),
    __metadata("design:paramtypes", [mastered_element_service_1.MasteredElementService])
], MasteredElementController);
//# sourceMappingURL=mastered-element.controller.js.map