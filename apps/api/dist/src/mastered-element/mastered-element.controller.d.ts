import { UserEntity } from "../user/user.decorators";
import { MasteredElementService } from "./mastered-element.service";
import { AddMasteredElementDto } from "./model/add-mastered-element.dto";
import { RemoveMasteredElementDto } from "./model/remove-mastered-element.dto";
export declare class MasteredElementController {
    private readonly masteredElementService;
    private readonly logger;
    constructor(masteredElementService: MasteredElementService);
    addMasteredElement(user: UserEntity, addMasteredElementDto: AddMasteredElementDto): Promise<{
        id: string;
        content: {
            english: string;
        };
        masteredAt: Date;
        userId: string;
        elementId: string;
    }>;
    getMasteredElements(user: UserEntity): Promise<{
        id: string;
        content: {
            english: string;
        };
        masteredAt: Date;
        userId: string;
        elementId: string;
    }[]>;
    removeMasteredElement(user: UserEntity, removeMasteredElementDto: RemoveMasteredElementDto): Promise<{
        success: boolean;
    }>;
}
