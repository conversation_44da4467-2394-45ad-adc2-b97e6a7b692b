"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MasteredElementService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasteredElementService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let MasteredElementService = MasteredElementService_1 = class MasteredElementService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(MasteredElementService_1.name);
    }
    async addMasteredElement(userId, elementId) {
        this.logger.log(`尝试添加掌握元素: userId=${userId}, elementId=${elementId}`);
        try {
            const existing = await this.findByUserIdAndElementId(userId, elementId);
            if (existing) {
                this.logger.log(`元素已存在，跳过添加: userId=${userId}, elementId=${elementId}`);
                return existing;
            }
            const result = await this.prisma.masteredElement.create({
                data: {
                    userId: userId,
                    elementId: elementId,
                    elementType: 'statement',
                },
            });
            this.logger.log(`掌握元素添加成功: userId=${userId}, elementId=${elementId}`);
            return result;
        }
        catch (error) {
            this.logger.error(`添加掌握元素失败: userId=${userId}, elementId=${elementId}`, error.stack);
            throw error;
        }
    }
    async getMasteredElements(userId) {
        this.logger.log(`获取用户的掌握元素列表: userId=${userId}`);
        try {
            const elements = await this.prisma.masteredElement.findMany({
                where: {
                    userId: userId,
                },
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const formattedElements = elements.map(element => {
                return {
                    id: element.id,
                    content: {
                        english: element.elementId
                    },
                    masteredAt: element.createdAt,
                    userId: element.userId,
                    elementId: element.elementId
                };
            });
            this.logger.log(`获取成功，共${formattedElements.length}条记录`);
            return formattedElements;
        }
        catch (error) {
            this.logger.error(`获取掌握元素列表失败: userId=${userId}`, error.stack);
            throw error;
        }
    }
    async removeMasteredElement(userId, elementId) {
        this.logger.log(`尝试删除掌握元素: userId=${userId}, elementId=${elementId}`);
        try {
            await this.prisma.masteredElement.deleteMany({
                where: {
                    userId: userId,
                    elementId: elementId,
                },
            });
            this.logger.log(`删除掌握元素成功: userId=${userId}, elementId=${elementId}`);
        }
        catch (error) {
            this.logger.error(`删除掌握元素失败: userId=${userId}, elementId=${elementId}`, error.stack);
            throw error;
        }
    }
    async findByUserIdAndElementId(userId, elementId) {
        this.logger.log(`查找掌握元素: userId=${userId}, elementId=${elementId}`);
        try {
            const element = await this.prisma.masteredElement.findFirst({
                where: {
                    userId: userId,
                    elementId: elementId,
                },
            });
            this.logger.log(`查找结果: ${element ? '已存在' : '不存在'}`);
            return element;
        }
        catch (error) {
            this.logger.error(`查找掌握元素失败: userId=${userId}, elementId=${elementId}`, error.stack);
            throw error;
        }
    }
};
exports.MasteredElementService = MasteredElementService;
exports.MasteredElementService = MasteredElementService = MasteredElementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MasteredElementService);
//# sourceMappingURL=mastered-element.service.js.map