import { PrismaService } from '../prisma/prisma.service';
export declare class MasteredElementService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    addMasteredElement(userId: string, elementId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        elementId: string;
        elementType: string;
        mastery: number;
        notes: string | null;
    }>;
    getMasteredElements(userId: string): Promise<{
        id: string;
        content: {
            english: string;
        };
        masteredAt: Date;
        userId: string;
        elementId: string;
    }[]>;
    removeMasteredElement(userId: string, elementId: string): Promise<void>;
    findByUserIdAndElementId(userId: string, elementId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        elementId: string;
        elementType: string;
        mastery: number;
        notes: string | null;
    }>;
}
