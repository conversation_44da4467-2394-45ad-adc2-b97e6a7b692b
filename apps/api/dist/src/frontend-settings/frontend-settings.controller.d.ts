import { FrontendSettingsService } from './frontend-settings.service';
export declare class FrontendSettingsController {
    private readonly frontendSettingsService;
    constructor(frontendSettingsService: FrontendSettingsService);
    getCategorySettings(category: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    }[] | {
        message: string;
        settings: any[];
    }>;
    getSetting(key: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    } | {
        message: string;
        setting: any;
    }>;
    bulkUpdateSettings(category: string, settings: any[]): Promise<{
        message: string;
        updatedSettings: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            key: string;
            value: import("@prisma/client/runtime/library").JsonValue;
            category: string;
        }[];
    }>;
    deleteSetting(key: string): Promise<{
        message: string;
    }>;
    getAllSettings(): Promise<Record<string, any[]>>;
    getBannerHtml(): Promise<string | number | true | import("@prisma/client/runtime/library").JsonObject | import("@prisma/client/runtime/library").JsonArray>;
}
