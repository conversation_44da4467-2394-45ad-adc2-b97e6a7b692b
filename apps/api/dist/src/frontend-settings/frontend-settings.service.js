"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FrontendSettingsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FrontendSettingsService = class FrontendSettingsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getCategorySettings(category) {
        const settings = await this.prisma.frontendSetting.findMany({
            where: { category },
            orderBy: { key: 'asc' }
        });
        if (!settings || settings.length === 0) {
            return { message: '无数据需要补充', settings: [] };
        }
        return settings;
    }
    async getSetting(key) {
        const setting = await this.prisma.frontendSetting.findUnique({
            where: { key }
        });
        if (!setting) {
            return { message: '无数据需要补充', setting: null };
        }
        return setting;
    }
    async bulkUpdateSettings(settings, category) {
        const updates = settings.map(setting => this.prisma.frontendSetting.upsert({
            where: { key: setting.key },
            update: {
                value: setting.value,
                description: setting.description || undefined,
                category: category || setting.category || 'general'
            },
            create: {
                key: setting.key,
                value: setting.value,
                description: setting.description,
                category: category || setting.category || 'general'
            }
        }));
        try {
            const result = await this.prisma.$transaction(updates);
            return { message: '设置更新成功', updatedSettings: result };
        }
        catch (error) {
            console.error('批量更新设置失败:', error);
            throw new Error('批量更新设置失败');
        }
    }
    async deleteSetting(key) {
        try {
            await this.prisma.frontendSetting.delete({
                where: { key }
            });
            return { message: '设置删除成功' };
        }
        catch (error) {
            console.error('删除设置失败:', error);
            throw new common_1.NotFoundException('设置不存在或删除失败');
        }
    }
    async getAllSettings() {
        return this.prisma.frontendSetting.findMany({
            orderBy: [{ category: 'asc' }, { key: 'asc' }]
        });
    }
    async findByKey(key) {
        return this.prisma.frontendSetting.findUnique({
            where: { key }
        });
    }
};
exports.FrontendSettingsService = FrontendSettingsService;
exports.FrontendSettingsService = FrontendSettingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FrontendSettingsService);
//# sourceMappingURL=frontend-settings.service.js.map