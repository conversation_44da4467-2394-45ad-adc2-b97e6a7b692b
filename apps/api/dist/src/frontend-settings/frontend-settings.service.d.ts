import { PrismaService } from '../prisma/prisma.service';
export declare class FrontendSettingsService {
    private prisma;
    constructor(prisma: PrismaService);
    getCategorySettings(category: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    }[] | {
        message: string;
        settings: any[];
    }>;
    getSetting(key: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    } | {
        message: string;
        setting: any;
    }>;
    bulkUpdateSettings(settings: any[], category?: string): Promise<{
        message: string;
        updatedSettings: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            key: string;
            value: import("@prisma/client/runtime/library").JsonValue;
            category: string;
        }[];
    }>;
    deleteSetting(key: string): Promise<{
        message: string;
    }>;
    getAllSettings(): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    }[]>;
    findByKey(key: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        key: string;
        value: import("@prisma/client/runtime/library").JsonValue;
        category: string;
    }>;
}
