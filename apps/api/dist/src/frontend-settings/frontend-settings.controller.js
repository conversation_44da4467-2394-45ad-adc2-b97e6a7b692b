"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FrontendSettingsController = void 0;
const common_1 = require("@nestjs/common");
const frontend_settings_service_1 = require("./frontend-settings.service");
const swagger_1 = require("@nestjs/swagger");
const public_decorator_1 = require("../auth/decorators/public.decorator");
let FrontendSettingsController = class FrontendSettingsController {
    constructor(frontendSettingsService) {
        this.frontendSettingsService = frontendSettingsService;
    }
    async getCategorySettings(category) {
        return this.frontendSettingsService.getCategorySettings(category);
    }
    async getSetting(key) {
        return this.frontendSettingsService.getSetting(key);
    }
    async bulkUpdateSettings(category, settings) {
        return this.frontendSettingsService.bulkUpdateSettings(settings, category);
    }
    async deleteSetting(key) {
        return this.frontendSettingsService.deleteSetting(key);
    }
    async getAllSettings() {
        const allSettings = await this.frontendSettingsService.getAllSettings();
        const grouped = {};
        for (const setting of allSettings) {
            if (!grouped[setting.category]) {
                grouped[setting.category] = [];
            }
            grouped[setting.category].push(setting);
        }
        return grouped;
    }
    async getBannerHtml() {
        const bannerSetting = await this.frontendSettingsService.findByKey('site_banner');
        return bannerSetting?.value || '';
    }
};
exports.FrontendSettingsController = FrontendSettingsController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('category/:category'),
    (0, swagger_1.ApiOperation)({ summary: '获取特定类别的设置' }),
    (0, swagger_1.ApiParam)({ name: 'category', description: '设置类别' }),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "getCategorySettings", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':key'),
    (0, swagger_1.ApiOperation)({ summary: '获取特定设置' }),
    (0, swagger_1.ApiParam)({ name: 'key', description: '设置键名' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "getSetting", null);
__decorate([
    (0, common_1.Post)('bulk/:category'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新设置' }),
    (0, swagger_1.ApiParam)({ name: 'category', description: '设置类别', required: false }),
    (0, swagger_1.ApiBody)({ description: '设置列表' }),
    __param(0, (0, common_1.Param)('category')),
    __param(1, (0, common_1.Body)('settings')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "bulkUpdateSettings", null);
__decorate([
    (0, common_1.Delete)(':key'),
    (0, swagger_1.ApiOperation)({ summary: '删除特定设置' }),
    (0, swagger_1.ApiParam)({ name: 'key', description: '设置键名' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "deleteSetting", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "getAllSettings", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('banner'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FrontendSettingsController.prototype, "getBannerHtml", null);
exports.FrontendSettingsController = FrontendSettingsController = __decorate([
    (0, swagger_1.ApiTags)('前台设置'),
    (0, common_1.Controller)('frontend-settings'),
    __metadata("design:paramtypes", [frontend_settings_service_1.FrontendSettingsService])
], FrontendSettingsController);
//# sourceMappingURL=frontend-settings.controller.js.map