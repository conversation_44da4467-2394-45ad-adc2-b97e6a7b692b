import { UserEntity } from "../user/user.decorators";
import { QueryParamsDto } from "./model/query-params.dto";
import { UpsertActivityDto } from "./model/upsert-activity.dto";
import { UserLearningActivityService } from "./user-learning-activity.service";
import { CourseHistoryService } from "../course-history/course-history.service";
export declare class UserLearningActivityController {
    private userLearningActivityService;
    private courseHistoryService;
    constructor(userLearningActivityService: UserLearningActivityService, courseHistoryService: CourseHistoryService);
    getDailyTotals(user: UserEntity, queryParams: QueryParamsDto): Promise<{
        date: string;
        count: number;
    }[]>;
    upsertActivity(user: UserEntity, activityData: UpsertActivityDto): Promise<boolean>;
    getTotalLearningTime(user: UserEntity, queryParams: QueryParamsDto): Promise<number>;
    getUserLearningStats(user: UserEntity): Promise<{
        totalLearningTime: number;
        completedLessons: number;
        mistakeCount: number;
        vocabularyCount: number;
        continuousCheckInDays: number;
        weeklyCheckIn: any[];
        todayCheckedIn: boolean;
        totalLearningDays: number;
    }>;
}
