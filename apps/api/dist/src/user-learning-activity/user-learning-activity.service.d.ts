import { PrismaService } from "../prisma/prisma.service";
export declare class UserLearningActivityService {
    private prisma;
    constructor(prisma: PrismaService);
    upsertActivity(userId: string, date: Date, activityType: string, duration: number, courseId?: string, metadata?: any): Promise<boolean>;
    getDailyTotalTime(userId: string, activityType: string, startDate?: Date, endDate?: Date): Promise<{
        date: string;
        count: number;
    }[]>;
    getTotalLearningTime(userId: string, activityType: string, startDate?: Date, endDate?: Date): Promise<number>;
    getTotalLearningDays(userId: string): Promise<number>;
    getDailyLearningTime(userId: string, dateStr: string): Promise<number>;
}
