"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLearningActivityController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const query_params_dto_1 = require("./model/query-params.dto");
const upsert_activity_dto_1 = require("./model/upsert-activity.dto");
const user_learning_activity_service_1 = require("./user-learning-activity.service");
const course_history_service_1 = require("../course-history/course-history.service");
const timezone_1 = require("../utils/timezone");
let UserLearningActivityController = class UserLearningActivityController {
    constructor(userLearningActivityService, courseHistoryService) {
        this.userLearningActivityService = userLearningActivityService;
        this.courseHistoryService = courseHistoryService;
    }
    async getDailyTotals(user, queryParams) {
        const { activityType, startDate, endDate } = queryParams;
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        return this.userLearningActivityService.getDailyTotalTime(user.userId, activityType, start, end);
    }
    async upsertActivity(user, activityData) {
        const result = await this.userLearningActivityService.upsertActivity(user.userId, new Date(activityData.date), activityData.activityType, activityData.duration);
        return result;
    }
    async getTotalLearningTime(user, queryParams) {
        const { activityType, startDate, endDate } = queryParams;
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        const totalTime = await this.userLearningActivityService.getTotalLearningTime(user.userId, activityType, start, end);
        return totalTime;
    }
    async getUserLearningStats(user) {
        const todayStr = (0, timezone_1.getCurrentDateInChina)();
        const today = (0, timezone_1.getCurrentDateObjectInChina)();
        const weekStart = (0, timezone_1.getWeekStartInChina)(today);
        const totalLearningTime = await this.userLearningActivityService.getTotalLearningTime(user.userId, "daily_total");
        const weeklyRecords = await this.userLearningActivityService.getDailyTotalTime(user.userId, "daily_total", weekStart, today);
        const weeklyCheckIn = Array(7).fill(false);
        weeklyRecords.forEach(record => {
            const date = new Date(record.date);
            const dayOfWeek = date.getDay();
            const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
            weeklyCheckIn[dayIndex] = record.count >= 60;
        });
        let continuousCheckInDays = 0;
        const currentDate = new Date(today);
        while (true) {
            const dateStr = currentDate.toISOString().split('T')[0];
            const dayRecord = await this.userLearningActivityService.getDailyLearningTime(user.userId, dateStr);
            if (dayRecord && dayRecord >= 60) {
                continuousCheckInDays++;
                currentDate.setDate(currentDate.getDate() - 1);
            }
            else {
                break;
            }
            if (continuousCheckInDays >= 365) {
                break;
            }
        }
        const todayRecord = weeklyRecords.find(record => {
            const recordDateStr = (0, timezone_1.dateToChineseString)(new Date(record.date));
            return recordDateStr === todayStr;
        });
        const todayCheckedIn = todayRecord?.count >= 60;
        const totalLearningDays = await this.userLearningActivityService.getTotalLearningDays(user.userId);
        const completedLessons = await this.courseHistoryService.getCompletedLessonsCount(user.userId);
        return {
            totalLearningTime,
            completedLessons,
            mistakeCount: 0,
            vocabularyCount: 0,
            continuousCheckInDays,
            weeklyCheckIn,
            todayCheckedIn,
            totalLearningDays
        };
    }
};
exports.UserLearningActivityController = UserLearningActivityController;
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, query_params_dto_1.QueryParamsDto]),
    __metadata("design:returntype", Promise)
], UserLearningActivityController.prototype, "getDailyTotals", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, upsert_activity_dto_1.UpsertActivityDto]),
    __metadata("design:returntype", Promise)
], UserLearningActivityController.prototype, "upsertActivity", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)("total"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, query_params_dto_1.QueryParamsDto]),
    __metadata("design:returntype", Promise)
], UserLearningActivityController.prototype, "getTotalLearningTime", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)("stats"),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserLearningActivityController.prototype, "getUserLearningStats", null);
exports.UserLearningActivityController = UserLearningActivityController = __decorate([
    (0, common_1.Controller)("user-learning-activities"),
    __metadata("design:paramtypes", [user_learning_activity_service_1.UserLearningActivityService,
        course_history_service_1.CourseHistoryService])
], UserLearningActivityController);
//# sourceMappingURL=user-learning-activity.controller.js.map