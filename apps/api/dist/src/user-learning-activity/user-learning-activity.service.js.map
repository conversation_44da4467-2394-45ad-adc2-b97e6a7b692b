{"version": 3, "file": "user-learning-activity.service.js", "sourceRoot": "", "sources": ["../../../src/user-learning-activity/user-learning-activity.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAiE;AACjE,6DAAyD;AACzD,gDAAoD;AAG7C,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,IAAU,EACV,YAAoB,EACpB,QAAgB,EAChB,QAAiB,EACjB,QAAc;QAEd,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC5C,KAAK,EAAE;gBACL,wBAAwB,EAAE;oBACxB,MAAM;oBACN,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC;oBACvB,YAAY;iBACb;aACF;YACD,MAAM,EAAE;gBACN,MAAM;gBACN,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC;gBACvB,YAAY;gBACZ,QAAQ;gBACR,QAAQ;gBACR,QAAQ;aACT;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE;oBACR,SAAS,EAAE,QAAQ;iBACpB;gBACD,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;oBACnB,GAAG,QAAQ;iBACZ,CAAC,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,YAAoB,EAAE,SAAgB,EAAE,OAAc;QAC5F,MAAM,KAAK,GAAQ;YACjB,MAAM;YACN,YAAY;SACb,CAAC;QAGF,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAID,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC5D,EAAE,EAAE,CAAC,MAAM,CAAC;YACZ,KAAK;YACL,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;SAC/B,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,YAAoB,EACpB,SAAgB,EAChB,OAAc;QAEd,MAAM,KAAK,GAAG;YACZ,MAAM;YACN,YAAY;YACZ,GAAG,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACpF,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SACjF,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC;YAC9D,KAAK;YACL,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC5D,EAAE,EAAE,CAAC,MAAM,CAAC;YACZ,KAAK,EAAE;gBACL,MAAM;gBACN,YAAY,EAAE,aAAa;gBAC3B,QAAQ,EAAE;oBACR,EAAE,EAAE,CAAC;iBACN;aACF;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAGH,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,OAAe;QAExD,MAAM,IAAI,GAAG,IAAA,0BAAe,EAAC,OAAO,CAAC,CAAC;QAItC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAC/D,KAAK,EAAE;gBACL,wBAAwB,EAAE;oBACxB,MAAM;oBACN,IAAI,EAAE,IAAI;oBACV,YAAY,EAAE,aAAa;iBAC5B;aACF;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAGH,OAAO,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF,CAAA;AAhJY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,2BAA2B,CAgJvC"}