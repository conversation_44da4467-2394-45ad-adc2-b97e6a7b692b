"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLearningActivityModule = void 0;
const common_1 = require("@nestjs/common");
const auth_common_module_1 = require("../auth/auth.common.module");
const prisma_module_1 = require("../prisma/prisma.module");
const course_history_module_1 = require("../course-history/course-history.module");
const user_learning_activity_controller_1 = require("./user-learning-activity.controller");
const user_learning_activity_service_1 = require("./user-learning-activity.service");
let UserLearningActivityModule = class UserLearningActivityModule {
};
exports.UserLearningActivityModule = UserLearningActivityModule;
exports.UserLearningActivityModule = UserLearningActivityModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_common_module_1.AuthCommonModule, prisma_module_1.PrismaModule, course_history_module_1.CourseHistoryModule],
        controllers: [user_learning_activity_controller_1.UserLearningActivityController],
        providers: [user_learning_activity_service_1.UserLearningActivityService],
        exports: [user_learning_activity_service_1.UserLearningActivityService],
    })
], UserLearningActivityModule);
//# sourceMappingURL=user-learning-activity.module.js.map