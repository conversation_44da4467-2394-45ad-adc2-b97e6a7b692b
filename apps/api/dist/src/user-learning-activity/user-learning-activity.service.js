"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLearningActivityService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const timezone_1 = require("../utils/timezone");
let UserLearningActivityService = class UserLearningActivityService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async upsertActivity(userId, date, activityType, duration, courseId, metadata) {
        if (duration < 0) {
            throw new common_1.BadRequestException("Duration cannot be negative");
        }
        const dateStr = date.toISOString().split("T")[0];
        await this.prisma.userLearningActivity.upsert({
            where: {
                userId_date_activityType: {
                    userId,
                    date: new Date(dateStr),
                    activityType,
                },
            },
            create: {
                userId,
                date: new Date(dateStr),
                activityType,
                duration,
                courseId,
                metadata,
            },
            update: {
                duration: {
                    increment: duration,
                },
                metadata: metadata ? {
                    ...metadata,
                } : undefined,
                updatedAt: new Date(),
            },
        });
        return true;
    }
    async getDailyTotalTime(userId, activityType, startDate, endDate) {
        const where = {
            userId,
            activityType,
        };
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate.toISOString().split("T")[0]);
            }
            if (endDate) {
                where.date.lte = new Date(endDate.toISOString().split("T")[0]);
            }
        }
        const result = await this.prisma.userLearningActivity.groupBy({
            by: ['date'],
            where,
            _sum: {
                duration: true,
            },
        });
        return result.map((item) => ({
            date: item.date.toISOString().split('T')[0],
            count: item._sum.duration || 0,
        }));
    }
    async getTotalLearningTime(userId, activityType, startDate, endDate) {
        const where = {
            userId,
            activityType,
            ...(startDate && { date: { gte: new Date(startDate.toISOString().split("T")[0]) } }),
            ...(endDate && { date: { lte: new Date(endDate.toISOString().split("T")[0]) } }),
        };
        const result = await this.prisma.userLearningActivity.aggregate({
            where,
            _sum: {
                duration: true,
            },
        });
        return result._sum.duration || 0;
    }
    async getTotalLearningDays(userId) {
        const result = await this.prisma.userLearningActivity.groupBy({
            by: ['date'],
            where: {
                userId,
                activityType: 'daily_total',
                duration: {
                    gt: 0
                }
            },
            _count: {
                date: true
            }
        });
        return result.length;
    }
    async getDailyLearningTime(userId, dateStr) {
        const date = (0, timezone_1.dateStringToUTC)(dateStr);
        const result = await this.prisma.userLearningActivity.findUnique({
            where: {
                userId_date_activityType: {
                    userId,
                    date: date,
                    activityType: 'daily_total'
                }
            },
            select: {
                duration: true
            }
        });
        return result?.duration || 0;
    }
};
exports.UserLearningActivityService = UserLearningActivityService;
exports.UserLearningActivityService = UserLearningActivityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserLearningActivityService);
//# sourceMappingURL=user-learning-activity.service.js.map