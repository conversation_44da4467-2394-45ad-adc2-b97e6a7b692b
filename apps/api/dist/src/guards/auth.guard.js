"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthGuard = exports.Public = exports.IS_PUBLIC_KEY = exports.Permissions = exports.UncheckAuth = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const jwt_1 = require("@nestjs/jwt");
const jwt_config_1 = require("../config/jwt.config");
const UncheckAuth = () => (0, common_1.SetMetadata)("uncheck", true);
exports.UncheckAuth = UncheckAuth;
const Permissions = (...permissions) => (0, common_1.SetMetadata)("permissions", permissions);
exports.Permissions = Permissions;
exports.IS_PUBLIC_KEY = 'isPublic';
const Public = () => (0, common_1.SetMetadata)(exports.IS_PUBLIC_KEY, true);
exports.Public = Public;
let AuthGuard = AuthGuard_1 = class AuthGuard {
    constructor(jwtService, reflector) {
        this.jwtService = jwtService;
        this.reflector = reflector;
        this.logger = new common_1.Logger(AuthGuard_1.name);
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(exports.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass()
        ]);
        if (isPublic) {
            this.logger.debug('公共路由，允许访问');
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        const uncheck = this.reflector.get("uncheck", context.getHandler());
        const permissions = this.reflector.get("permissions", context.getHandler());
        if (!token && uncheck) {
            this.logger.debug('无 token 但允许未认证访问');
            request["userId"] = null;
            return true;
        }
        if (!token) {
            this.logger.debug('无 token 且需要认证');
            throw new common_1.UnauthorizedException('未提供认证令牌');
        }
        try {
            const payload = await this.jwtService.verifyAsync(token, {
                secret: jwt_config_1.JWT_CONFIG.secret,
                issuer: jwt_config_1.JWT_CONFIG.issuer,
                audience: jwt_config_1.JWT_CONFIG.audience,
            });
            if (payload.exp && payload.exp * 1000 < Date.now()) {
                this.logger.debug(`Token 已过期: exp=${payload.exp}, now=${Date.now() / 1000}`);
                throw new common_1.UnauthorizedException('认证令牌已过期');
            }
            if (permissions) {
                const userPermissions = payload.permissions || [];
                if (!permissions.every((permission) => userPermissions.includes(permission))) {
                    this.logger.debug(`权限不足: 需要 ${permissions.join(', ')}, 拥有 ${userPermissions.join(', ')}`);
                    throw new common_1.UnauthorizedException('权限不足');
                }
            }
            request["userId"] = payload.sub;
            this.logger.debug(`认证成功: userId=${payload.sub}`);
            return true;
        }
        catch (e) {
            if (!uncheck) {
                this.logger.error(`认证失败: ${e.message}`);
                throw new common_1.UnauthorizedException(e.message);
            }
            this.logger.debug(`认证失败但允许未认证访问: ${e.message}`);
            request["userId"] = null;
            return true;
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(" ") ?? [];
        return type === "Bearer" ? token : undefined;
    }
};
exports.AuthGuard = AuthGuard;
exports.AuthGuard = AuthGuard = AuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        core_1.Reflector])
], AuthGuard);
//# sourceMappingURL=auth.guard.js.map