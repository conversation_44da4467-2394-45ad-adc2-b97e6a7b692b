"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoursePacksAccessGuard = void 0;
const common_1 = require("@nestjs/common");
const course_pack_service_1 = require("../course-pack/course-pack.service");
const membership_service_1 = require("../membership/membership.service");
let CoursePacksAccessGuard = class CoursePacksAccessGuard {
    constructor(coursePackService, membershipService) {
        this.coursePackService = coursePackService;
        this.membershipService = membershipService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const userId = request.userId;
        const coursePack = await this.coursePackService.findOne(request.params.coursePackId);
        if (coursePack.isFree) {
            return true;
        }
        if (!userId) {
            throw new common_1.ForbiddenException("这是会员专属内容");
        }
        const isMember = await this.membershipService.isMember(userId);
        if (!isMember) {
            throw new common_1.ForbiddenException("这是会员专属内容");
        }
        return true;
    }
};
exports.CoursePacksAccessGuard = CoursePacksAccessGuard;
exports.CoursePacksAccessGuard = CoursePacksAccessGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [course_pack_service_1.CoursePackService,
        membership_service_1.MembershipService])
], CoursePacksAccessGuard);
//# sourceMappingURL=course-packs-access.guard.js.map