import { CanActivate, ExecutionContext } from "@nestjs/common";
import { CoursePackService } from "../course-pack/course-pack.service";
import { MembershipService } from "../membership/membership.service";
export declare class CoursePacksAccessGuard implements CanActivate {
    private coursePackService;
    private membershipService;
    constructor(coursePackService: CoursePackService, membershipService: MembershipService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
