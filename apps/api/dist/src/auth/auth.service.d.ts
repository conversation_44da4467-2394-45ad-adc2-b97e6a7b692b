import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { UsersService } from '../users/users.service';
import { User } from '@prisma/client';
export interface LoginResponse {
    access_token: string;
    user: Omit<User, 'hashedPassword'>;
}
export interface RegisterResponse {
    message: string;
    data: Omit<User, 'hashedPassword'>;
}
export declare class AuthService {
    private readonly usersService;
    private readonly jwtService;
    private readonly logger;
    constructor(usersService: UsersService, jwtService: JwtService);
    login(loginDto: LoginDto): Promise<LoginResponse>;
    private getAvatarUrl;
    register(registerDto: RegisterDto): Promise<RegisterResponse>;
}
