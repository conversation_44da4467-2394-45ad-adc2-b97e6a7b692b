"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const bcryptjs_1 = require("bcryptjs");
const users_service_1 = require("../users/users.service");
const crypto_1 = require("crypto");
const jwt_config_1 = require("../config/jwt.config");
let AuthService = AuthService_1 = class AuthService {
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.logger = new common_1.Logger(AuthService_1.name);
    }
    async login(loginDto) {
        this.logger.debug(`尝试登录账号: ${loginDto.account}`);
        const user = await this.usersService.findByEmailOrUsername(loginDto.account);
        if (!user) {
            this.logger.debug(`账号 ${loginDto.account} 不存在`);
            throw new common_1.UnauthorizedException('账号或密码错误');
        }
        const isPasswordValid = await (0, bcryptjs_1.compare)(loginDto.password, user.hashedPassword);
        if (!isPasswordValid) {
            this.logger.debug(`账号 ${loginDto.account} 密码错误`);
            throw new common_1.UnauthorizedException('账号或密码错误');
        }
        this.logger.debug(`账号 ${loginDto.account} 登录成功`);
        const payload = {
            sub: user.id,
            email: user.email
        };
        const access_token = await this.jwtService.signAsync(payload, {
            secret: jwt_config_1.JWT_CONFIG.secret,
            expiresIn: jwt_config_1.JWT_CONFIG.expiresIn,
            issuer: jwt_config_1.JWT_CONFIG.issuer,
            audience: jwt_config_1.JWT_CONFIG.audience,
            algorithm: jwt_config_1.JWT_CONFIG.algorithm,
        });
        const { hashedPassword: _, ...userInfo } = user;
        return {
            access_token,
            user: userInfo
        };
    }
    getAvatarUrl(seed) {
        const id = seed || (0, crypto_1.randomUUID)();
        return `https://api.dicebear.com/7.x/bottts/svg?seed=${id}`;
    }
    async register(registerDto) {
        const existingUserByEmail = await this.usersService.findByEmail(registerDto.email);
        if (existingUserByEmail) {
            this.logger.debug(`邮箱 ${registerDto.email} 已被注册`);
            throw new common_1.ConflictException('该邮箱已被注册');
        }
        const existingUserByUsername = await this.usersService.findByUsername(registerDto.username);
        if (existingUserByUsername) {
            this.logger.debug(`用户名 ${registerDto.username} 已被使用`);
            throw new common_1.ConflictException('该用户名已被使用');
        }
        const user = await this.usersService.createUser({
            email: registerDto.email,
            username: registerDto.username,
            password: registerDto.password,
            avatar: this.getAvatarUrl(registerDto.email),
            isNewUser: true,
        });
        const { hashedPassword: _, ...userInfo } = user;
        return {
            message: '注册成功',
            data: userInfo,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map