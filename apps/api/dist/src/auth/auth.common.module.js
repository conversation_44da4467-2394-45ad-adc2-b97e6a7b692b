"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthCommonModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const JWT_SECRET = process.env.JWT_SECRET || 'sjldk92#sd903mnc./xklsjdf9sdfj';
const JWT_ISSUER = process.env.JWT_ISSUER || 'zerobase';
const JWT_AUDIENCE = process.env.JWT_AUDIENCE || 'zerobase-api';
let AuthCommonModule = class AuthCommonModule {
};
exports.AuthCommonModule = AuthCommonModule;
exports.AuthCommonModule = AuthCommonModule = __decorate([
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.register({
                secret: JWT_SECRET,
                signOptions: {
                    expiresIn: '7d',
                    algorithm: 'HS256',
                    issuer: JWT_ISSUER,
                    audience: JWT_AUDIENCE
                },
                verifyOptions: {
                    algorithms: ['HS256'],
                    issuer: JWT_ISSUER,
                    audience: JWT_AUDIENCE
                }
            }),
        ],
        exports: [jwt_1.JwtModule],
    })
], AuthCommonModule);
//# sourceMappingURL=auth.common.module.js.map