import { PrismaService } from '../prisma/prisma.service';
export declare class StatementService {
    private prisma;
    constructor(prisma: PrismaService);
    searchByEnglish(english: string, requireAudio?: boolean): Promise<{
        id: string;
        order: number;
        soundmark: string;
        chinese: string;
        english: string;
        audioEnUsMale: string;
        audioEnGbMale: string;
        audioEnUsFemale: string;
        audioEnGbFemale: string;
        audioStatus: string;
        audioGeneratedAt: Date;
    }[]>;
}
