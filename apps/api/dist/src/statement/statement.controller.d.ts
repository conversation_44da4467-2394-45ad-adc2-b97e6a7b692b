import { StatementService } from './statement.service';
export declare class StatementController {
    private readonly statementService;
    constructor(statementService: StatementService);
    test(): Promise<{
        message: string;
        timestamp: string;
    }>;
    searchByEnglish(english: string, withAudio?: string): Promise<{
        id: string;
        order: number;
        soundmark: string;
        chinese: string;
        english: string;
        audioEnUsMale: string;
        audioEnGbMale: string;
        audioEnUsFemale: string;
        audioEnGbFemale: string;
        audioStatus: string;
        audioGeneratedAt: Date;
    }[]>;
}
