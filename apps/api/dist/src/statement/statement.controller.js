"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementController = void 0;
const common_1 = require("@nestjs/common");
const statement_service_1 = require("./statement.service");
let StatementController = class StatementController {
    constructor(statementService) {
        this.statementService = statementService;
    }
    async test() {
        return { message: 'StatementController is working!', timestamp: new Date().toISOString() };
    }
    async searchByEnglish(english, withAudio) {
        if (!english || !english.trim()) {
            return [];
        }
        const requireAudio = withAudio === 'true';
        return this.statementService.searchByEnglish(english.trim(), requireAudio);
    }
};
exports.StatementController = StatementController;
__decorate([
    (0, common_1.Get)('test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StatementController.prototype, "test", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('english')),
    __param(1, (0, common_1.Query)('withAudio')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StatementController.prototype, "searchByEnglish", null);
exports.StatementController = StatementController = __decorate([
    (0, common_1.Controller)('statements'),
    __metadata("design:paramtypes", [statement_service_1.StatementService])
], StatementController);
//# sourceMappingURL=statement.controller.js.map