"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let StatementService = class StatementService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async searchByEnglish(english, requireAudio = false) {
        try {
            console.log(`[StatementService] 搜索英文文本: '${english}', 需要音频: ${requireAudio}`);
            const whereCondition = {
                english: english
            };
            if (requireAudio) {
                whereCondition.OR = [
                    { audioEnUsMale: { not: null } },
                    { audioEnUsFemale: { not: null } },
                    { audioEnGbMale: { not: null } },
                    { audioEnGbFemale: { not: null } }
                ];
            }
            const statements = await this.prisma.statement.findMany({
                where: whereCondition,
                select: {
                    id: true,
                    chinese: true,
                    english: true,
                    soundmark: true,
                    order: true,
                    audioStatus: true,
                    audioEnUsMale: true,
                    audioEnUsFemale: true,
                    audioEnGbMale: true,
                    audioEnGbFemale: true,
                    audioGeneratedAt: true
                },
                orderBy: {
                    audioGeneratedAt: 'desc'
                },
                take: 5
            });
            console.log(`[StatementService] 找到 ${statements.length} 个匹配的语句`);
            return statements;
        }
        catch (error) {
            console.error('[StatementService] 搜索语句失败:', error);
            throw error;
        }
    }
};
exports.StatementService = StatementService;
exports.StatementService = StatementService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], StatementService);
//# sourceMappingURL=statement.service.js.map