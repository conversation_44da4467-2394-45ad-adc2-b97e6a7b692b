{"version": 3, "file": "audio-proxy.controller.js", "sourceRoot": "", "sources": ["../../../../src/modules/audio/audio-proxy.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,6EAAgE;AAEhE,yCAAyC;AAGlC,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAA1B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAwPlE,CAAC;IApPO,AAAN,KAAK,CAAC,cAAc;QAClB,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;IAIK,AAAN,KAAK,CAAC,QAAQ,CACG,IAAY,EACZ,OAAe,GAAG,EACd,WAAmB,QAAQ,EAC7B,SAAiB,OAAO,EAC9B,OAAO,EACX,GAAa;QAEpB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,sBAAa,CAAC,cAAc,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,QAAQ,MAAM,IAAI,SAAS,IAAI,WAAW,MAAM,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAErD,IAAI,GAAW,CAAC;QAGhB,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAG3C,MAAM,cAAc,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QACnD,IAAI,oBAAoB,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1E,IAAI,oBAAoB,KAAK,CAAC,CAAC;YAAE,oBAAoB,GAAG,CAAC,CAAC;QAE1D,OAAO,oBAAoB,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;YACpD,MAAM,eAAe,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAE7D,IAAI,CAAC;gBAEH,QAAQ,eAAe,EAAE,CAAC;oBACxB,KAAK,QAAQ;wBAEX,GAAG,GAAG,2CAA2C,kBAAkB,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;wBACzF,MAAM;oBACR,KAAK,MAAM;wBAET,MAAM,QAAQ,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;wBAG1C,MAAM,QAAQ,GAAG;4BAEf,0DAA0D,kBAAkB,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;4BAGxH,6DAA6D,kBAAkB,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;yBAC5H,CAAC;wBAGF,MAAM,cAAc,GAAG;4BACrB,QAAQ,EAAE,oCAAoC;4BAC9C,YAAY,EAAE,oHAAoH;4BAClI,SAAS,EAAE,4BAA4B;4BACvC,QAAQ,EAAE,sBAAsB;4BAChC,iBAAiB,EAAE,yBAAyB;4BAC5C,gBAAgB,EAAE,OAAO;4BACzB,gBAAgB,EAAE,SAAS;yBAC5B,CAAC;wBAEF,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;4BAC3B,IAAI,CAAC;gCACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC;gCAEvC,MAAM,QAAQ,GAAG,MAAM,UAAU;qCAC9B,GAAG,CAAC,GAAG,CAAC;qCACR,GAAG,CAAC,cAAc,CAAC;qCACnB,MAAM,CAAC,IAAI,CAAC;qCACZ,KAAK,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;oCACvB,MAAM,MAAM,GAAG,EAAE,CAAC;oCAClB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oCAC9C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wCACjB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wCACrC,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;wCAEtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;wCAC7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;wCAGrD,MAAM,cAAc,GAClB,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;4CAC9B,MAAM,CAAC,MAAM,GAAG,IAAI;gDACpB,CAEE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;oDAEnD,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CACjE,CAAC;wCAEJ,IAAI,cAAc,EAAE,CAAC;4CACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;4CACpD,OAAO,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;wCAChC,CAAC;wCAGD,MAAM,aAAa,GACjB,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC;4CACjC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;wCAEhD,IAAI,aAAa,EAAE,CAAC;4CAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;4CAC9C,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,CAAC;wCACvD,CAAC;wCAGD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,WAAW,QAAQ,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;wCACzE,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;oCAC9C,CAAC,CAAC,CAAC;gCACL,CAAC,CAAC;qCACD,OAAO,CAAC;oCACP,QAAQ,EAAE,IAAI;oCACd,QAAQ,EAAE,KAAK;iCAChB,CAAC,CAAC;gCAGL,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oCAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;oCAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC;gCACvB,CAAC;4BACH,CAAC;4BAAC,OAAO,KAAK,EAAE,CAAC;gCACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gCAG/D,IAAI,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;oCAC1C,MAAM,IAAI,sBAAa,CACrB,gBAAgB,KAAK,CAAC,OAAO,EAAE,EAC/B,mBAAU,CAAC,WAAW,CACvB,CAAC;gCACJ,CAAC;gCAGD,SAAS;4BACX,CAAC;wBACH,CAAC;wBAGD,MAAM,IAAI,sBAAa,CACrB,yBAAyB,EACzB,mBAAU,CAAC,WAAW,CACvB,CAAC;wBACF,MAAM;oBACR,KAAK,OAAO;wBAEV,MAAM,SAAS,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC7C,GAAG,GAAG,8CAA8C,kBAAkB,CAAC,IAAI,CAAC,4GAA4G,SAAS,EAAE,CAAC;wBACpM,MAAM;gBACV,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,GAAG,UAAU,eAAe,GAAG,CAAC,CAAC;gBAE/D,IAAI,CAAC;oBAEH,MAAM,QAAQ,GAAG,MAAM,UAAU;yBAC9B,GAAG,CAAC,GAAG,CAAC;yBACR,GAAG,CAAC,QAAQ,EAAE,oCAAoC,CAAC;yBACnD,GAAG,CAAC,YAAY,EAAE,oHAAoH,CAAC;yBACvI,MAAM,CAAC,IAAI,CAAC;yBACZ,OAAO,CAAC;wBACP,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,KAAK;qBAChB,CAAC,CAAC;oBAGL,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC;oBACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;oBAG/E,IAAI,WAAW,GAAG,YAAY,CAAC;oBAG/B,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;oBAG3C,GAAG,CAAC,SAAS,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;oBAClD,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,cAAc,CAAC,CAAC;oBAC9D,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,uDAAuD,CAAC,CAAC;oBACvG,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;oBAGxC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,uBAAuB,CAAC,CAAC;oBAGxD,IAAI,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBAClC,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBACjE,CAAC;yBAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;wBACzB,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACxD,CAAC;oBAGD,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;oBACnD,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;oBACpC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;oBACpC,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;oBAEnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,eAAe,kBAAkB,WAAW,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;oBAGrI,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,eAAe,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAGnE,IAAI,oBAAoB,KAAK,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;wBAC5E,MAAM,IAAI,sBAAa,CACrB,OAAO,eAAe,QAAQ,IAAI,UAAU,KAAK,CAAC,OAAO,EAAE,EAC3D,mBAAU,CAAC,WAAW,CACvB,CAAC;oBACJ,CAAC;oBAGD,oBAAoB,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,eAAe,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAGrE,IAAI,oBAAoB,KAAK,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;oBAC5E,MAAM,IAAI,sBAAa,CACrB,WAAW,KAAK,CAAC,OAAO,EAAE,EAC1B,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;gBACJ,CAAC;gBAGD,oBAAoB,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;QAGD,MAAM,IAAI,sBAAa,CACrB,iBAAiB,IAAI,OAAO,EAC5B,mBAAU,CAAC,WAAW,CACvB,CAAC;IACJ,CAAC;CACF,CAAA;AAzPY,oDAAoB;AAKzB;IAFL,IAAA,yBAAM,GAAE;IACR,IAAA,YAAG,EAAC,MAAM,CAAC;;;;0DAMX;AAIK;IAFL,IAAA,yBAAM,GAAE;IACR,IAAA,YAAG,GAAE;IAEH,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,YAAG,GAAE,CAAA;;;;oDAoOP;+BAxPU,oBAAoB;IADhC,IAAA,mBAAU,EAAC,aAAa,CAAC;GACb,oBAAoB,CAyPhC"}