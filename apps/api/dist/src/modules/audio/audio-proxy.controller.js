"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AudioProxyController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioProxyController = void 0;
const common_1 = require("@nestjs/common");
const public_decorator_1 = require("../../auth/decorators/public.decorator");
const superagent = require("superagent");
let AudioProxyController = AudioProxyController_1 = class AudioProxyController {
    constructor() {
        this.logger = new common_1.Logger(AudioProxyController_1.name);
    }
    async testAudioProxy() {
        return {
            success: true,
            message: '音频代理API可用',
        };
    }
    async getAudio(word, type = '1', provider = 'youdao', strict = 'false', headers, res) {
        if (!word) {
            throw new common_1.HttpException('缺少必要参数: word', common_1.HttpStatus.BAD_REQUEST);
        }
        this.logger.log(`接收到音频请求: ${provider} / ${word} / 类型:${type} / 严格模式:${strict}`);
        this.logger.debug(`用户浏览器: ${headers['user-agent']}`);
        let url;
        const userAgent = headers['user-agent'] || '';
        const isMobile = /mobile/i.test(userAgent);
        const audioProviders = ['youdao', 'bing', 'baidu'];
        let currentProviderIndex = audioProviders.indexOf(provider.toLowerCase());
        if (currentProviderIndex === -1)
            currentProviderIndex = 0;
        while (currentProviderIndex < audioProviders.length) {
            const currentProvider = audioProviders[currentProviderIndex];
            try {
                switch (currentProvider) {
                    case 'youdao':
                        url = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(word)}&type=${type}`;
                        break;
                    case 'bing':
                        const bingType = type === '1' ? '0' : '1';
                        const bingUrls = [
                            `https://www.bing.com/dict/mediamp3?blob=audio%2Ffrom%2F${encodeURIComponent(word)}%2F${bingType === '0' ? 'uk' : 'us'}`,
                            `https://global.bing.com/dict/mediamp3?blob=audio%2Ffrom%2F${encodeURIComponent(word)}%2F${bingType === '0' ? 'uk' : 'us'}`
                        ];
                        const requestHeaders = {
                            'Accept': 'audio/mpeg,audio/*;q=0.9,*/*;q=0.8',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
                            'Referer': 'https://www.bing.com/dict/',
                            'Origin': 'https://www.bing.com',
                            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                            'Sec-Fetch-Dest': 'audio',
                            'Sec-Fetch-Mode': 'no-cors'
                        };
                        for (const url of bingUrls) {
                            try {
                                this.logger.log(`尝试Bing音频URL: ${url}`);
                                const response = await superagent
                                    .get(url)
                                    .set(requestHeaders)
                                    .buffer(true)
                                    .parse((res, callback) => {
                                    const chunks = [];
                                    res.on('data', (chunk) => chunks.push(chunk));
                                    res.on('end', () => {
                                        const buffer = Buffer.concat(chunks);
                                        const contentType = res.headers['content-type'] || '';
                                        this.logger.debug(`Bing响应头: ${JSON.stringify(res.headers)}`);
                                        this.logger.debug(`Bing响应内容长度: ${buffer.length} 字节`);
                                        const isAudioContent = contentType.includes('audio/') ||
                                            buffer.length > 1024 &&
                                                ((buffer[0] === 0xFF && (buffer[1] & 0xF0) === 0xF0) ||
                                                    (buffer[0] === 0x49 && buffer[1] === 0x44 && buffer[2] === 0x33));
                                        if (isAudioContent) {
                                            this.logger.log(`成功识别音频内容，大小: ${buffer.length} 字节`);
                                            return callback(null, buffer);
                                        }
                                        const isHtmlContent = contentType.includes('text/html') &&
                                            buffer.toString().includes('<!DOCTYPE html>');
                                        if (isHtmlContent) {
                                            this.logger.warn(`Bing音频请求返回HTML内容，尝试下一个URL`);
                                            return callback(new Error('Bing音频请求返回HTML内容'), null);
                                        }
                                        this.logger.warn(`未找到音频内容，内容类型: ${contentType}，大小: ${buffer.length} 字节`);
                                        return callback(new Error('未找到音频内容'), null);
                                    });
                                })
                                    .timeout({
                                    response: 5000,
                                    deadline: 10000
                                });
                                if (response.body && response.body.length > 0) {
                                    this.logger.log(`成功获取Bing音频，大小: ${response.body.length} 字节`);
                                    return response.body;
                                }
                            }
                            catch (error) {
                                this.logger.warn(`Bing音频获取失败 (URL: ${url}): ${error.message}`);
                                if (url === bingUrls[bingUrls.length - 1]) {
                                    throw new common_1.HttpException(`无法从Bing获取音频: ${error.message}`, common_1.HttpStatus.BAD_GATEWAY);
                                }
                                continue;
                            }
                        }
                        throw new common_1.HttpException('无法从Bing获取音频：所有尝试的URL均失败', common_1.HttpStatus.BAD_GATEWAY);
                        break;
                    case 'baidu':
                        const baiduType = type === '1' ? 'uk' : 'en';
                        url = `https://fanyi.baidu.com/gettts?lan=en&text=${encodeURIComponent(word)}&spd=3&source=web&format=mp3&from=FanyiBrowserDownloadClicking&browser=chrome&product=pc&req=dict&accord=${baiduType}`;
                        break;
                }
                this.logger.log(`尝试获取音频URL: ${url} (提供商: ${currentProvider})`);
                try {
                    const response = await superagent
                        .get(url)
                        .set('Accept', 'audio/mpeg,audio/*;q=0.9,*/*;q=0.8')
                        .set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36')
                        .buffer(true)
                        .timeout({
                        response: 5000,
                        deadline: 10000
                    });
                    const respHeaders = response.headers;
                    this.logger.debug(`获取到响应头: ${JSON.stringify(respHeaders)}`);
                    this.logger.debug(`获取到音频内容长度: ${response.body ? response.body.length : '未知'}`);
                    let contentType = 'audio/mpeg';
                    res.setHeader('Content-Type', contentType);
                    res.setHeader('Access-Control-Allow-Origin', '*');
                    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
                    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Range');
                    res.setHeader('Accept-Ranges', 'bytes');
                    res.setHeader('Cache-Control', 'public, max-age=86400');
                    if (respHeaders['content-length']) {
                        res.setHeader('Content-Length', respHeaders['content-length']);
                    }
                    else if (response.body) {
                        res.setHeader('Content-Length', response.body.length);
                    }
                    res.setHeader('X-Audio-Provider', currentProvider);
                    res.setHeader('X-Audio-Word', word);
                    res.setHeader('X-Audio-Type', type);
                    res.setHeader('X-Content-Type-Options', 'nosniff');
                    this.logger.log(`发送音频数据，提供商: ${currentProvider}，Content-Type: ${contentType}，长度: ${response.body ? response.body.length : '未知'} 字节`);
                    return res.send(response.body);
                }
                catch (error) {
                    this.logger.error(`获取音频失败 (${currentProvider}): ${error.message}`);
                    if (currentProviderIndex === audioProviders.length - 1 || strict === 'true') {
                        throw new common_1.HttpException(`无法从 ${currentProvider} 获取 "${word}" 的音频: ${error.message}`, common_1.HttpStatus.BAD_GATEWAY);
                    }
                    currentProviderIndex++;
                }
            }
            catch (error) {
                this.logger.error(`处理音频请求出错 (${currentProvider}): ${error.message}`);
                if (currentProviderIndex === audioProviders.length - 1 || strict === 'true') {
                    throw new common_1.HttpException(`获取音频失败: ${error.message}`, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                }
                currentProviderIndex++;
            }
        }
        throw new common_1.HttpException(`所有音频提供商均无法获取 "${word}" 的音频`, common_1.HttpStatus.BAD_GATEWAY);
    }
};
exports.AudioProxyController = AudioProxyController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AudioProxyController.prototype, "testAudioProxy", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('word')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('provider')),
    __param(3, (0, common_1.Query)('strict')),
    __param(4, (0, common_1.Headers)()),
    __param(5, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], AudioProxyController.prototype, "getAudio", null);
exports.AudioProxyController = AudioProxyController = AudioProxyController_1 = __decorate([
    (0, common_1.Controller)('audio-proxy')
], AudioProxyController);
//# sourceMappingURL=audio-proxy.controller.js.map