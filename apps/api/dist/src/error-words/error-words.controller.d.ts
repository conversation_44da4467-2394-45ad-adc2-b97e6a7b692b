import { ErrorWordsService } from './error-words.service';
import { UserEntity } from '../user/user.controller';
export declare class ErrorWordsController {
    private readonly errorWordsService;
    private readonly logger;
    constructor(errorWordsService: ErrorWordsService);
    getErrorWords(user: UserEntity): Promise<any[]>;
    saveErrorWords(user: UserEntity, data: {
        words: any[];
    }): Promise<{
        success: boolean;
    }>;
}
