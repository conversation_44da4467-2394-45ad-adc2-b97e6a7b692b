"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ErrorWordsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorWordsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ErrorWordsService = ErrorWordsService_1 = class ErrorWordsService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(ErrorWordsService_1.name);
    }
    async getErrorWords(userId) {
        this.logger.log(`获取用户错词表: userId=${userId}`);
        try {
            const userErrorWords = await this.prisma.userErrorWords.findUnique({
                where: { userId }
            });
            if (!userErrorWords) {
                this.logger.log(`用户错词表不存在，返回空数组: userId=${userId}`);
                return [];
            }
            if (typeof userErrorWords.words === 'string') {
                try {
                    return JSON.parse(userErrorWords.words);
                }
                catch (e) {
                    this.logger.error(`解析错词表JSON失败: ${e.message}`, e.stack);
                    return [];
                }
            }
            else if (Array.isArray(userErrorWords.words)) {
                return userErrorWords.words;
            }
            else {
                this.logger.warn(`错词表格式不正确: ${typeof userErrorWords.words}`);
                return [];
            }
        }
        catch (error) {
            this.logger.error(`获取用户错词表失败: userId=${userId}`, error.stack);
            throw new Error('获取错词表失败');
        }
    }
    async saveErrorWords(userId, words) {
        this.logger.log(`保存用户错词表: userId=${userId}, 词汇数量=${words.length}`);
        try {
            await this.prisma.userErrorWords.upsert({
                where: { userId },
                update: {
                    words,
                    updatedAt: new Date()
                },
                create: {
                    userId,
                    words
                }
            });
            this.logger.log(`用户错词表保存成功: userId=${userId}`);
        }
        catch (error) {
            this.logger.error(`保存用户错词表失败: userId=${userId}`, error.stack);
            throw new Error('保存错词表失败');
        }
    }
};
exports.ErrorWordsService = ErrorWordsService;
exports.ErrorWordsService = ErrorWordsService = ErrorWordsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ErrorWordsService);
//# sourceMappingURL=error-words.service.js.map