"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorWordsModule = void 0;
const common_1 = require("@nestjs/common");
const error_words_controller_1 = require("./error-words.controller");
const error_words_service_1 = require("./error-words.service");
const prisma_service_1 = require("../prisma/prisma.service");
const auth_common_module_1 = require("../auth/auth.common.module");
let ErrorWordsModule = class ErrorWordsModule {
};
exports.ErrorWordsModule = ErrorWordsModule;
exports.ErrorWordsModule = ErrorWordsModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_common_module_1.AuthCommonModule],
        controllers: [error_words_controller_1.ErrorWordsController],
        providers: [error_words_service_1.ErrorWordsService, prisma_service_1.PrismaService],
        exports: [error_words_service_1.ErrorWordsService]
    })
], ErrorWordsModule);
//# sourceMappingURL=error-words.module.js.map