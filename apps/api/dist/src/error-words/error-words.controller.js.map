{"version": 3, "file": "error-words.controller.js", "sourceRoot": "", "sources": ["../../../src/error-words/error-words.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,qDAAiD;AACjD,6DAA+C;AAC/C,+DAA0D;AAInD,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YAA6B,iBAAoC;QAApC,sBAAiB,GAAjB,iBAAiB,CAAmB;QAFhD,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAEI,CAAC;IAI/D,AAAN,KAAK,CAAC,aAAa,CAAS,IAAgB;QAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,MAAM,QAAQ,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;YAC3E,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CAAS,IAAgB,EAAU,IAAsB;QAC3E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;QACnF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAClD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAhCY,oDAAoB;AAOzB;IAFL,IAAA,kBAAS,EAAC,sBAAS,CAAC;IACpB,IAAA,YAAG,GAAE;IACe,WAAA,IAAA,sBAAI,GAAE,CAAA;;;;yDAU1B;AAIK;IAFL,IAAA,kBAAS,EAAC,sBAAS,CAAC;IACpB,IAAA,aAAI,GAAE;IACe,WAAA,IAAA,sBAAI,GAAE,CAAA;IAAoB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;0DAUrD;+BA/BU,oBAAoB;IADhC,IAAA,mBAAU,EAAC,aAAa,CAAC;qCAIwB,uCAAiB;GAHtD,oBAAoB,CAgChC"}