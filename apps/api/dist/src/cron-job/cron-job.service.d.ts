import { RankService } from "../rank/rank.service";
export declare const TIME_ZONE = "Asia/Shanghai";
export declare class CronJobService {
    private readonly rankService;
    private static readonly EVERY_MONDAY_AT_2AM;
    private static readonly EVERY_FIRST_DAY_OF_MONTH_AT_2AM;
    private static readonly EVERY_FIRST_DAY_OF_YEAR_AT_2AM;
    constructor(rankService: RankService);
    resetRankListWeekly(): Promise<void>;
    resetRankListMonthly(): Promise<void>;
    resetRankListYearly(): Promise<void>;
}
