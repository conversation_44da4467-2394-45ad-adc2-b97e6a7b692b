"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronJobService = exports.TIME_ZONE = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const rank_service_1 = require("../rank/rank.service");
exports.TIME_ZONE = "Asia/Shanghai";
let CronJobService = class CronJobService {
    constructor(rankService) {
        this.rankService = rankService;
    }
    async resetRankListWeekly() {
        this.rankService.resetRankList(rank_service_1.RankPeriod.WEEKLY);
    }
    async resetRankListMonthly() {
        this.rankService.resetRankList(rank_service_1.RankPeriod.MONTHLY);
    }
    async resetRankListYearly() {
        this.rankService.resetRankList(rank_service_1.RankPeriod.YEARLY);
    }
};
exports.CronJobService = CronJobService;
CronJobService.EVERY_MONDAY_AT_2AM = "0 2 * * 1";
CronJobService.EVERY_FIRST_DAY_OF_MONTH_AT_2AM = "0 2 1 * *";
CronJobService.EVERY_FIRST_DAY_OF_YEAR_AT_2AM = "0 2 1 1 *";
__decorate([
    (0, schedule_1.Cron)(CronJobService.EVERY_MONDAY_AT_2AM, { timeZone: exports.TIME_ZONE }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronJobService.prototype, "resetRankListWeekly", null);
__decorate([
    (0, schedule_1.Cron)(CronJobService.EVERY_FIRST_DAY_OF_MONTH_AT_2AM, { timeZone: exports.TIME_ZONE }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronJobService.prototype, "resetRankListMonthly", null);
__decorate([
    (0, schedule_1.Cron)(CronJobService.EVERY_FIRST_DAY_OF_YEAR_AT_2AM, { timeZone: exports.TIME_ZONE }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronJobService.prototype, "resetRankListYearly", null);
exports.CronJobService = CronJobService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [rank_service_1.RankService])
], CronJobService);
//# sourceMappingURL=cron-job.service.js.map