"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSettingsResponseDto = exports.SaveSettingsDto = exports.UpdatePasswordDto = exports.UpdateUserDto = exports.FindUserDto = exports.CreateUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateUserDto {
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "admin",
        description: "用户名不能为空,长度为2-20位",
    }),
    (0, class_validator_1.IsNotEmpty)({ message: "用户名不能为空" }),
    (0, class_validator_1.Length)(2, 20, { message: "用户名长度为2-20位" }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "<EMAIL>",
        description: "邮箱地址",
    }),
    (0, class_validator_1.IsEmail)({}, { message: "请输入有效的邮箱地址" }),
    (0, class_validator_1.IsNotEmpty)({ message: "邮箱不能为空" }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "15512345678",
        description: "手机号码不能为空,长度应在6-20位之间",
    }),
    (0, class_validator_1.IsNotEmpty)({ message: "手机号码不能为空" }),
    (0, class_validator_1.Length)(6, 20, { message: "手机号码长度应在6到20位之间" }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "123456",
        description: "密码不能为空,长度应在6-20位之间",
    }),
    (0, class_validator_1.IsNotEmpty)({ message: "密码不能为空" }),
    (0, class_validator_1.Length)(6, 20, { message: "密码长度为6-20位" }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "avatar.jpg",
        description: "用户头像",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: true,
        description: "是否为新用户",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "isNewUser", void 0);
class FindUserDto {
}
exports.FindUserDto = FindUserDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)({ message: "邮箱不能为空" }),
    (0, class_validator_1.IsString)({ message: "邮箱必须是字符串" }),
    __metadata("design:type", String)
], FindUserDto.prototype, "email", void 0);
class UpdateUserDto {
}
exports.UpdateUserDto = UpdateUserDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "avatar", void 0);
class UpdatePasswordDto {
}
exports.UpdatePasswordDto = UpdatePasswordDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], UpdatePasswordDto.prototype, "oldPassword", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], UpdatePasswordDto.prototype, "newPassword", void 0);
class SaveSettingsDto {
}
exports.SaveSettingsDto = SaveSettingsDto;
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", String)
], SaveSettingsDto.prototype, "settings", void 0);
class GetSettingsResponseDto {
}
exports.GetSettingsResponseDto = GetSettingsResponseDto;
//# sourceMappingURL=user.dto.js.map