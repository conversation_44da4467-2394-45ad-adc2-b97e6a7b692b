export declare class CreateUserDto {
    username: string;
    email: string;
    phone: string;
    password: string;
    avatar?: string;
    isNewUser?: boolean;
}
export declare class FindUserDto {
    email: string;
}
export declare class UpdateUserDto {
    username?: string;
    email?: string;
    avatar?: string;
}
export declare class UpdatePasswordDto {
    oldPassword: string;
    newPassword: string;
}
export declare class SaveSettingsDto {
    settings: string;
}
export declare class GetSettingsResponseDto {
    id: string;
    userId: string;
    settings: Record<string, any>;
    updatedAt: Date;
}
