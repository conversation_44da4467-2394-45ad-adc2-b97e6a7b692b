import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
import { ConfigService } from '@nestjs/config';
import { UpdateUserDto, UpdatePasswordDto } from "./model/user.dto";
interface UpdateUserProfileParams {
    username?: string;
    email?: string;
    avatar?: string;
    isNewUser?: boolean;
}
export declare class UserService {
    private readonly prisma;
    private readonly configService;
    private readonly logger;
    private readonly MAX_AVATAR_SIZE;
    private readonly allowedMimeTypes;
    constructor(prisma: PrismaService, configService: ConfigService);
    findById(id: string): Promise<User | null>;
    findUser(id: string): Promise<User | null>;
    findCurrentUser(id: string): Promise<User | undefined>;
    updateUser(userId: string, dto: UpdateUserDto): Promise<User>;
    updateProfile(userId: string, params: UpdateUserProfileParams): Promise<User>;
    private validateImageFile;
    uploadAvatar(userId: string, file: Express.Multer.File): Promise<string>;
    updateAvatar(id: string, avatar: string): Promise<User>;
    setupNewUser(id: string): Promise<User>;
    updatePassword(userId: string, dto: UpdatePasswordDto): Promise<{
        success: boolean;
        message: string;
    }>;
    saveUserSettings(userId: string, settingsData: string): Promise<any>;
    getUserSettings(userId: string): Promise<any>;
    deleteUserSettings(userId: string): Promise<boolean>;
}
export {};
