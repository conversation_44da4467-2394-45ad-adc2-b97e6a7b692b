import { type MembershipDetails } from "../../membership/types/membership.types";
export interface UserInfo {
    id: string;
    username: string;
    email?: string;
    name?: string;
    avatar?: string;
    createdAt: Date;
    updatedAt: Date;
    profile?: {
        nickname?: string;
        website?: string;
        bio?: string;
        location?: string;
    };
    permissions?: string[];
    isSuspended?: boolean;
    membership?: {
        isMember: boolean;
        details: MembershipDetails | null;
    };
}
