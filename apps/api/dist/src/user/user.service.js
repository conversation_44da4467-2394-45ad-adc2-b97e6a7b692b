"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const config_1 = require("@nestjs/config");
const bcryptjs_1 = require("bcryptjs");
let UserService = UserService_1 = class UserService {
    constructor(prisma, configService) {
        this.prisma = prisma;
        this.configService = configService;
        this.logger = new common_1.Logger(UserService_1.name);
        this.MAX_AVATAR_SIZE = 200 * 1024;
        this.allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
    }
    async findById(id) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id },
            });
            if (!user) {
                this.logger.warn(`用户不存在: ${id}`);
                return null;
            }
            return user;
        }
        catch (error) {
            this.logger.error('查找用户失败:', error);
            return null;
        }
    }
    async findUser(id) {
        return this.findById(id);
    }
    async findCurrentUser(id) {
        try {
            const user = await this.findById(id);
            if (!user)
                return undefined;
            return user;
        }
        catch (error) {
            this.logger.error("获取当前用户信息失败:", error);
            return undefined;
        }
    }
    async updateUser(userId, dto) {
        return this.updateProfile(userId, {
            username: dto.username,
            email: dto.email,
            avatar: dto.avatar,
        });
    }
    async updateProfile(userId, params) {
        try {
            const user = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    username: params.username,
                    email: params.email,
                    avatar: params.avatar,
                    isNewUser: params.isNewUser,
                },
            });
            return user;
        }
        catch (error) {
            this.logger.error('更新用户资料失败:', error);
            throw new Error('更新用户资料失败');
        }
    }
    validateImageFile(file) {
        if (!this.allowedMimeTypes.includes(file.mimetype)) {
            throw new common_1.BadRequestException('不支持的文件类型，仅支持 JPEG、PNG 和 GIF 格式');
        }
        if (file.size > this.MAX_AVATAR_SIZE) {
            throw new common_1.BadRequestException(`文件大小不能超过 ${this.MAX_AVATAR_SIZE / 1024}KB`);
        }
        if (!file.buffer || file.buffer.length === 0) {
            throw new common_1.BadRequestException('文件内容为空');
        }
    }
    async uploadAvatar(userId, file) {
        try {
            this.logger.log('开始处理头像上传:', {
                userId,
                fileName: file.originalname,
                mimetype: file.mimetype,
                size: file.size
            });
            this.validateImageFile(file);
            const base64Data = file.buffer.toString('base64');
            const avatarData = `data:${file.mimetype};base64,${base64Data}`;
            this.logger.log('头像转换为Base64成功:', {
                userId,
                mimetype: file.mimetype,
                dataLength: avatarData.length
            });
            await this.updateProfile(userId, { avatar: avatarData });
            this.logger.log('头像上传成功:', {
                userId,
                avatarDataLength: avatarData.length
            });
            return avatarData;
        }
        catch (error) {
            this.logger.error('上传头像失败:', {
                userId,
                error: error.message,
                stack: error.stack
            });
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('头像上传失败，请重试');
        }
    }
    async updateAvatar(id, avatar) {
        return this.prisma.user.update({
            where: { id },
            data: { avatar },
        });
    }
    async setupNewUser(id) {
        return this.prisma.user.update({
            where: { id },
            data: { isNewUser: false },
        });
    }
    async updatePassword(userId, dto) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const isPasswordValid = await (0, bcryptjs_1.compare)(dto.oldPassword, user.hashedPassword);
            if (!isPasswordValid) {
                throw new common_1.UnauthorizedException('当前密码不正确');
            }
            const hashedPassword = await (0, bcryptjs_1.hash)(dto.newPassword, 10);
            await this.prisma.user.update({
                where: { id: userId },
                data: { hashedPassword: hashedPassword },
            });
            this.logger.log('密码修改成功:', { userId });
            return {
                success: true,
                message: '密码修改成功'
            };
        }
        catch (error) {
            this.logger.error('修改密码失败:', error);
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new Error('修改密码失败');
        }
    }
    async saveUserSettings(userId, settingsData) {
        try {
            this.logger.log(`保存用户设置: ${userId}`);
            this.logger.log(`接收到的设置数据: ${settingsData}`);
            let settings;
            try {
                settings = JSON.parse(settingsData);
                this.logger.log(`成功解析设置数据: ${JSON.stringify(settings).substring(0, 100)}...`);
            }
            catch (e) {
                this.logger.error(`解析设置JSON失败: ${e.message}`, e.stack);
                this.logger.error(`原始设置数据: ${settingsData}`);
                throw new common_1.BadRequestException('无效的JSON格式');
            }
            if (!settings || typeof settings !== 'object') {
                this.logger.error(`设置不是有效的对象: ${typeof settings}`);
                throw new common_1.BadRequestException('设置必须是有效的对象');
            }
            try {
                const result = await this.prisma.userSettings.upsert({
                    where: { userId },
                    update: {
                        settings,
                        updatedAt: new Date(),
                    },
                    create: {
                        userId,
                        settings,
                    },
                });
                this.logger.log(`用户设置已保存: ${userId}, 设置ID: ${result.id}`);
                return {
                    userId,
                    settings: result.settings || {},
                };
            }
            catch (dbError) {
                this.logger.error(`数据库操作失败: ${dbError.message}`, dbError.stack);
                throw new common_1.BadRequestException('保存设置到数据库失败');
            }
        }
        catch (error) {
            this.logger.error(`保存用户设置失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`保存用户设置失败: ${error.message}`);
        }
    }
    async getUserSettings(userId) {
        try {
            this.logger.log(`获取用户设置: ${userId}`);
            const userSettings = await this.prisma.userSettings.findUnique({
                where: { userId },
            });
            if (!userSettings) {
                this.logger.log(`用户设置不存在，创建默认设置: ${userId}`);
                const defaultSettings = await this.prisma.userSettings.create({
                    data: {
                        userId,
                        settings: {},
                    },
                });
                return {
                    userId,
                    settings: defaultSettings.settings || {},
                };
            }
            return {
                userId,
                settings: userSettings.settings || {},
            };
        }
        catch (error) {
            this.logger.error('获取用户设置失败:', error);
            throw new common_1.BadRequestException('获取用户设置失败');
        }
    }
    async deleteUserSettings(userId) {
        try {
            this.logger.log(`删除用户设置: ${userId}`);
            await this.prisma.userSettings.delete({
                where: { userId },
            });
            this.logger.log(`用户设置已删除: ${userId}`);
            return true;
        }
        catch (error) {
            this.logger.error('删除用户设置失败:', error);
            if (error.code === 'P2025') {
                return true;
            }
            throw new common_1.BadRequestException('删除用户设置失败');
        }
    }
};
exports.UserService = UserService;
exports.UserService = UserService = UserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService])
], UserService);
//# sourceMappingURL=user.service.js.map