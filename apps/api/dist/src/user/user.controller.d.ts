import { UpdateUserDto, UpdatePasswordDto, SaveSettingsDto } from "./model/user.dto";
import { UserService } from "./user.service";
export interface UserEntity {
    userId: string;
}
export declare class UserController {
    private readonly userService;
    private readonly logger;
    constructor(userService: UserService);
    uploadAvatar(user: UserEntity, file: Express.Multer.File): Promise<{
        avatar: string;
    }>;
    updateInfo(user: UserEntity, dto: UpdateUserDto): Promise<{
        id: string;
        email: string;
        username: string | null;
        avatar: string | null;
        isActive: boolean;
        lastLogoutAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
        isNewUser: boolean;
        hashedPassword: string | null;
        role: string;
    }>;
    getCurrentUser(user: UserEntity): Promise<{
        id: string;
        email: string;
        username: string | null;
        avatar: string | null;
        isActive: boolean;
        lastLogoutAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
        isNewUser: boolean;
        hashedPassword: string | null;
        role: string;
    }>;
    initializeUser(user: UserEntity): Promise<{
        id: string;
        email: string;
        username: string | null;
        avatar: string | null;
        isActive: boolean;
        lastLogoutAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
        isNewUser: boolean;
        hashedPassword: string | null;
        role: string;
    }>;
    getLearningProgress(req: any): Promise<number>;
    getNewCourses(req: any): Promise<{
        count: number;
    }>;
    updatePassword(user: UserEntity, dto: UpdatePasswordDto): Promise<{
        success: boolean;
        message: string;
    }>;
    getUserSettings(user: UserEntity): Promise<any>;
    saveUserSettings(user: UserEntity, dto: SaveSettingsDto): Promise<any>;
    deleteUserSettings(user: UserEntity): Promise<{
        success: boolean;
    }>;
}
