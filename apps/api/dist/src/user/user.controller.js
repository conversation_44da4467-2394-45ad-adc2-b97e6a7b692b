"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const passport_1 = require("@nestjs/passport");
const common_2 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("./user.decorators");
const user_dto_1 = require("./model/user.dto");
const user_service_1 = require("./user.service");
let UserController = UserController_1 = class UserController {
    constructor(userService) {
        this.userService = userService;
        this.logger = new common_2.Logger(UserController_1.name);
    }
    async uploadAvatar(user, file) {
        try {
            if (!file) {
                this.logger.error('上传失败: 未收到文件');
                throw new Error('请选择要上传的文件');
            }
            this.logger.log('开始处理头像上传:', {
                userId: user.userId,
                fileInfo: {
                    fieldname: file.fieldname,
                    originalname: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size
                }
            });
            const avatarUrl = await this.userService.uploadAvatar(user.userId, file);
            this.logger.log('文件上传成功，URL:', avatarUrl);
            const updatedUser = await this.userService.updateAvatar(user.userId, avatarUrl);
            this.logger.log('用户头像信息更新成功:', {
                userId: user.userId,
                avatar: updatedUser.avatar
            });
            return { avatar: updatedUser.avatar };
        }
        catch (error) {
            this.logger.error('上传头像失败:', {
                error,
                userId: user.userId,
                fileInfo: file ? {
                    fieldname: file.fieldname,
                    originalname: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size
                } : 'No file'
            });
            throw new Error(error.message || '上传头像失败');
        }
    }
    async updateInfo(user, dto) {
        return this.userService.updateUser(user.userId, dto);
    }
    async getCurrentUser(user) {
        const userInfo = await this.userService.findCurrentUser(user.userId);
        return userInfo;
    }
    async initializeUser(user) {
        return this.userService.setupNewUser(user.userId);
    }
    async getLearningProgress(req) {
        return Math.floor(Math.random() * 100);
    }
    async getNewCourses(req) {
        return {
            count: 0
        };
    }
    async updatePassword(user, dto) {
        this.logger.log('修改密码请求:', { userId: user.userId });
        return this.userService.updatePassword(user.userId, dto);
    }
    async getUserSettings(user) {
        this.logger.log('获取用户设置:', { userId: user.userId });
        const settings = await this.userService.getUserSettings(user.userId);
        return settings;
    }
    async saveUserSettings(user, dto) {
        this.logger.log('保存用户设置:', { userId: user.userId });
        return this.userService.saveUserSettings(user.userId, dto.settings);
    }
    async deleteUserSettings(user) {
        this.logger.log('删除用户设置:', { userId: user.userId });
        return { success: await this.userService.deleteUserSettings(user.userId) };
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)('avatar'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('avatar')),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "uploadAvatar", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Patch)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateInfo", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getCurrentUser", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)("setup"),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "initializeUser", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, common_1.Get)('learning-progress'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getLearningProgress", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, common_1.Get)('new-courses'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getNewCourses", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Patch)('password'),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_dto_1.UpdatePasswordDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updatePassword", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)('settings'),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserSettings", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)('settings'),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_dto_1.SaveSettingsDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "saveUserSettings", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Delete)('settings'),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "deleteUserSettings", null);
exports.UserController = UserController = UserController_1 = __decorate([
    (0, common_1.Controller)("user"),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
//# sourceMappingURL=user.controller.js.map