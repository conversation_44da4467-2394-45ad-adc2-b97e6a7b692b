"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RankService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RankService = exports.RankPeriod = void 0;
const ioredis_1 = require("@nestjs-modules/ioredis");
const common_1 = require("@nestjs/common");
const ioredis_2 = require("ioredis");
const user_service_1 = require("../user/user.service");
var RankPeriod;
(function (RankPeriod) {
    RankPeriod["WEEKLY"] = "weekly";
    RankPeriod["MONTHLY"] = "monthly";
    RankPeriod["YEARLY"] = "yearly";
})(RankPeriod || (exports.RankPeriod = RankPeriod = {}));
let RankService = RankService_1 = class RankService {
    constructor(redis, userService) {
        this.redis = redis;
        this.userService = userService;
        this.FINISH_COUNT_KEY = `user:finishCount`;
        this.logger = new common_1.Logger(RankService_1.name);
        this.rankKeys = {
            [RankPeriod.WEEKLY]: `${this.FINISH_COUNT_KEY}`,
            [RankPeriod.MONTHLY]: `${this.FINISH_COUNT_KEY}:${RankPeriod.MONTHLY}Rank`,
            [RankPeriod.YEARLY]: `${this.FINISH_COUNT_KEY}:${RankPeriod.YEARLY}Rank`,
        };
    }
    async userFinishCourse(userId) {
        const counts = {};
        for (const period of Object.keys(this.rankKeys)) {
            const rankKey = this.rankKeys[period];
            let count = await this.redis.zscore(rankKey, userId);
            if (!count) {
                await this.redis.zadd(rankKey, 1, userId);
            }
            else {
                await this.redis.zincrby(rankKey, 1, userId);
            }
            count = await this.redis.zscore(rankKey, userId);
            counts[period] = count;
        }
        return counts;
    }
    convertRankListToObjectArray(rankList) {
        const res = [];
        for (let i = 0; i < rankList.length; i += 2) {
            const count = parseInt(rankList[i + 1] ?? "-1");
            res.push({ count, userId: rankList[i] });
        }
        return res;
    }
    async getRankList(user, period = RankPeriod.WEEKLY) {
        let self = null;
        const rankPeriod = this.rankKeys[period];
        const rankList = this.convertRankListToObjectArray(await this.redis.zrevrange(rankPeriod, 0, 24, "WITHSCORES"));
        if (user) {
            const userRank = await this.redis.zrevrank(rankPeriod, user.userId);
            const userCount = await this.redis.zscore(rankPeriod, user.userId);
            self = {
                userId: user.userId,
                count: userCount === null ? -1 : parseInt(userCount),
                rank: userRank === null ? -1 : userRank + 1,
            };
        }
        await this.appendUserNameProperty(self, rankList);
        return {
            self,
            list: rankList,
        };
    }
    async appendUserNameProperty(self, rankList) {
        const usersMap = await this.fetchUsersMap(Array.from(new Set([self.userId, ...rankList.map(({ userId }) => userId)])));
        const rankListUsernameGenByUserId = (id) => {
            const user = usersMap[id];
            if (!user) {
                return "";
            }
            return user.username;
        };
        self.username = rankListUsernameGenByUserId(self.userId);
        rankList.forEach((info) => {
            info.username = rankListUsernameGenByUserId(info.userId);
        });
    }
    async fetchUsersMap(uIds) {
        const promises = uIds.map((uId) => {
            return this.userService.findUser(uId);
        });
        const users = await Promise.all(promises);
        return users.reduce((obj, cur) => {
            if (cur) {
                obj[cur.id] = cur;
            }
            return obj;
        }, {});
    }
    async resetRankList(period = RankPeriod.WEEKLY) {
        const rankKey = this.rankKeys[period];
        try {
            await this.redis.del(rankKey);
            this.logger.verbose(`${period}重置排行榜成功: ${new Date()}`);
        }
        catch (error) {
            this.logger.error(`${period}重置排行榜时发生错误: ${error}`);
        }
    }
    async getUserInfo(uId) {
        try {
            return this.userService.findUser(uId);
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }
};
exports.RankService = RankService;
exports.RankService = RankService = RankService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, ioredis_1.InjectRedis)()),
    __metadata("design:paramtypes", [ioredis_2.default,
        user_service_1.UserService])
], RankService);
//# sourceMappingURL=rank.service.js.map