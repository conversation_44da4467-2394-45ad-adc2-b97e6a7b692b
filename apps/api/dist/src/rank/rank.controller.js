"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RankController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const rank_service_1 = require("./rank.service");
let RankController = class RankController {
    constructor(rankService) {
        this.rankService = rankService;
    }
    getRankList(user, period = rank_service_1.RankPeriod.WEEKLY) {
        return this.rankService.getRankList(user, period);
    }
};
exports.RankController = RankController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: "获取排行榜信息",
    }),
    (0, auth_guard_1.UncheckAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)("progress/:period"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("period")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], RankController.prototype, "getRankList", null);
exports.RankController = RankController = __decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiTags)("Rank"),
    (0, common_1.Controller)("rank"),
    __metadata("design:paramtypes", [rank_service_1.RankService])
], RankController);
//# sourceMappingURL=rank.controller.js.map