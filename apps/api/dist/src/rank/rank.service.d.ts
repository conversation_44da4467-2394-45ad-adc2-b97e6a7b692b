import Redis from "ioredis";
import { UserEntity } from "../user/user.decorators";
import { UserService } from "../user/user.service";
export declare enum RankPeriod {
    WEEKLY = "weekly",
    MONTHLY = "monthly",
    YEARLY = "yearly"
}
export type RankPeriodAlias = "weekly" | "monthly" | "yearly";
export declare class RankService {
    private readonly redis;
    private readonly userService;
    private readonly FINISH_COUNT_KEY;
    private readonly logger;
    private readonly rankKeys;
    constructor(redis: Redis, userService: UserService);
    userFinishCourse(userId: string): Promise<{}>;
    private convertRankListToObjectArray;
    getRankList(user: UserEntity, period?: RankPeriodAlias): Promise<{
        self: any;
        list: any[];
    }>;
    private appendUserNameProperty;
    private fetchUsersMap;
    resetRankList(period?: RankPeriodAlias): Promise<void>;
    getUserInfo(uId: string): Promise<{
        id: string;
        email: string;
        username: string | null;
        avatar: string | null;
        isActive: boolean;
        lastLogoutAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
        isNewUser: boolean;
        hashedPassword: string | null;
        role: string;
    }>;
}
