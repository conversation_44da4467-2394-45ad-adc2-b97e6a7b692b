"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const bcryptjs_1 = require("bcryptjs");
const prisma_service_1 = require("../prisma/prisma.service");
let UsersService = UsersService_1 = class UsersService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(UsersService_1.name);
    }
    async findByEmail(email) {
        try {
            return await this.prisma.user.findUnique({
                where: { email },
            });
        }
        catch (error) {
            this.logger.error('通过邮箱查找用户失败:', error);
            return null;
        }
    }
    async findByUsername(username) {
        try {
            return await this.prisma.user.findFirst({
                where: { username },
            });
        }
        catch (error) {
            this.logger.error('通过用户名查找用户失败:', error);
            return null;
        }
    }
    async findByEmailOrUsername(account) {
        try {
            return await this.prisma.user.findFirst({
                where: {
                    OR: [
                        { email: account },
                        { username: account }
                    ]
                },
            });
        }
        catch (error) {
            this.logger.error('通过邮箱或用户名查找用户失败:', error);
            return null;
        }
    }
    async findById(id) {
        try {
            return await this.prisma.user.findUnique({
                where: { id },
            });
        }
        catch (error) {
            this.logger.error('通过ID查找用户失败:', error);
            return null;
        }
    }
    async createUser(params) {
        try {
            const hashedPassword = await (0, bcryptjs_1.hash)(params.password, 10);
            return await this.prisma.user.create({
                data: {
                    email: params.email,
                    username: params.username,
                    hashedPassword: hashedPassword,
                    avatar: params.avatar,
                    isNewUser: params.isNewUser ?? true,
                },
            });
        }
        catch (error) {
            this.logger.error('创建用户失败:', error);
            throw error;
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map