import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
interface CreateUserParams {
    email: string;
    username: string;
    password: string;
    avatar?: string;
    isNewUser?: boolean;
}
export declare class UsersService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findByEmail(email: string): Promise<User | null>;
    findByUsername(username: string): Promise<User | null>;
    findByEmailOrUsername(account: string): Promise<User | null>;
    findById(id: string): Promise<User | null>;
    createUser(params: CreateUserParams): Promise<User>;
}
export {};
