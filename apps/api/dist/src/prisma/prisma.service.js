"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrismaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let PrismaService = PrismaService_1 = class PrismaService extends client_1.PrismaClient {
    constructor() {
        super({
            log: [
                { emit: 'stdout', level: 'query' },
                { emit: 'stdout', level: 'info' },
                { emit: 'stdout', level: 'warn' },
                { emit: 'stdout', level: 'error' },
            ],
            datasources: {
                db: {
                    url: process.env.DATABASE_URL,
                },
            },
        });
        this.logger = new common_1.Logger(PrismaService_1.name);
        this.isConnected = false;
        this.reconnectTimer = null;
        const prismaExtended = this;
        prismaExtended.$on('query', (e) => {
            if (e.duration > 2000) {
                this.logger.warn(`慢查询: ${e.query}`, 'PrismaSlowQuery');
            }
        });
        prismaExtended.$on('error', (e) => {
            this.logger.error(`Prisma错误: ${e.message}`, e.target || '');
            if (!this.isConnected) {
                this.scheduleReconnect();
            }
        });
    }
    async onModuleInit() {
        try {
            this.logger.log('正在连接数据库...');
            await this.$connect();
            this.isConnected = true;
            this.logger.log('数据库连接成功');
            const dbUrl = process.env.DATABASE_URL || '';
            if (dbUrl.includes('postgresql') || dbUrl.includes('postgres')) {
                try {
                    await this.$executeRaw `SET SESSION idle_in_transaction_session_timeout = 60000`;
                    await this.$executeRaw `SET statement_timeout = 30000`;
                    this.logger.log('数据库连接参数配置完成');
                }
                catch (err) {
                    this.logger.warn('配置数据库连接参数失败: ' + err.message);
                }
            }
        }
        catch (error) {
            this.isConnected = false;
            this.logger.error(`数据库连接失败: ${error.message}`, error.stack);
            this.scheduleReconnect();
        }
    }
    async onModuleDestroy() {
        this.logger.log('断开数据库连接');
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.isConnected = false;
        await this.$disconnect();
    }
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        this.reconnectTimer = setTimeout(async () => {
            this.logger.log('尝试重新连接数据库...');
            try {
                await this.$connect();
                this.isConnected = true;
                this.logger.log('数据库重连成功');
            }
            catch (error) {
                this.isConnected = false;
                this.logger.error(`数据库重连失败: ${error.message}`);
                this.scheduleReconnect();
            }
        }, 5000);
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = PrismaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map