"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentDateInChina = getCurrentDateInChina;
exports.getCurrentDateObjectInChina = getCurrentDateObjectInChina;
exports.dateStringToUTC = dateStringToUTC;
exports.dateToChineseString = dateToChineseString;
exports.getPreviousDayInChina = getPreviousDayInChina;
exports.getWeekStartInChina = getWeekStartInChina;
function getCurrentDateInChina() {
    const now = new Date();
    const chinaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const year = chinaTime.getUTCFullYear();
    const month = String(chinaTime.getUTCMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
function getCurrentDateObjectInChina() {
    const dateStr = getCurrentDateInChina();
    return new Date(`${dateStr}T00:00:00.000Z`);
}
function dateStringToUTC(dateStr) {
    return new Date(`${dateStr}T00:00:00.000Z`);
}
function dateToChineseString(date) {
    const chinaTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
    const year = chinaTime.getUTCFullYear();
    const month = String(chinaTime.getUTCMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
function getPreviousDayInChina(date) {
    const previousDay = new Date(date);
    previousDay.setDate(previousDay.getDate() - 1);
    return previousDay;
}
function getWeekStartInChina(today) {
    const weekStart = new Date(today);
    const dayOfWeek = weekStart.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    weekStart.setDate(weekStart.getDate() - daysToMonday);
    return weekStart;
}
//# sourceMappingURL=timezone.js.map