import { UserLearnRecordService } from "./user-learn-record.service";
export declare class UserLearnRecordController {
    private readonly userLearnRecordService;
    constructor(userLearnRecordService: UserLearnRecordService);
    upsertRecord(user: {
        userId: string;
    }, count: number): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        count: number;
        day: Date;
    }>;
    getDailyRecord(user: {
        userId: string;
    }, date?: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        count: number;
        day: Date;
    }>;
    getTotalCount(user: {
        userId: string;
    }): Promise<number>;
}
