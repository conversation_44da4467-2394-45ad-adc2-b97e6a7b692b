"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLearnRecordController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const user_learn_record_service_1 = require("./user-learn-record.service");
let UserLearnRecordController = class UserLearnRecordController {
    constructor(userLearnRecordService) {
        this.userLearnRecordService = userLearnRecordService;
    }
    async upsertRecord(user, count) {
        return this.userLearnRecordService.upsertRecord(user.userId, count, new Date());
    }
    async getDailyRecord(user, date) {
        return this.userLearnRecordService.getDailyRecord(user.userId, date ? new Date(date) : new Date());
    }
    async getTotalCount(user) {
        return this.userLearnRecordService.getTotalCount(user.userId);
    }
};
exports.UserLearnRecordController = UserLearnRecordController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)("count")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], UserLearnRecordController.prototype, "upsertRecord", null);
__decorate([
    (0, common_1.Get)("daily"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Query)("date")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UserLearnRecordController.prototype, "getDailyRecord", null);
__decorate([
    (0, common_1.Get)("total"),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserLearnRecordController.prototype, "getTotalCount", null);
exports.UserLearnRecordController = UserLearnRecordController = __decorate([
    (0, common_1.Controller)("user-learn-record"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [user_learn_record_service_1.UserLearnRecordService])
], UserLearnRecordController);
//# sourceMappingURL=user-learn-record.controller.js.map