"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLearnRecordModule = void 0;
const common_1 = require("@nestjs/common");
const auth_common_module_1 = require("../auth/auth.common.module");
const prisma_module_1 = require("../prisma/prisma.module");
const user_learn_record_controller_1 = require("./user-learn-record.controller");
const user_learn_record_service_1 = require("./user-learn-record.service");
let UserLearnRecordModule = class UserLearnRecordModule {
};
exports.UserLearnRecordModule = UserLearnRecordModule;
exports.UserLearnRecordModule = UserLearnRecordModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_common_module_1.AuthCommonModule, prisma_module_1.PrismaModule],
        controllers: [user_learn_record_controller_1.UserLearnRecordController],
        providers: [user_learn_record_service_1.UserLearnRecordService],
        exports: [user_learn_record_service_1.UserLearnRecordService],
    })
], UserLearnRecordModule);
//# sourceMappingURL=user-learn-record.module.js.map