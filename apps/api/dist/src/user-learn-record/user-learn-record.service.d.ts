import { PrismaService } from "../prisma/prisma.service";
export declare class UserLearnRecordService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    upsertRecord(userId: string, count: number, day: Date): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        count: number;
        day: Date;
    }>;
    getDailyRecord(userId: string, day: Date): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        count: number;
        day: Date;
    }>;
    getTotalCount(userId: string): Promise<number>;
}
