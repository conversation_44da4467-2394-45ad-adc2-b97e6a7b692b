"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProgressController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const user_progress_dto_1 = require("./model/user-progress.dto");
const user_course_progress_service_1 = require("./user-course-progress.service");
let UserProgressController = class UserProgressController {
    constructor(userCourseProgressService) {
        this.userCourseProgressService = userCourseProgressService;
    }
    async getUserRecentCoursePacks(user, limit) {
        return this.userCourseProgressService.getUserRecentCoursePacks(user.userId);
    }
    async upsert(user, dto) {
        const result = await this.userCourseProgressService.upsert(user.userId, dto.coursePackId, dto.courseId, dto.statementIndex);
        return result;
    }
};
exports.UserProgressController = UserProgressController;
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)("recent"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Query)("limit")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UserProgressController.prototype, "getUserRecentCoursePacks", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Put)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_progress_dto_1.UpsertUserProgressDto]),
    __metadata("design:returntype", Promise)
], UserProgressController.prototype, "upsert", null);
exports.UserProgressController = UserProgressController = __decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiTags)("UserProgress"),
    (0, common_1.Controller)("user-course-progress"),
    __metadata("design:paramtypes", [user_course_progress_service_1.UserCourseProgressService])
], UserProgressController);
//# sourceMappingURL=user-course-progress.controller.js.map