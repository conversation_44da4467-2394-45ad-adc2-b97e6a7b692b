import { PrismaService } from '../prisma/prisma.service';
import { CoursePack, UserCourseProgress } from '@prisma/client';
export declare class UserCourseProgressService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    findStatement(userId: string, coursePackId: string, courseId: string): Promise<number>;
    findRecentCoursePacks(userId: string): Promise<CoursePack[]>;
    getUserRecentCoursePacks(userId: string): Promise<CoursePack[]>;
    upsert(userId: string, coursePackId: string, courseId: string, statementIndex: number): Promise<UserCourseProgress>;
}
