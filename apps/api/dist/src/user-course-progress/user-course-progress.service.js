"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCourseProgressService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let UserCourseProgressService = class UserCourseProgressService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findStatement(userId, coursePackId, courseId) {
        const progress = await this.prisma.userCourseProgress.findFirst({
            where: {
                userId,
                coursePackId,
                courseId
            }
        });
        return progress?.statementIndex || 0;
    }
    async findRecentCoursePacks(userId) {
        const progress = await this.prisma.userCourseProgress.findMany({
            where: { userId },
            orderBy: {
                updatedAt: 'desc'
            },
            take: 5,
            select: {
                coursePackId: true
            }
        });
        const coursePackIds = progress.map(p => p.coursePackId);
        const coursePacks = await this.prisma.coursePack.findMany({
            where: {
                id: {
                    in: coursePackIds
                }
            }
        });
        return coursePackIds.map(id => coursePacks.find(pack => pack.id === id)).filter(Boolean);
    }
    async getUserRecentCoursePacks(userId) {
        return this.findRecentCoursePacks(userId);
    }
    async upsert(userId, coursePackId, courseId, statementIndex) {
        return this.prisma.userCourseProgress.upsert({
            where: {
                userId_coursePackId: {
                    userId,
                    coursePackId
                }
            },
            update: {
                courseId,
                statementIndex
            },
            create: {
                userId,
                coursePackId,
                courseId,
                statementIndex
            }
        });
    }
};
exports.UserCourseProgressService = UserCourseProgressService;
exports.UserCourseProgressService = UserCourseProgressService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserCourseProgressService);
//# sourceMappingURL=user-course-progress.service.js.map