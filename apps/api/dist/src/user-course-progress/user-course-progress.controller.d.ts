import { UserEntity } from "../user/user.decorators";
import { UpsertUserProgressDto } from "./model/user-progress.dto";
import { UserCourseProgressService } from "./user-course-progress.service";
import { CoursePack } from "@prisma/client";
export declare class UserProgressController {
    private readonly userCourseProgressService;
    constructor(userCourseProgressService: UserCourseProgressService);
    getUserRecentCoursePacks(user: UserEntity, limit?: string): Promise<CoursePack[]>;
    upsert(user: UserEntity, dto: UpsertUserProgressDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        statementIndex: number;
    }>;
}
