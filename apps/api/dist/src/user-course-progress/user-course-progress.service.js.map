{"version": 3, "file": "user-course-progress.service.js", "sourceRoot": "", "sources": ["../../../src/user-course-progress/user-course-progress.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6DAAyD;AAIlD,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,YAAoB,EAAE,QAAgB;QACxE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC;YAC9D,KAAK,EAAE;gBACL,MAAM;gBACN,YAAY;gBACZ,QAAQ;aACT;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,EAAE,cAAc,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAExC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC7D,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;YACD,IAAI,EAAE,CAAC;YACP,MAAM,EAAE;gBACN,YAAY,EAAE,IAAI;aACnB;SACF,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QACxD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACxD,KAAK,EAAE;gBACL,EAAE,EAAE;oBACF,EAAE,EAAE,aAAa;iBAClB;aACF;SACF,CAAC,CAAC;QAGH,OAAO,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAC5B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CACzC,CAAC,MAAM,CAAC,OAAO,CAAiB,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,MAAc;QAC3C,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,MAAM,CACV,MAAc,EACd,YAAoB,EACpB,QAAgB,EAChB,cAAsB;QAEtB,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC3C,KAAK,EAAE;gBACL,mBAAmB,EAAE;oBACnB,MAAM;oBACN,YAAY;iBACb;aACF;YACD,MAAM,EAAE;gBACN,QAAQ;gBACR,cAAc;aACf;YACD,MAAM,EAAE;gBACN,MAAM;gBACN,YAAY;gBACZ,QAAQ;gBACR,cAAc;aACf;SACF,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAxEY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,yBAAyB,CAwErC"}