import { PrismaService } from '../prisma/prisma.service';
import { CourseHistoryService } from '../course-history/course-history.service';
export declare class CourseService {
    private readonly prisma;
    private readonly courseHistoryService;
    constructor(prisma: PrismaService, courseHistoryService: CourseHistoryService);
    find(coursePackId: string, courseId: string): Promise<{
        statements: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            order: number;
            soundmark: string;
            chinese: string;
            english: string;
            audioEnUsMale: string;
            audioEnGbMale: string;
            audioEnUsFemale: string;
            audioEnGbFemale: string;
            audioStatus: string;
            audioGeneratedAt: Date;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        coursePackId: string;
        description: string | null;
        order: number;
        title: string;
        cover: string | null;
    }>;
    findWithUserProgress(coursePackId: string, courseId: string, userId: string): Promise<{
        completionCount: number;
        statements: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            order: number;
            soundmark: string;
            chinese: string;
            english: string;
            audioEnUsMale: string;
            audioEnGbMale: string;
            audioEnUsFemale: string;
            audioEnGbFemale: string;
            audioStatus: string;
            audioGeneratedAt: Date;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        coursePackId: string;
        description: string | null;
        order: number;
        title: string;
        cover: string | null;
    }>;
    findNext(coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        coursePackId: string;
        description: string | null;
        order: number;
        title: string;
        cover: string | null;
    }>;
    completeCourse(userId: string, coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }>;
}
