"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseModule = void 0;
const common_1 = require("@nestjs/common");
const course_history_service_1 = require("../course-history/course-history.service");
const global_module_1 = require("../global/global.module");
const membership_module_1 = require("../membership/membership.module");
const prisma_module_1 = require("../prisma/prisma.module");
const rank_service_1 = require("../rank/rank.service");
const user_course_progress_service_1 = require("../user-course-progress/user-course-progress.service");
const user_module_1 = require("../user/user.module");
const course_service_1 = require("./course.service");
let CourseModule = class CourseModule {
};
exports.CourseModule = CourseModule;
exports.CourseModule = CourseModule = __decorate([
    (0, common_1.Module)({
        imports: [global_module_1.GlobalModule, user_module_1.UserModule, membership_module_1.MembershipModule, prisma_module_1.PrismaModule],
        providers: [course_service_1.CourseService, user_course_progress_service_1.UserCourseProgressService, rank_service_1.RankService, course_history_service_1.CourseHistoryService],
        controllers: [],
        exports: [course_service_1.CourseService],
    })
], CourseModule);
//# sourceMappingURL=course.module.js.map