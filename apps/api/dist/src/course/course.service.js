"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const course_history_service_1 = require("../course-history/course-history.service");
let CourseService = class CourseService {
    constructor(prisma, courseHistoryService) {
        this.prisma = prisma;
        this.courseHistoryService = courseHistoryService;
    }
    async find(coursePackId, courseId) {
        const course = await this.prisma.course.findFirst({
            where: {
                id: courseId,
                coursePackId
            },
            include: {
                statements: {
                    orderBy: {
                        order: 'asc'
                    },
                    select: {
                        id: true,
                        order: true,
                        chinese: true,
                        english: true,
                        soundmark: true,
                        audioEnUsMale: true,
                        audioEnUsFemale: true,
                        audioEnGbMale: true,
                        audioEnGbFemale: true,
                        audioStatus: true,
                        audioGeneratedAt: true,
                        createdAt: true,
                        updatedAt: true
                    }
                }
            }
        });
        if (!course) {
            throw new common_1.NotFoundException('课程不存在');
        }
        return course;
    }
    async findWithUserProgress(coursePackId, courseId, userId) {
        const course = await this.find(coursePackId, courseId);
        const completionCount = await this.courseHistoryService.findCompletionCount(userId, coursePackId, courseId);
        return {
            ...course,
            completionCount
        };
    }
    async findNext(coursePackId, courseId) {
        const currentCourse = await this.prisma.course.findFirst({
            where: {
                id: courseId,
                coursePackId
            },
            select: {
                order: true
            }
        });
        if (!currentCourse) {
            throw new common_1.NotFoundException('当前课程不存在');
        }
        const nextCourse = await this.prisma.course.findFirst({
            where: {
                coursePackId,
                order: {
                    gt: currentCourse.order
                }
            },
            orderBy: {
                order: 'asc'
            }
        });
        return nextCourse;
    }
    async completeCourse(userId, coursePackId, courseId) {
        return this.courseHistoryService.createOrUpdate(userId, coursePackId, courseId);
    }
};
exports.CourseService = CourseService;
exports.CourseService = CourseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        course_history_service_1.CourseHistoryService])
], CourseService);
//# sourceMappingURL=course.service.js.map