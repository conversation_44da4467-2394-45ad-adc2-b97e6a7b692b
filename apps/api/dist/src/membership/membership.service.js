"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MembershipService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const prisma_service_1 = require("../prisma/prisma.service");
const buy_membership_dto_1 = require("./dto/buy-membership.dto");
let MembershipService = MembershipService_1 = class MembershipService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(MembershipService_1.name);
    }
    async updateMembership(userId, data) {
        return this.prisma.membership.update({
            where: { userId },
            data
        });
    }
    async createMembership(userId, data) {
        return this.prisma.membership.create({
            data: {
                ...data,
                user: {
                    connect: { id: userId }
                }
            }
        });
    }
    async findByUserId(userId) {
        return this.prisma.membership.findUnique({
            where: { userId }
        });
    }
    async isFounderMembership(userId) {
        const membership = await this.prisma.membership.findFirst({
            where: {
                userId,
                tier: 'founder',
                isActive: true
            }
        });
        return !!membership;
    }
    async deactivateMembership(userId) {
        return this.prisma.membership.update({
            where: { userId },
            data: {
                isActive: false
            }
        });
    }
    async upsert(startDate, buyMembershipDto) {
        const { userId } = buyMembershipDto;
        const membership = await this.findByUserId(userId);
        if (membership && membership.isActive) {
            const endDate = this.calculateEndDate(membership.endDate, buyMembershipDto);
            await this.updateMembership(userId, {
                endDate: endDate,
            });
            this.logger.log(`Membership for user ${userId} extend end date to ${endDate}`);
            return { endDate, startDate, isActive: true };
        }
        else {
            const endDate = this.calculateEndDate(startDate, buyMembershipDto);
            if (!membership) {
                await this.createMembership(userId, {
                    startDate,
                    endDate,
                    isActive: true,
                    tier: 'regular'
                });
                this.logger.log(`Membership for user ${userId} has been created`);
            }
            else {
                await this.updateMembership(userId, {
                    endDate,
                    isActive: true,
                    startDate
                });
                this.logger.log(`Membership for user ${userId} has been updated`);
            }
            return { endDate, startDate, isActive: true };
        }
    }
    calculateEndDate(startDate, buyMembershipDto) {
        const { period, duration } = buyMembershipDto;
        const endDate = new Date(startDate);
        if (period === buy_membership_dto_1.MembershipPeriod.MONTH) {
            endDate.setMonth(endDate.getMonth() + Number(duration));
        }
        else if (period === buy_membership_dto_1.MembershipPeriod.YEAR) {
            endDate.setFullYear(endDate.getFullYear() + Number(duration));
        }
        return endDate;
    }
    async isMember(userId) {
        const membership = await this.findByUserId(userId);
        return membership?.isActive || false;
    }
    async getMembershipDetails(userId) {
        const membership = await this.findByUserId(userId);
        if (!membership)
            return;
        return {
            startDate: membership.startDate,
            endDate: membership.endDate,
            tier: membership.tier,
        };
    }
    async deactivateExpiredMemberships() {
        this.logger.log("Running scheduled task to deactivate expired memberships");
        try {
            await this.prisma.membership.updateMany({
                where: {
                    endDate: {
                        lt: new Date()
                    },
                    isActive: true
                },
                data: {
                    isActive: false
                }
            });
            this.logger.log(`Deactivated expired memberships`);
        }
        catch (error) {
            this.logger.error("Failed to deactivate expired memberships", error.stack);
        }
    }
};
exports.MembershipService = MembershipService;
__decorate([
    (0, schedule_1.Cron)("0 0 * * *"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MembershipService.prototype, "deactivateExpiredMemberships", null);
exports.MembershipService = MembershipService = MembershipService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MembershipService);
//# sourceMappingURL=membership.service.js.map