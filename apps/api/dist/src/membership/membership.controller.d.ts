import { BuyMembershipDto } from "./dto/buy-membership.dto";
import { MembershipService } from "./membership.service";
export declare class MembershipController {
    private readonly membershipService;
    constructor(membershipService: MembershipService);
    buyMembership(buyMembershipDto: BuyMembershipDto): Promise<{
        endDate: Date;
        startDate: Date;
        isActive: boolean;
    }>;
}
