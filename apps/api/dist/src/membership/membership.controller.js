"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const buy_membership_dto_1 = require("./dto/buy-membership.dto");
const membership_service_1 = require("./membership.service");
let MembershipController = class MembershipController {
    constructor(membershipService) {
        this.membershipService = membershipService;
    }
    async buyMembership(buyMembershipDto) {
        return await this.membershipService.upsert(new Date(), buyMembershipDto);
    }
};
exports.MembershipController = MembershipController;
__decorate([
    (0, auth_guard_1.Permissions)("write:membership"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)("buy"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [buy_membership_dto_1.BuyMembershipDto]),
    __metadata("design:returntype", Promise)
], MembershipController.prototype, "buyMembership", null);
exports.MembershipController = MembershipController = __decorate([
    (0, common_1.Controller)("membership"),
    __metadata("design:paramtypes", [membership_service_1.MembershipService])
], MembershipController);
//# sourceMappingURL=membership.controller.js.map