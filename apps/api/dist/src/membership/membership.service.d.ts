import { PrismaService } from '../prisma/prisma.service';
import { BuyMembershipDto } from "./dto/buy-membership.dto";
import { Prisma } from "@prisma/client";
export declare class MembershipService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    updateMembership(userId: string, data: Prisma.MembershipUpdateInput): Promise<Prisma.MembershipGetPayload<{}>>;
    createMembership(userId: string, data: Omit<Prisma.MembershipCreateInput, 'user'>): Promise<Prisma.MembershipGetPayload<{}>>;
    findByUserId(userId: string): Promise<Prisma.MembershipGetPayload<{}> | null>;
    isFounderMembership(userId: string): Promise<boolean>;
    deactivateMembership(userId: string): Promise<Prisma.MembershipGetPayload<{}>>;
    upsert(startDate: Date, buyMembershipDto: BuyMembershipDto): Promise<{
        endDate: Date;
        startDate: Date;
        isActive: boolean;
    }>;
    private calculateEndDate;
    isMember(userId: string): Promise<boolean>;
    getMembershipDetails(userId: string): Promise<{
        startDate: Date;
        endDate: Date;
        tier: string;
    }>;
    deactivateExpiredMemberships(): Promise<void>;
}
