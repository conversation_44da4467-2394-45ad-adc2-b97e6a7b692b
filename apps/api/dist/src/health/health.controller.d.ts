import { HealthService } from './health.service';
export declare class HealthController {
    private readonly healthService;
    constructor(healthService: HealthService);
    getHealthStatus(): Promise<{
        status: string;
        timestamp: string;
        database: boolean;
        redis: boolean;
        uptime: number;
        error?: undefined;
    } | {
        status: string;
        timestamp: string;
        database: boolean;
        redis: boolean;
        uptime: number;
        error: any;
    }>;
}
