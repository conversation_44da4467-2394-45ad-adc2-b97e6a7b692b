import { PrismaService } from '../prisma/prisma.service';
export declare class HealthService {
    private readonly prisma;
    private readonly logger;
    private databaseHealthy;
    private redisHealthy;
    constructor(prisma: PrismaService);
    checkDatabaseHealth(): Promise<void>;
    getHealthStatus(): Promise<{
        status: string;
        timestamp: string;
        database: boolean;
        redis: boolean;
        uptime: number;
        error?: undefined;
    } | {
        status: string;
        timestamp: string;
        database: boolean;
        redis: boolean;
        uptime: number;
        error: any;
    }>;
}
