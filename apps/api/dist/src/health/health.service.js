"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var HealthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const prisma_service_1 = require("../prisma/prisma.service");
let HealthService = HealthService_1 = class HealthService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(HealthService_1.name);
        this.databaseHealthy = true;
        this.redisHealthy = true;
    }
    async checkDatabaseHealth() {
        try {
            await this.prisma.$queryRaw `SELECT 1 as health`;
            if (!this.databaseHealthy) {
                this.logger.log('数据库连接已恢复');
            }
            this.databaseHealthy = true;
        }
        catch (error) {
            this.databaseHealthy = false;
            this.logger.error(`数据库连接检查失败: ${error.message}`);
        }
    }
    async getHealthStatus() {
        try {
            await this.checkDatabaseHealth();
            return {
                status: 'ok',
                timestamp: new Date().toISOString(),
                database: this.databaseHealthy,
                redis: this.redisHealthy,
                uptime: process.uptime(),
            };
        }
        catch (error) {
            this.logger.error(`健康检查失败: ${error.message}`);
            return {
                status: 'error',
                timestamp: new Date().toISOString(),
                database: this.databaseHealthy,
                redis: this.redisHealthy,
                uptime: process.uptime(),
                error: error.message,
            };
        }
    }
};
exports.HealthService = HealthService;
__decorate([
    (0, schedule_1.Interval)(30000),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthService.prototype, "checkDatabaseHealth", null);
exports.HealthService = HealthService = HealthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], HealthService);
//# sourceMappingURL=health.service.js.map