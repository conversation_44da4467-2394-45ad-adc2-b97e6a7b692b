"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const message_type_dto_1 = require("./dto/message-type.dto");
const client_1 = require("@prisma/client");
let MessagesService = class MessagesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getMessageCounts() {
        const counts = await this.prisma.userMessage.groupBy({
            by: ['status'],
            _count: {
                status: true
            }
        });
        const statusCounts = {
            UNREAD: 0,
            READ: 0,
            PROCESSED: 0
        };
        counts.forEach(count => {
            statusCounts[count.status] = count._count.status;
        });
        return statusCounts;
    }
    async findAll(page = 1, pageSize = 10, type, status) {
        const where = {
            ...(type && { type: type }),
            ...(status && { status: status })
        };
        const [messages, total] = await Promise.all([
            this.prisma.userMessage.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' }
            }),
            this.prisma.userMessage.count({ where })
        ]);
        return {
            messages,
            total
        };
    }
    async updateStatus(id, status) {
        return this.prisma.userMessage.update({
            where: { id: Number(id) },
            data: { status: status }
        });
    }
    async delete(id) {
        return this.prisma.userMessage.delete({
            where: { id: Number(id) }
        });
    }
    async create(createMessageDto) {
        const type = Object.values(message_type_dto_1.MessageType).includes(createMessageDto.type)
            ? createMessageDto.type
            : client_1.MessageType.FEEDBACK;
        return this.prisma.userMessage.create({
            data: {
                name: createMessageDto.name || '匿名用户',
                contact: createMessageDto.contact || null,
                type,
                content: createMessageDto.content,
                status: client_1.MessageStatus.UNREAD,
                createdAt: new Date()
            }
        });
    }
};
exports.MessagesService = MessagesService;
exports.MessagesService = MessagesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MessagesService);
//# sourceMappingURL=messages.service.js.map