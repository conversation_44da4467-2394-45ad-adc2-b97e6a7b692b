"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessagesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesController = void 0;
const common_1 = require("@nestjs/common");
const messages_service_1 = require("./messages.service");
const create_message_dto_1 = require("./dto/create-message.dto");
const public_decorator_1 = require("../auth/decorators/public.decorator");
const message_type_dto_1 = require("./dto/message-type.dto");
let MessagesController = MessagesController_1 = class MessagesController {
    constructor(messagesService) {
        this.messagesService = messagesService;
        this.logger = new common_1.Logger(MessagesController_1.name);
    }
    async getMessageCounts() {
        return this.messagesService.getMessageCounts();
    }
    async findAll(page = 1, pageSize = 10, type, status) {
        return this.messagesService.findAll(page, pageSize, type, status);
    }
    async updateStatus(id, status) {
        return this.messagesService.updateStatus(id, status);
    }
    async delete(id) {
        return this.messagesService.delete(id);
    }
    async create(createMessageDto) {
        this.logger.log('收到消息创建请求');
        this.logger.log('请求完整信息:', JSON.stringify({
            body: createMessageDto,
            headers: process.env.NODE_ENV === 'development' ? process.env : 'Headers hidden'
        }, null, 2));
        this.logger.log(`请求内容类型: ${createMessageDto.type}`);
        this.logger.log(`请求内容长度: ${createMessageDto.content?.length || 0}`);
        this.logger.log(`请求联系方式: ${createMessageDto.contact || '未提供'}`);
        if (!createMessageDto.content || createMessageDto.content.trim().length === 0) {
            this.logger.warn('消息内容为空');
            throw new common_1.BadRequestException('消息内容不能为空');
        }
        if (createMessageDto.content.length > 500) {
            this.logger.warn('消息内容超过长度限制');
            throw new common_1.BadRequestException('消息内容不能超过500个字符');
        }
        if (createMessageDto.contact) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(createMessageDto.contact)) {
                this.logger.warn('无效的联系邮箱');
                throw new common_1.BadRequestException('无效的联系邮箱');
            }
        }
        try {
            const result = await this.messagesService.create(createMessageDto);
            this.logger.log('消息创建成功', JSON.stringify(result));
            return result;
        }
        catch (error) {
            this.logger.error('消息创建失败', error);
            throw new common_1.BadRequestException('消息创建失败：' + error.message);
        }
    }
};
exports.MessagesController = MessagesController;
__decorate([
    (0, common_1.Get)('counts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getMessageCounts", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('type')),
    __param(3, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Patch)('/:id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_message_dto_1.CreateMessageDto]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "create", null);
exports.MessagesController = MessagesController = MessagesController_1 = __decorate([
    (0, common_1.Controller)('messages'),
    __metadata("design:paramtypes", [messages_service_1.MessagesService])
], MessagesController);
//# sourceMappingURL=messages.controller.js.map