import { MessagesService } from './messages.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { MessageType, MessageStatus } from './dto/message-type.dto';
export declare class MessagesController {
    private readonly messagesService;
    private readonly logger;
    constructor(messagesService: MessagesService);
    getMessageCounts(): Promise<{
        UNREAD: number;
        READ: number;
        PROCESSED: number;
    }>;
    findAll(page?: number, pageSize?: number, type?: MessageType, status?: MessageStatus): Promise<{
        messages: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            name: string | null;
            content: string;
            type: import(".prisma/client").$Enums.MessageType;
            contact: string | null;
            ipAddress: string | null;
            status: import(".prisma/client").$Enums.MessageStatus;
        }[];
        total: number;
    }>;
    updateStatus(id: string, status: MessageStatus): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
    delete(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
    create(createMessageDto: CreateMessageDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
}
