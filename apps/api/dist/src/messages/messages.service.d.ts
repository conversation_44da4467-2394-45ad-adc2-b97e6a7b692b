import { PrismaService } from '../prisma/prisma.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { MessageType as LocalMessageType, MessageStatus as LocalMessageStatus } from './dto/message-type.dto';
export declare class MessagesService {
    private prisma;
    constructor(prisma: PrismaService);
    getMessageCounts(): Promise<{
        UNREAD: number;
        READ: number;
        PROCESSED: number;
    }>;
    findAll(page?: number, pageSize?: number, type?: LocalMessageType, status?: LocalMessageStatus): Promise<{
        messages: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            name: string | null;
            content: string;
            type: import(".prisma/client").$Enums.MessageType;
            contact: string | null;
            ipAddress: string | null;
            status: import(".prisma/client").$Enums.MessageStatus;
        }[];
        total: number;
    }>;
    updateStatus(id: string, status: LocalMessageStatus): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
    delete(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
    create(createMessageDto: CreateMessageDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        content: string;
        type: import(".prisma/client").$Enums.MessageType;
        contact: string | null;
        ipAddress: string | null;
        status: import(".prisma/client").$Enums.MessageStatus;
    }>;
}
