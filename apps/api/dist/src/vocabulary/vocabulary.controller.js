"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VocabularyController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VocabularyController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const vocabulary_service_1 = require("./vocabulary.service");
let VocabularyController = VocabularyController_1 = class VocabularyController {
    constructor(vocabularyService) {
        this.vocabularyService = vocabularyService;
        this.logger = new common_1.Logger(VocabularyController_1.name);
    }
    async getVocabulary(user) {
        this.logger.log(`获取用户生词表: userId=${user.userId}`);
        try {
            const vocabulary = await this.vocabularyService.getVocabulary(user.userId);
            this.logger.log(`获取生词表成功: userId=${user.userId}, 数量=${vocabulary.length}`);
            return vocabulary;
        }
        catch (error) {
            this.logger.error(`获取生词表失败: userId=${user.userId}`, error.stack);
            throw error;
        }
    }
    async saveVocabulary(user, data) {
        this.logger.log(`保存用户生词表: userId=${user.userId}, 词汇数量=${data.words?.length || 0}`);
        try {
            await this.vocabularyService.saveVocabulary(user.userId, data.words || []);
            this.logger.log(`保存生词表成功: userId=${user.userId}`);
            return { success: true };
        }
        catch (error) {
            this.logger.error(`保存生词表失败: userId=${user.userId}`, error.stack);
            throw error;
        }
    }
};
exports.VocabularyController = VocabularyController;
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VocabularyController.prototype, "getVocabulary", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Post)(),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VocabularyController.prototype, "saveVocabulary", null);
exports.VocabularyController = VocabularyController = VocabularyController_1 = __decorate([
    (0, common_1.Controller)('vocabulary'),
    __metadata("design:paramtypes", [vocabulary_service_1.VocabularyService])
], VocabularyController);
//# sourceMappingURL=vocabulary.controller.js.map