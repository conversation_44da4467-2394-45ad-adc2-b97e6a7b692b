"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VocabularyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VocabularyService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let VocabularyService = VocabularyService_1 = class VocabularyService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(VocabularyService_1.name);
    }
    async getVocabulary(userId) {
        this.logger.log(`获取用户生词表: userId=${userId}`);
        try {
            const userVocabulary = await this.prisma.userVocabulary.findUnique({
                where: { userId }
            });
            if (!userVocabulary) {
                this.logger.log(`用户生词表不存在，返回空数组: userId=${userId}`);
                return [];
            }
            if (typeof userVocabulary.words === 'string') {
                try {
                    return JSON.parse(userVocabulary.words);
                }
                catch (e) {
                    this.logger.error(`解析生词表JSON失败: ${e.message}`, e.stack);
                    return [];
                }
            }
            else if (Array.isArray(userVocabulary.words)) {
                return userVocabulary.words;
            }
            else {
                this.logger.warn(`生词表格式不正确: ${typeof userVocabulary.words}`);
                return [];
            }
        }
        catch (error) {
            this.logger.error(`获取用户生词表失败: userId=${userId}`, error.stack);
            throw new Error('获取生词表失败');
        }
    }
    async saveVocabulary(userId, words) {
        this.logger.log(`保存用户生词表: userId=${userId}, 词汇数量=${words.length}`);
        try {
            await this.prisma.userVocabulary.upsert({
                where: { userId },
                update: {
                    words,
                    updatedAt: new Date()
                },
                create: {
                    userId,
                    words
                }
            });
            this.logger.log(`用户生词表保存成功: userId=${userId}`);
        }
        catch (error) {
            this.logger.error(`保存用户生词表失败: userId=${userId}`, error.stack);
            throw new Error('保存生词表失败');
        }
    }
};
exports.VocabularyService = VocabularyService;
exports.VocabularyService = VocabularyService = VocabularyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VocabularyService);
//# sourceMappingURL=vocabulary.service.js.map