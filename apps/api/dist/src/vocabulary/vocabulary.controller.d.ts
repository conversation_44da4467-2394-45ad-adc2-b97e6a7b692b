import { VocabularyService } from './vocabulary.service';
import { UserEntity } from '../user/user.controller';
export declare class VocabularyController {
    private readonly vocabularyService;
    private readonly logger;
    constructor(vocabularyService: VocabularyService);
    getVocabulary(user: UserEntity): Promise<any[]>;
    saveVocabulary(user: UserEntity, data: {
        words: any[];
    }): Promise<{
        success: boolean;
    }>;
}
