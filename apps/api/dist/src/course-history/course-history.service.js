"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseHistoryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CourseHistoryService = class CourseHistoryService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(userId) {
        return this.prisma.courseHistory.findMany({
            where: { userId },
        });
    }
    async findByUserAndCourse(userId, courseId) {
        return this.prisma.courseHistory.findFirst({
            where: {
                userId,
                courseId,
            },
        });
    }
    async findByCoursePackId(userId, coursePackId) {
        const histories = await this.prisma.courseHistory.findMany({
            where: {
                userId,
                coursePackId
            }
        });
        const result = await Promise.all(histories.map(async (history) => {
            const course = await this.prisma.course.findUnique({
                where: { id: history.courseId }
            });
            const coursePack = await this.prisma.coursePack.findUnique({
                where: { id: history.coursePackId }
            });
            return {
                ...history,
                course,
                coursePack
            };
        }));
        return result;
    }
    async findCompletionCount(userId, coursePackId, courseId) {
        const history = await this.prisma.courseHistory.findUnique({
            where: {
                userId_courseId_coursePackId: {
                    userId,
                    courseId,
                    coursePackId,
                },
            },
        });
        const completionCount = history?.completionCount || 0;
        return completionCount;
    }
    async createOrUpdate(userId, coursePackId, courseId) {
        const result = await this.prisma.courseHistory.upsert({
            where: {
                userId_courseId_coursePackId: {
                    userId,
                    courseId,
                    coursePackId,
                },
            },
            create: {
                userId,
                courseId,
                coursePackId,
                completionCount: 1,
            },
            update: {
                completionCount: {
                    increment: 1,
                },
            },
        });
        return result;
    }
    async getCompletedLessonsCount(userId) {
        const completedLessons = await this.prisma.courseHistory.count({
            where: {
                userId,
                completionCount: {
                    gt: 0
                }
            }
        });
        return completedLessons;
    }
};
exports.CourseHistoryService = CourseHistoryService;
exports.CourseHistoryService = CourseHistoryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CourseHistoryService);
//# sourceMappingURL=course-history.service.js.map