"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseHistoryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../guards/auth.guard");
const user_decorators_1 = require("../user/user.decorators");
const course_history_service_1 = require("./course-history.service");
let CourseHistoryController = class CourseHistoryController {
    constructor(courseHistoryService) {
        this.courseHistoryService = courseHistoryService;
    }
    courseCompletionCount(user) {
        return this.courseHistoryService.findAll(user.userId);
    }
    async getCoursePackHistory(user, coursePackId) {
        return this.courseHistoryService.findByCoursePackId(user.userId, coursePackId);
    }
};
exports.CourseHistoryController = CourseHistoryController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: "获取登陆用户的所有课程历史记录",
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(""),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CourseHistoryController.prototype, "courseCompletionCount", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)(":coursePackId"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("coursePackId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CourseHistoryController.prototype, "getCoursePackHistory", null);
exports.CourseHistoryController = CourseHistoryController = __decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiTags)("CourseHistory"),
    (0, common_1.Controller)("course-history"),
    __metadata("design:paramtypes", [course_history_service_1.CourseHistoryService])
], CourseHistoryController);
//# sourceMappingURL=course-history.controller.js.map