import { PrismaService } from '../prisma/prisma.service';
export declare class CourseHistoryService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    findAll(userId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }[]>;
    findByUserAndCourse(userId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }>;
    findByCoursePackId(userId: string, coursePackId: string): Promise<{
        course: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            coursePackId: string;
            description: string | null;
            order: number;
            title: string;
            cover: string | null;
        };
        coursePack: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            order: number;
            title: string;
            cover: string | null;
            creatorId: string;
            isFree: boolean;
            difficulty: number;
            shareLevel: import(".prisma/client").$Enums.ShareLevel;
        };
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }[]>;
    findCompletionCount(userId: string, coursePackId: string, courseId: string): Promise<number>;
    createOrUpdate(userId: string, coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }>;
    getCompletedLessonsCount(userId: string): Promise<number>;
}
