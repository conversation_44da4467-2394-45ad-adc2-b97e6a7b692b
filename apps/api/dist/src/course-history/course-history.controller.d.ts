import { UserEntity } from "../user/user.decorators";
import { CourseHistoryService } from "./course-history.service";
import { CourseHistory } from "@prisma/client";
interface CourseHistoryWithDetails extends CourseHistory {
    course: {
        id: string;
        title: string;
        description: string | null;
    };
    coursePack: {
        id: string;
        title: string;
        description: string | null;
    };
}
export declare class CourseHistoryController {
    private readonly courseHistoryService;
    constructor(courseHistoryService: CourseHistoryService);
    courseCompletionCount(user: UserEntity): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }[]>;
    getCoursePackHistory(user: UserEntity, coursePackId: string): Promise<CourseHistoryWithDetails[]>;
}
export {};
