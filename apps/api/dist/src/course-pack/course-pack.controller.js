"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CoursePackController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoursePackController = void 0;
const common_1 = require("@nestjs/common");
const guards_1 = require("../guards");
const user_decorators_1 = require("../user/user.decorators");
const course_pack_service_1 = require("./course-pack.service");
const client_1 = require("@prisma/client");
const public_decorator_1 = require("../auth/decorators/public.decorator");
let CoursePackController = CoursePackController_1 = class CoursePackController {
    constructor(coursePackService) {
        this.coursePackService = coursePackService;
        this.logger = new common_1.Logger(CoursePackController_1.name);
    }
    async getPublicPackages() {
        this.logger.log(`[CoursePackController] 获取公开课程包列表`);
        try {
            const coursePacks = await this.coursePackService.findPublicCoursePacks(undefined);
            this.logger.log(`[CoursePackController] 成功获取公开课程包列表，数量: ${coursePacks.length}`);
            return coursePacks;
        }
        catch (error) {
            this.logger.error(`[CoursePackController] 获取公开课程包列表失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取公开课程包列表失败，请稍后重试',
                error: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async create(user, data) {
        this.logger.log(`创建课程包请求，用户ID: ${user.userId}`);
        try {
            const coursePack = await this.coursePackService.create({
                ...data,
                creatorId: user.userId,
            });
            this.logger.log(`课程包创建成功，ID: ${coursePack.id}`);
            return coursePack;
        }
        catch (error) {
            this.logger.error(`创建课程包失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getMallCoursePacks(user) {
        this.logger.log(`[CoursePackController] 获取课程包商城列表，用户ID: ${user.userId}`);
        try {
            const coursePacks = await this.coursePackService.findPublicCoursePacks(user.userId);
            this.logger.log(`[CoursePackController] 成功获取商城课程包列表，数量: ${coursePacks.length}`);
            return coursePacks;
        }
        catch (error) {
            this.logger.error(`[CoursePackController] 获取商城课程包列表失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取商城课程包列表失败，请稍后重试',
                error: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findJoined(user) {
        this.logger.log(`[CoursePackController] 获取用户(${user.userId})已加入的课程包列表请求`);
        try {
            const result = await this.coursePackService.findJoinedCoursePacks(user.userId);
            this.logger.log(`[CoursePackController] 成功获取用户(${user.userId})的课程包列表，数量: ${result.length}`);
            if (result.length > 0) {
                this.logger.log(`[CoursePackController] 用户(${user.userId})的课程包列表:`, result.map(pack => ({
                    id: pack.id,
                    title: pack.title,
                    课程数量: pack.courses?.length || 0,
                    加入时间: pack.createdAt,
                    最后更新: pack.updatedAt
                })));
            }
            else {
                this.logger.warn(`[CoursePackController] 用户(${user.userId})暂无已加入的课程包`);
            }
            return result.map(pack => ({
                id: pack.id,
                title: pack.title,
                description: pack.description,
                isFree: pack.isFree,
                cover: pack.cover,
                courses: pack.courses?.map(course => ({
                    id: course.id,
                    title: course.title,
                    description: course.description,
                    order: course.order,
                    coursePackId: course.coursePackId,
                    completionCount: course.completionCount,
                    createdAt: course.createdAt.toISOString(),
                    updatedAt: course.updatedAt.toISOString()
                })) || [],
                createdAt: pack.createdAt.toISOString(),
                updatedAt: pack.updatedAt.toISOString()
            }));
        }
        catch (error) {
            this.logger.error(`[CoursePackController] 获取用户(${user.userId})的课程包列表失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取课程包列表失败，请稍后重试',
                error: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(user) {
        this.logger.log(`获取课程包列表请求，用户ID: ${user?.userId || '未认证'}`);
        try {
            const result = await this.coursePackService.findAll(user?.userId);
            this.logger.log(`成功获取课程包列表，数量: ${result.length}`);
            const coursePacks = await Promise.all(result.map(async (pack) => {
                const courses = await this.coursePackService.findCoursesByPackId(pack.id);
                return {
                    ...pack,
                    totalCourses: courses.length
                };
            }));
            return coursePacks;
        }
        catch (error) {
            this.logger.error(`获取课程包列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createTestData(user) {
        this.logger.log('创建测试数据');
        try {
            const testData = [
                {
                    title: '前端开发入门课程包',
                    description: '从零开始学习前端开发，包含 HTML、CSS 和 JavaScript 基础知识',
                    cover: 'https://picsum.photos/300/200',
                    isFree: true,
                    shareLevel: client_1.ShareLevel.PUBLIC,
                    creatorId: user.userId
                },
                {
                    title: 'React 进阶课程包',
                    description: '深入学习 React 框架，包含组件开发、状态管理和性能优化',
                    cover: 'https://picsum.photos/300/200',
                    isFree: false,
                    shareLevel: client_1.ShareLevel.PUBLIC,
                    creatorId: user.userId
                },
                {
                    title: 'Vue.js 实战课程包',
                    description: '通过实战项目学习 Vue.js，包含 Vue 3、Composition API 和 Vuex',
                    cover: 'https://picsum.photos/300/200',
                    isFree: false,
                    shareLevel: client_1.ShareLevel.PUBLIC,
                    creatorId: user.userId
                }
            ];
            const results = await Promise.all(testData.map(data => this.coursePackService.create(data)));
            return {
                message: '测试数据创建成功',
                data: results
            };
        }
        catch (error) {
            this.logger.error('创建测试数据失败:', error);
            throw new common_1.InternalServerErrorException('创建测试数据失败');
        }
    }
    async join(user, id) {
        this.logger.log(`用户 ${user.userId} 请求加入课程包 ${id}`);
        try {
            const result = await this.coursePackService.joinCoursePack(user.userId, id);
            this.logger.log(`用户 ${user.userId} 成功加入课程包 ${id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`用户 ${user.userId} 加入课程包 ${id} 失败:`, error);
            throw error;
        }
    }
    async findOne(user, coursePackId) {
        console.log(`🔥🔥🔥 [CoursePackController] 修改已生效！获取课程包详情请求，课程包ID: ${coursePackId}，用户ID: ${user?.userId || '未认证'}`);
        this.logger.log(`[CoursePackController] 获取课程包详情请求，课程包ID: ${coursePackId}，用户ID: ${user?.userId || '未认证'}`);
        try {
            const result = await this.coursePackService.findOneWithCourses(user?.userId, coursePackId);
            console.log(`🔥🔥🔥 [CoursePackController] 调用 findOneWithCourses 完成，结果课程数量: ${result.courses?.length}`);
            this.logger.log(`[CoursePackController] 成功获取课程包详情，ID: ${result.id}`);
            return result;
        }
        catch (error) {
            console.log(`🔥🔥🔥 [CoursePackController] 获取课程包详情失败:`, error);
            this.logger.error(`[CoursePackController] 获取课程包详情失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw new common_1.HttpException({
                message: '获取课程包详情失败，请稍后重试',
                error: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findCourse(user, coursePackId, courseId) {
        this.logger.log(`获取课程详情请求，课程包ID: ${coursePackId}, 课程ID: ${courseId}, 用户ID: ${user?.userId || '未认证'}`);
        return this.coursePackService.findCourse(user?.userId, coursePackId, courseId);
    }
    findNextCourse(coursePackId, courseId) {
        this.logger.log(`获取下一课程请求，课程包ID: ${coursePackId}, 当前课程ID: ${courseId}`);
        return this.coursePackService.findNextCourse(coursePackId, courseId);
    }
    CompleteCourse(user, coursePackId, courseId) {
        this.logger.log(`完成课程请求，课程包ID: ${coursePackId}, 课程ID: ${courseId}, 用户ID: ${user.userId}`);
        return this.coursePackService.completeCourse(user.userId, coursePackId, courseId);
    }
    async quit(user, id) {
        this.logger.log(`用户 ${user.userId} 请求退出课程包 ${id}`);
        try {
            await this.coursePackService.quitCoursePack(user.userId, id);
            return {
                success: true,
                message: '已成功退出课程包'
            };
        }
        catch (error) {
            this.logger.error(`用户 ${user.userId} 退出课程包 ${id} 失败:`, error);
            throw error;
        }
    }
};
exports.CoursePackController = CoursePackController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "getPublicPackages", null);
__decorate([
    (0, common_1.Post)("create"),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "create", null);
__decorate([
    (0, common_1.Get)("mall"),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "getMallCoursePacks", null);
__decorate([
    (0, common_1.Get)("joined"),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "findJoined", null);
__decorate([
    (0, guards_1.UncheckAuth)(),
    (0, common_1.Get)(),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)('test-data'),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "createTestData", null);
__decorate([
    (0, common_1.Post)(":id/join"),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "join", null);
__decorate([
    (0, guards_1.UncheckAuth)(),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    (0, common_1.Get)(":coursePackId"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("coursePackId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "findOne", null);
__decorate([
    (0, guards_1.UncheckAuth)(),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    (0, common_1.Get)(":coursePackId/courses/:courseId"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("coursePackId")),
    __param(2, (0, common_1.Param)("courseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "findCourse", null);
__decorate([
    (0, guards_1.UncheckAuth)(),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    (0, common_1.Get)(":coursePackId/courses/:courseId/next"),
    __param(0, (0, common_1.Param)("coursePackId")),
    __param(1, (0, common_1.Param)("courseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], CoursePackController.prototype, "findNextCourse", null);
__decorate([
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    (0, common_1.Post)(":coursePackId/courses/:courseId/complete"),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("coursePackId")),
    __param(2, (0, common_1.Param)("courseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", void 0)
], CoursePackController.prototype, "CompleteCourse", null);
__decorate([
    (0, common_1.Delete)(":id/quit"),
    (0, common_1.UseGuards)(guards_1.AuthGuard),
    __param(0, (0, user_decorators_1.User)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CoursePackController.prototype, "quit", null);
exports.CoursePackController = CoursePackController = CoursePackController_1 = __decorate([
    (0, common_1.Controller)(["course-pack", "packages"]),
    __metadata("design:paramtypes", [course_pack_service_1.CoursePackService])
], CoursePackController);
//# sourceMappingURL=course-pack.controller.js.map