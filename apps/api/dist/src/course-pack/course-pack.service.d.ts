import { CourseHistoryService } from "../course-history/course-history.service";
import { CourseService } from "../course/course.service";
import { MembershipService } from "../membership/membership.service";
import { PrismaService } from '../prisma/prisma.service';
import { CoursePack, Course, Prisma, ShareLevel } from '@prisma/client';
import { PublicCoursePackResponse } from './types';
interface CourseWithCompletionCount extends Course {
    completionCount: number;
}
type CoursePackWithDetails = Prisma.CoursePackGetPayload<{
    include: {
        courses: true;
        progresses: true;
    };
}>;
export declare class CoursePackService {
    private readonly prisma;
    private readonly courseService;
    private readonly courseHistoryService;
    private readonly membershipService;
    private readonly logger;
    constructor(prisma: PrismaService, courseService: CourseService, courseHistoryService: CourseHistoryService, membershipService: MembershipService);
    findAll(userId?: string): Promise<CoursePack[]>;
    findJoinedCoursePacks(userId: string): Promise<{
        id: string;
        title: string;
        description: string;
        isFree: boolean;
        cover: string;
        courses: {
            id: string;
            title: string;
            description: string;
            order: number;
            coursePackId: string;
            completionCount: number;
            createdAt: Date;
            updatedAt: Date;
        }[];
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    findFounderOnly(): Promise<CoursePack[]>;
    findAllForUser(userId: string): Promise<CoursePack[]>;
    findAllPublicCoursePacks(): Promise<CoursePack[]>;
    findOne(coursePackId: string): Promise<CoursePack>;
    findOneWithCourses(userId: string | undefined, coursePackId: string): Promise<CoursePackWithDetails>;
    findCoursePackWithCourses(id: string, userId?: string): Promise<CoursePackWithDetails>;
    private addCompletionCountsToCourses;
    findCourse(userId: string | undefined, coursePackId: string, courseId: string): Promise<Course | CourseWithCompletionCount>;
    findNextCourse(coursePackId: string, courseId: string): Promise<Course | null>;
    completeCourse(userId: string, coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }>;
    findCoursesByPackId(packId: string): Promise<any[]>;
    create(data: {
        title: string;
        description?: string;
        cover?: string;
        isFree?: boolean;
        shareLevel?: ShareLevel;
        creatorId: string;
    }): Promise<CoursePack>;
    joinCoursePack(userId: string, coursePackId: string): Promise<CoursePack>;
    quitCoursePack(userId: string, coursePackId: string): Promise<void>;
    findPublicCoursePacks(userId?: string): Promise<PublicCoursePackResponse[]>;
}
export {};
