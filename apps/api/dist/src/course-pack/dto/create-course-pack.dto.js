"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCoursePackDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCoursePackDto {
}
exports.CreateCoursePackDto = CreateCoursePackDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "英语入门课程",
        description: "课程包标题",
    }),
    (0, class_validator_1.IsNotEmpty)({ message: "标题不能为空" }),
    (0, class_validator_1.IsString)({ message: "标题必须是字符串" }),
    __metadata("design:type", String)
], CreateCoursePackDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "这是一个适合初学者的英语课程包",
        description: "课程包描述",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "描述必须是字符串" }),
    __metadata("design:type", String)
], CreateCoursePackDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: "课程包排序",
    }),
    (0, class_validator_1.IsInt)({ message: "排序必须是整数" }),
    (0, class_validator_1.Min)(0, { message: "排序不能小于0" }),
    __metadata("design:type", Number)
], CreateCoursePackDto.prototype, "order", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "https://example.com/cover.jpg",
        description: "课程包封面图片URL",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "封面必须是字符串" }),
    __metadata("design:type", String)
], CreateCoursePackDto.prototype, "cover", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "user-123",
        description: "创建者ID",
    }),
    (0, class_validator_1.IsNotEmpty)({ message: "创建者ID不能为空" }),
    (0, class_validator_1.IsString)({ message: "创建者ID必须是字符串" }),
    __metadata("design:type", String)
], CreateCoursePackDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: "是否免费",
    }),
    (0, class_validator_1.IsBoolean)({ message: "isFree必须是布尔值" }),
    __metadata("design:type", Boolean)
], CreateCoursePackDto.prototype, "isFree", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: "课程难度等级 (1-5)",
    }),
    (0, class_validator_1.IsInt)({ message: "难度等级必须是整数" }),
    (0, class_validator_1.Min)(1, { message: "难度等级最小为1" }),
    (0, class_validator_1.Max)(5, { message: "难度等级最大为5" }),
    __metadata("design:type", Number)
], CreateCoursePackDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "private",
        description: "分享级别 (private/public/founder_only)",
    }),
    (0, class_validator_1.IsString)({ message: "分享级别必须是字符串" }),
    (0, class_validator_1.IsNotEmpty)({ message: "分享级别不能为空" }),
    __metadata("design:type", String)
], CreateCoursePackDto.prototype, "shareLevel", void 0);
//# sourceMappingURL=create-course-pack.dto.js.map