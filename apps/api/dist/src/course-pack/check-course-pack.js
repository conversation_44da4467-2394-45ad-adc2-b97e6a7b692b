"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
async function checkCoursePack() {
    const prisma = new client_1.PrismaClient();
    try {
        const coursePack = await prisma.coursePack.findUnique({
            where: {
                id: '2cd587be-9cad-44be-914a-64dddc77ba35'
            }
        });
        console.log('查询结果:', coursePack ? coursePack : '课程包不存在');
    }
    catch (error) {
        console.error('查询出错:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
checkCoursePack();
//# sourceMappingURL=check-course-pack.js.map