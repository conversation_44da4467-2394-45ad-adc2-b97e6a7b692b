import { UserEntity } from "../user/user.decorators";
import { CoursePackService } from "./course-pack.service";
import { CoursePack, Course, ShareLevel } from "@prisma/client";
import { PublicCoursePackResponse } from "./types";
import { CoursePackWithDetails } from "./types";
export declare class CoursePackController {
    private readonly coursePackService;
    private readonly logger;
    constructor(coursePackService: CoursePackService);
    getPublicPackages(): Promise<PublicCoursePackResponse[]>;
    create(user: UserEntity, data: {
        title: string;
        description?: string;
        cover?: string;
        isFree?: boolean;
        shareLevel?: ShareLevel;
    }): Promise<CoursePack>;
    getMallCoursePacks(user: UserEntity): Promise<PublicCoursePackResponse[]>;
    findJoined(user: UserEntity): Promise<{
        id: string;
        title: string;
        description: string;
        isFree: boolean;
        cover: string;
        courses: {
            id: string;
            title: string;
            description: string;
            order: number;
            coursePackId: string;
            completionCount: number;
            createdAt: string;
            updatedAt: string;
        }[];
        createdAt: string;
        updatedAt: string;
    }[]>;
    findAll(user: UserEntity): Promise<CoursePack[]>;
    createTestData(user: UserEntity): Promise<{
        message: string;
        data: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            order: number;
            title: string;
            cover: string | null;
            creatorId: string;
            isFree: boolean;
            difficulty: number;
            shareLevel: import(".prisma/client").$Enums.ShareLevel;
        }[];
    }>;
    join(user: UserEntity, id: string): Promise<CoursePack>;
    findOne(user: UserEntity, coursePackId: string): Promise<CoursePackWithDetails>;
    findCourse(user: UserEntity, coursePackId: string, courseId: string): Promise<Course>;
    findNextCourse(coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        coursePackId: string;
        description: string | null;
        order: number;
        title: string;
        cover: string | null;
    }>;
    CompleteCourse(user: UserEntity, coursePackId: string, courseId: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        courseId: string;
        coursePackId: string;
        completionCount: number;
    }>;
    quit(user: UserEntity, id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
