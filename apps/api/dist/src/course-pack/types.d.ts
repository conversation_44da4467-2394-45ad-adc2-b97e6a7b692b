import { CoursePack, Course, UserCourseProgress } from '@prisma/client';
export interface PublicCoursePackResponse {
    id: string;
    title: string;
    description: string | null;
    cover: string | null;
    isFree: boolean;
    creatorInfo: {
        id: string;
        name: string | null;
        avatar: string | null;
    };
    courseCount: number;
    userCount: number;
    hasJoined: boolean;
    createdAt: string;
    updatedAt: string;
}
export type CoursePackWithDetails = CoursePack & {
    courses: Course[];
    progresses?: UserCourseProgress[];
};
