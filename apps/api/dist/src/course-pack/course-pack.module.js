"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoursePackModule = void 0;
const common_1 = require("@nestjs/common");
const auth_common_module_1 = require("../auth/auth.common.module");
const course_history_module_1 = require("../course-history/course-history.module");
const course_module_1 = require("../course/course.module");
const membership_module_1 = require("../membership/membership.module");
const prisma_module_1 = require("../prisma/prisma.module");
const course_pack_controller_1 = require("./course-pack.controller");
const course_pack_service_1 = require("./course-pack.service");
let CoursePackModule = class CoursePackModule {
};
exports.CoursePackModule = CoursePackModule;
exports.CoursePackModule = CoursePackModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_common_module_1.AuthCommonModule,
            course_module_1.CourseModule,
            membership_module_1.MembershipModule,
            course_history_module_1.CourseHistoryModule,
            prisma_module_1.PrismaModule
        ],
        providers: [course_pack_service_1.CoursePackService],
        controllers: [course_pack_controller_1.CoursePackController],
        exports: [course_pack_service_1.CoursePackService],
    })
], CoursePackModule);
//# sourceMappingURL=course-pack.module.js.map