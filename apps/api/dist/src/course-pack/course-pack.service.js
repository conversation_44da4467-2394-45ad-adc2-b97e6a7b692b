"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CoursePackService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoursePackService = void 0;
const common_1 = require("@nestjs/common");
const course_history_service_1 = require("../course-history/course-history.service");
const course_service_1 = require("../course/course.service");
const membership_service_1 = require("../membership/membership.service");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let CoursePackService = CoursePackService_1 = class CoursePackService {
    constructor(prisma, courseService, courseHistoryService, membershipService) {
        this.prisma = prisma;
        this.courseService = courseService;
        this.courseHistoryService = courseHistoryService;
        this.membershipService = membershipService;
        this.logger = new common_1.Logger(CoursePackService_1.name);
    }
    async findAll(userId) {
        try {
            if (userId) {
                const isFounder = await this.membershipService.isFounderMembership(userId);
                return this.prisma.coursePack.findMany({
                    where: {
                        OR: [
                            { shareLevel: client_1.ShareLevel.PUBLIC },
                            { creatorId: userId },
                            ...(isFounder ? [{ shareLevel: client_1.ShareLevel.FOUNDER_ONLY }] : []),
                        ],
                    },
                    include: {
                        courses: true
                    },
                    orderBy: {
                        order: 'asc',
                    },
                });
            }
            else {
                return this.findAllPublicCoursePacks();
            }
        }
        catch (error) {
            console.error('获取课程包列表失败:', error);
            return [];
        }
    }
    async findJoinedCoursePacks(userId) {
        try {
            this.logger.log(`[CoursePackService] 开始查询用户(${userId})已加入的课程包...`);
            const coursePacks = await this.prisma.coursePack.findMany({
                where: {
                    progresses: {
                        some: {
                            userId: userId
                        }
                    }
                },
                include: {
                    courses: {
                        orderBy: {
                            order: 'asc'
                        }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            if (coursePacks.length === 0) {
                this.logger.warn(`[CoursePackService] 用户(${userId})暂无已加入的课程包`);
                return [];
            }
            this.logger.log(`[CoursePackService] 成功获取用户(${userId})的课程包:`, {
                数量: coursePacks.length,
                课程包列表: coursePacks.map(pack => ({
                    id: pack.id,
                    title: pack.title,
                    课程数量: pack.courses.length,
                    创建时间: pack.createdAt,
                    更新时间: pack.updatedAt
                }))
            });
            const coursePacksWithProgress = await Promise.all(coursePacks.map(async (pack) => {
                const coursesWithProgress = await this.addCompletionCountsToCourses(userId, pack.courses, pack.id);
                return {
                    id: pack.id,
                    title: pack.title,
                    description: pack.description,
                    isFree: pack.isFree,
                    cover: pack.cover,
                    courses: coursesWithProgress.map(course => ({
                        id: course.id,
                        title: course.title,
                        description: course.description,
                        order: course.order,
                        coursePackId: course.coursePackId,
                        completionCount: course.completionCount,
                        createdAt: course.createdAt,
                        updatedAt: course.updatedAt
                    })),
                    createdAt: pack.createdAt,
                    updatedAt: pack.updatedAt
                };
            }));
            return coursePacksWithProgress;
        }
        catch (error) {
            this.logger.error(`[CoursePackService] 获取用户(${userId})已加入的课程包失败:`, {
                错误类型: error.constructor.name,
                错误信息: error.message,
                堆栈: error.stack
            });
            throw error;
        }
    }
    async findFounderOnly() {
        return this.prisma.coursePack.findMany({
            where: {
                shareLevel: client_1.ShareLevel.FOUNDER_ONLY,
            },
        });
    }
    async findAllForUser(userId) {
        return this.prisma.coursePack.findMany({
            where: {
                OR: [
                    { shareLevel: client_1.ShareLevel.PUBLIC },
                    { shareLevel: client_1.ShareLevel.FOUNDER_ONLY },
                ],
            },
        });
    }
    async findAllPublicCoursePacks() {
        return this.prisma.coursePack.findMany({
            where: {
                shareLevel: client_1.ShareLevel.PUBLIC,
            },
            include: {
                courses: true
            }
        });
    }
    async findOne(coursePackId) {
        const result = await this.prisma.coursePack.findUnique({
            where: { id: coursePackId }
        });
        if (!result) {
            throw new common_1.NotFoundException(`CoursePack with ID ${coursePackId} not found`);
        }
        return result;
    }
    async findOneWithCourses(userId, coursePackId) {
        console.log(`[CoursePackService] findOneWithCourses 调用 - 用户ID: ${userId}, 课程包ID: ${coursePackId}`);
        const coursePackWithCourses = await this.findCoursePackWithCourses(coursePackId, userId);
        if (userId) {
            console.log(`[CoursePackService] 用户已认证，开始添加完成次数统计，课程数量: ${coursePackWithCourses.courses.length}`);
            coursePackWithCourses.courses = await this.addCompletionCountsToCourses(userId, coursePackWithCourses.courses, coursePackId);
            console.log(`[CoursePackService] 完成次数统计添加完成`);
        }
        else {
            console.log(`[CoursePackService] 用户未认证，跳过完成次数统计`);
        }
        return coursePackWithCourses;
    }
    async findCoursePackWithCourses(id, userId) {
        const coursePack = await this.prisma.coursePack.findUnique({
            where: { id },
            include: {
                courses: {
                    orderBy: {
                        order: 'asc'
                    }
                },
                progresses: true,
            },
        });
        if (!coursePack) {
            this.logger.error(`[CoursePackService] 未找到课程包:`, {
                课程包ID: id,
                用户ID: userId || '未认证'
            });
            throw new common_1.NotFoundException('Course pack not found');
        }
        if (!userId) {
            const isAccessible = coursePack.shareLevel === client_1.ShareLevel.PUBLIC;
            if (!isAccessible) {
                this.logger.warn(`[CoursePackService] 未认证用户尝试访问非公开课程包:`, {
                    课程包ID: id,
                    分享级别: coursePack.shareLevel,
                    是否免费: coursePack.isFree
                });
                throw new common_1.ForbiddenException(coursePack.shareLevel !== client_1.ShareLevel.PUBLIC
                    ? '此课程包不对外公开'
                    : '此课程包需要付费访问');
            }
        }
        this.logger.log(`[CoursePackService] 成功获取课程包详情:`, {
            课程包ID: id,
            标题: coursePack.title,
            课程数量: coursePack.courses.length,
            访问级别: coursePack.shareLevel,
            是否免费: coursePack.isFree,
            用户ID: userId || '未认证'
        });
        return coursePack;
    }
    async addCompletionCountsToCourses(userId, courses, coursePackId) {
        return await Promise.all(courses.map(async (course) => {
            const completionCount = await this.courseHistoryService.findCompletionCount(userId, coursePackId, course.id);
            return {
                ...course,
                completionCount,
            };
        }));
    }
    async findCourse(userId, coursePackId, courseId) {
        if (userId) {
            return await this.courseService.findWithUserProgress(coursePackId, courseId, userId);
        }
        else {
            return await this.courseService.find(coursePackId, courseId);
        }
    }
    async findNextCourse(coursePackId, courseId) {
        return await this.courseService.findNext(coursePackId, courseId);
    }
    async completeCourse(userId, coursePackId, courseId) {
        return await this.courseService.completeCourse(userId, coursePackId, courseId);
    }
    async findCoursesByPackId(packId) {
        const coursePack = await this.prisma.coursePack.findUnique({
            where: {
                id: packId
            },
            include: {
                courses: true
            }
        });
        return coursePack?.courses || [];
    }
    async create(data) {
        try {
            const maxOrder = await this.prisma.coursePack.findFirst({
                orderBy: {
                    order: 'desc'
                },
                select: {
                    order: true
                }
            });
            return await this.prisma.coursePack.create({
                data: {
                    ...data,
                    order: (maxOrder?.order || 0) + 1,
                }
            });
        }
        catch (error) {
            console.error('创建课程包失败:', error);
            throw error;
        }
    }
    async joinCoursePack(userId, coursePackId) {
        const coursePack = await this.prisma.coursePack.findUnique({
            where: { id: coursePackId }
        });
        if (!coursePack) {
            throw new common_1.NotFoundException('课程包不存在');
        }
        const existingProgress = await this.prisma.userCourseProgress.findFirst({
            where: {
                userId,
                coursePackId
            }
        });
        if (existingProgress) {
            throw new common_1.ForbiddenException('您已经加入过这个课程包了');
        }
        if (!coursePack.isFree) {
            const isFounder = await this.membershipService.isFounderMembership(userId);
            if (!isFounder) {
                throw new common_1.ForbiddenException('该课程包需要付费才能加入');
            }
        }
        const firstCourse = await this.prisma.course.findFirst({
            where: { coursePackId },
            orderBy: { order: 'asc' }
        });
        if (!firstCourse) {
            throw new common_1.NotFoundException('该课程包还没有任何课程');
        }
        await this.prisma.userCourseProgress.create({
            data: {
                userId,
                coursePackId,
                courseId: firstCourse.id,
                statementIndex: 0
            }
        });
        return coursePack;
    }
    async quitCoursePack(userId, coursePackId) {
        const coursePack = await this.prisma.coursePack.findUnique({
            where: { id: coursePackId }
        });
        if (!coursePack) {
            throw new common_1.NotFoundException('课程包不存在');
        }
        const existingProgress = await this.prisma.userCourseProgress.findFirst({
            where: {
                userId,
                coursePackId
            }
        });
        if (!existingProgress) {
            throw new common_1.ForbiddenException('您还未加入此课程包');
        }
        await this.prisma.userCourseProgress.deleteMany({
            where: {
                userId,
                coursePackId
            }
        });
        await this.prisma.courseHistory.deleteMany({
            where: {
                userId,
                coursePackId
            }
        });
    }
    async findPublicCoursePacks(userId) {
        try {
            const coursePacks = await this.prisma.coursePack.findMany({
                where: {
                    shareLevel: client_1.ShareLevel.PUBLIC
                },
                select: {
                    id: true,
                    title: true,
                    description: true,
                    cover: true,
                    isFree: true,
                    creatorId: true,
                    createdAt: true,
                    updatedAt: true,
                    courses: true,
                    progresses: {
                        select: {
                            userId: true
                        }
                    },
                    _count: {
                        select: {
                            progresses: true
                        }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            const creatorIds = [...new Set(coursePacks.map(pack => pack.creatorId))];
            const creators = await this.prisma.user.findMany({
                where: {
                    id: {
                        in: creatorIds
                    }
                },
                select: {
                    id: true,
                    username: true,
                    avatar: true
                }
            });
            const creatorMap = new Map(creators.map(creator => [creator.id, creator]));
            return coursePacks.map(pack => {
                const creator = creatorMap.get(pack.creatorId);
                const hasJoined = userId ? pack.progresses.some(progress => progress.userId === userId) : false;
                return {
                    id: pack.id,
                    title: pack.title,
                    description: pack.description,
                    cover: pack.cover,
                    isFree: pack.isFree,
                    creatorInfo: {
                        id: pack.creatorId,
                        name: creator?.username || null,
                        avatar: creator?.avatar || null
                    },
                    courseCount: pack.courses.length,
                    userCount: pack._count.progresses,
                    hasJoined: hasJoined,
                    createdAt: pack.createdAt.toISOString(),
                    updatedAt: pack.updatedAt.toISOString()
                };
            });
        }
        catch (error) {
            this.logger.error('获取公开课程包列表失败:', error);
            throw new Error('获取公开课程包列表失败');
        }
    }
};
exports.CoursePackService = CoursePackService;
exports.CoursePackService = CoursePackService = CoursePackService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        course_service_1.CourseService,
        course_history_service_1.CourseHistoryService,
        membership_service_1.MembershipService])
], CoursePackService);
//# sourceMappingURL=course-pack.service.js.map