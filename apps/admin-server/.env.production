# 服务器配置
PORT=5000
NODE_ENV=production

# 数据库配置
DATABASE_URL="postgresql://zerobase:your_password@localhost:5432/zerobase?schema=public"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# 日志级别
LOG_LEVEL=info

# 跨域配置
CORS_ORIGIN=https://www.zerobase.dpdns.org

# 安全配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000
HELMET_ENABLED=true

# Redis配置
REDIS_URL="redis://localhost:6379"

# 测试控制
# 生产环境禁用测试脚本
ENABLE_TEST_SCRIPTS=false