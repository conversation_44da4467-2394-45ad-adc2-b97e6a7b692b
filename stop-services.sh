#!/bin/bash

# ZeroBase 停止服务脚本

echo "停止所有Node.js应用服务..."

# 停止特定端口的服务
echo "停止端口3000的服务..."
fuser -k 3000/tcp 2>/dev/null || true

echo "停止端口3001的服务..."
fuser -k 3001/tcp 2>/dev/null || true

echo "停止端口3002的服务..."
fuser -k 3002/tcp 2>/dev/null || true

echo "停止端口5000的服务..."
fuser -k 5000/tcp 2>/dev/null || true

# 停止所有相关的Node.js进程
echo "停止所有应用相关的Node.js进程..."
pkill -f "node.*apps" 2>/dev/null || true
pkill -f "npm.*start" 2>/dev/null || true
pkill -f "npm.*preview" 2>/dev/null || true

echo "等待进程完全停止..."
sleep 5

echo "检查剩余进程..."
netstat -tlnp | grep -E ':(3000|3001|3002|5000)' || echo "所有服务已停止"

echo "服务停止完成！"
