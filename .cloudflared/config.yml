# Cloudflare Tunnel 配置
# tunnel: 隧道名称
# credentials-file: 凭证文件路径
# ingress: 路由规则配置

tunnel: zerobase
credentials-file: ~/.cloudflared/zerobase.json

ingress:
  # 主域名管理平台API路由 (管理平台API - 端口 5000) - 必须在/api/*之前
  - hostname: www.zerobase.dpdns.org
    path: /api/admin*
    service: http://localhost:5000

  # 主域名 API 路由 (用户端API - 端口 3002)
  - hostname: www.zerobase.dpdns.org
    path: /api/*
    service: http://localhost:3002

  # 主域名管理平台路由 (管理平台 - 端口 3001)
  - hostname: www.zerobase.dpdns.org
    path: /admin*
    service: http://localhost:3001

  # 主域名其他请求路由到前端 (前端 - 端口 3000)
  - hostname: www.zerobase.dpdns.org
    service: http://localhost:3000

  # 备用域名管理平台API路由 (管理平台API - 端口 5000) - 必须在/api/*之前
  - hostname: zerobase.dpdns.org
    path: /api/admin*
    service: http://localhost:5000

  # 备用域名 API 路由 (用户端API - 端口 3002)
  - hostname: zerobase.dpdns.org
    path: /api/*
    service: http://localhost:3002

  # 备用域名管理平台路由 (管理平台 - 端口 3001)
  - hostname: zerobase.dpdns.org
    path: /admin*
    service: http://localhost:3001

  # 备用域名其他请求路由到前端 (前端 - 端口 3000)
  - hostname: zerobase.dpdns.org
    service: http://localhost:3000

  # 默认 404 处理
  - service: http_status:404
