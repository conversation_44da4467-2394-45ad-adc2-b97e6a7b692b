#!/bin/bash

# ZeroBase 简化服务启动脚本
# 只启动基本的前端和API服务

echo "停止现有服务..."
pkill -f "node.*apps" 2>/dev/null || true

echo "等待进程完全停止..."
sleep 3

echo "启动数据库服务..."
sudo systemctl start postgresql redis 2>/dev/null || true

echo "等待数据库启动..."
sleep 5

echo "启动API服务 (端口3002)..."
cd /var/www/apps/api
NODE_ENV=production nohup npm run start > /var/log/api.log 2>&1 &
API_PID=$!

echo "等待API服务启动..."
sleep 10

echo "启动前端服务 (端口3000)..."
cd /var/www/apps/client
NODE_ENV=production nohup npm run preview > /var/log/frontend.log 2>&1 &
FRONTEND_PID=$!

echo "等待前端服务启动..."
sleep 10

echo "检查服务状态..."
netstat -tlnp | grep -E ':(3000|3002)'

echo "服务启动完成！"
echo "API服务PID: $API_PID"
echo "前端服务PID: $FRONTEND_PID"
echo ""
echo "访问地址："
echo "- 本地前端: http://localhost:3000"
echo "- 本地API: http://localhost:3002/api"
echo "- 隧道前端: https://www.zerobase.dpdns.org"
echo "- 隧道API: https://www.zerobase.dpdns.org/api"
echo ""
echo "日志文件："
echo "- API日志: /var/log/api.log"
echo "- 前端日志: /var/log/frontend.log"
