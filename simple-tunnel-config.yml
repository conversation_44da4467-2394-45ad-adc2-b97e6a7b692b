# Cloudflare Tunnel 简化配置 - 只保留基本功能
tunnel: zerobase
credentials-file: /root/.cloudflared/zerobase.json

ingress:
  # API路由 - 优先级最高
  - hostname: www.zerobase.dpdns.org
    path: /api/*
    service: http://localhost:3002
  
  - hostname: zerobase.dpdns.org
    path: /api/*
    service: http://localhost:3002

  # 前端路由 - 处理所有其他请求
  - hostname: www.zerobase.dpdns.org
    service: http://localhost:3000
  
  - hostname: zerobase.dpdns.org
    service: http://localhost:3000

  # 默认处理
  - service: http_status:404
